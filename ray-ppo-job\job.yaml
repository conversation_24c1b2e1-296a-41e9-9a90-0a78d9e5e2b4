apiVersion: ray.io/v1
kind: RayJob
metadata:
  name: ray-job
  namespace: rl-platform
spec:
  entrypoint: "python /root/ray-gym/train.py"
  rayClusterSpec:
    rayVersion: "2.7.0"
    headGroupSpec:
      rayStartParams:
        dashboard-host: "0.0.0.0"
      template:
        spec:
          containers:
            - name: ray-head
              image: core.**************.nip.io:30670/public/ray-gym-cartpole:latest
              imagePullPolicy: IfNotPresent
              ports:
                - containerPort: 6379
                  name: gcs-server
                - containerPort: 8265
                  name: dashboard
                - containerPort: 10001
                  name: client
              resources:
                limits:
                  cpu: "1"
                requests:
                  cpu: "200m"
              volumeMounts:
                - name: shared-volume
                  mountPath: /mnt/shared
          volumes:
            - name: shared-volume
              persistentVolumeClaim:
                claimName: common-pvc
    workerGroupSpecs:
      - replicas: 1
        minReplicas: 1
        maxReplicas: 5
        groupName: gpu-group
        rayStartParams: {}
        template:
          spec:
            containers:
              - name: ray-worker
                image: core.**************.nip.io:30670/public/ray-gym-cartpole:latest
                imagePullPolicy: IfNotPresent
                resources:
                  limits:
                    cpu: 4
                    memory: 4Gi
                    nvidia.com/gpu: 1
                  requests:
                    cpu: 4
                    memory: 4Gi
                    nvidia.com/gpu: 1
                volumeMounts:
                  - name: shared-volume
                    mountPath: /mnt/shared
            volumes:
              - name: shared-volume
                persistentVolumeClaim:
                  claimName: common-pvc
      - replicas: 10
        minReplicas: 1
        maxReplicas: 5
        groupName: cpu-group
        rayStartParams: {}
        template:
          spec:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: nvidia.com/gpu
                          operator: DoesNotExist
            containers:
              - name: ray-worker
                image: core.**************.nip.io:30670/public/ray-gym-cartpole:latest
                imagePullPolicy: IfNotPresent
                resources:
                  limits:
                    cpu: 1
                    memory: 2Gi
                  requests:
                    cpu: 1
                    memory: 2Gi
                volumeMounts:
                  - name: shared-volume
                    mountPath: /mnt/shared
            volumes:
              - name: shared-volume
                persistentVolumeClaim:
                  claimName: common-pvc
