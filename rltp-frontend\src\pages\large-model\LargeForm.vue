<!--
 * @Author: Szc
 * @Date: 2025-08-14 09:49:13
 * @LastEditors: Szc
 * @LastEditTime: 2025-08-19 08:49:40
 * @Description: 
-->
<template>
    <div class="main-Form">
        <div class="Top">
            <LargeTitleBtn ref="largeTitleBtnRef"></LargeTitleBtn>
            <q-btn class="doReturn" @click="returnToOverview">
                <img class="returnIcon" src="../../assets/images/icon_fh.png" alt="">
                <div class="labelColor">返回模型概览</div>
            </q-btn>
        </div>
        <component 
            :is="currentStepComponent" 
            @next-step="handleNextStep" 
            @prev-step="handlePrevStep"
        ></component>
    </div>
</template>

<script setup>
import { defineAsyncComponent, ref, computed, } from 'vue'
import { useRouter } from 'vue-router'
import LargeTitleBtn from './components/LargeTitleBtn.vue'

// 异步加载拆分为独立chunk
const stepComponents = {
  1: defineAsyncComponent(() => import('./LargeStepOne.vue')),
  2: defineAsyncComponent(() => import('./LargeStepTwo.vue')),
  3: defineAsyncComponent(() => import('./LargeStepThree.vue')),
  4: defineAsyncComponent(() => import('./LargeStepFour.vue')),
  5: defineAsyncComponent(() => import('./LargeStepFive.vue')),
  6: defineAsyncComponent(() => import('./LargeStepSix.vue')),
}

const router = useRouter()

// 大模型TitleBtn组件引用
const largeTitleBtnRef = ref(null)


const currentStep = computed(() => {
    return largeTitleBtnRef.value?.currentStep || 1
})


const currentStepComponent = computed(() => stepComponents[currentStep.value])

// 处理下一步
const handleNextStep = () => {
    if (largeTitleBtnRef.value) {
        largeTitleBtnRef.value.nextStep()
        console.log('大模型-当前步骤:', largeTitleBtnRef.value.currentStep)
        
        // 根据当前步骤执行相应逻辑
        const currentStep = largeTitleBtnRef.value.currentStep
        if (currentStep > 1) {
            console.log('执行大模型步骤', currentStep, '的相关逻辑')
        }
    }
}

// 处理上一步
const handlePrevStep = () => {
    if (largeTitleBtnRef.value) {
        largeTitleBtnRef.value.prevStep()
        console.log('大模型-当前步骤:', largeTitleBtnRef.value.currentStep)
    }
}

// 直接跳转到指定步骤的方法
const goToStep = (step) => {
    if (largeTitleBtnRef.value) {
        largeTitleBtnRef.value.goToStep(step)
        console.log('大模型-跳转到步骤:', step)
    }
}

// 返回概览页面
const returnToOverview = () => {
    router.push('/ai-model/largeHome') // 跳转到深度学习入口页
}
</script>

<style lang="scss" scoped>
.main-Form {
    background-color: #131520;
    height: calc(100vh - 1rem); // 减去顶部padding和margin
    display: flex;
    flex-direction: column;
}

.Top {
    margin-top: 1rem;
    margin-bottom:.375rem;
    position: relative;
    
    .doReturn{
        position: absolute;
        left: 0;
        top: 0;
        display: flex;
        
        .returnIcon{
            width: .375rem;
            height: .375rem;
            margin-right: .125rem;
        }
        
        .labelColor {
            color: #4ab4ff;
        }
    }
}

// 通用样式
.labelColor {
    color: #4ab4ff;
}
</style>