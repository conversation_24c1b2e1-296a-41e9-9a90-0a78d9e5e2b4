import torch
import torch.nn as nn
import torch.nn.functional as F

class PPOActorCritic(nn.Module):
    def __init__(self, state_dim, action_dim, hidden_dim):
        super(PPOActorCritic, self).__init__()
        # Actor network
        self.actor_fc1 = nn.Linear(state_dim, hidden_dim)
        self.actor_fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.actor_fc3 = nn.Linear(hidden_dim, action_dim)
        
        # Critic network
        self.critic_fc1 = nn.Linear(state_dim, hidden_dim)
        self.critic_fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.critic_fc3 = nn.Linear(hidden_dim, 1)

    def forward(self, state):
        # Actor forward
        actor_x = F.relu(self.actor_fc1(state))
        actor_x = F.relu(self.actor_fc2(actor_x))
        action_probs = F.softmax(self.actor_fc3(actor_x), dim=-1)
        
        # Critic forward
        critic_x = <PERSON>.relu(self.critic_fc1(state))
        critic_x = F.relu(self.critic_fc2(critic_x))
        state_value = self.critic_fc3(critic_x)

        return action_probs, state_value
