import time
import ray
from ray import tune
from ray.tune.registry import register_env
from ray.rllib.algorithms.ppo import PPOConfig
from ray.rllib.algorithms import Algorithm
from ray.rllib.callbacks.callbacks import RLlibCallback
from torch import Tensor
from envs.JSBSim.envs.multiplecombat_rllib_env import MultipleCombatEnv
import argparse
import os
import subprocess

# 华为NPU支持
try:
    import torch_npu
    NPU_AVAILABLE = True
    print("华为NPU支持已加载")
except ImportError:
    NPU_AVAILABLE = False
    print("华为NPU支持未找到，将使用CPU/GPU")

def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--checkpoint-path", type=str, default="",
                      help="检查点路径，如果不指定则使用随机动作")
    parser.add_argument("--num-env-runners", type=int, default=32,
                      help="环境运行器数量")
    parser.add_argument("--num-learners", type=int, default=1,
                      help="学习器数量")
    parser.add_argument("--num-gpus", type=int, default=0,
                      help="GPU数量")
    parser.add_argument("--num-npus", type=int, default=0,
                      help="华为NPU数量")
    parser.add_argument("--use-npu", action="store_true",
                      help="使用华为NPU进行训练")
    parser.add_argument("--local-dir", type=str, default="~/ray_results",
                      help="结果保存路径")
    parser.add_argument("--tensorboard-dir", type=str, default="~/tensorboard_logs",
                      help="TensorBoard日志路径")
    parser.add_argument("--env-name", type=str, default="3v1/ShootMissile/Hierarchy",
                      help="环境名称，例如：1v1/DodgeMissile/vsBaseline, 1v1/NoWeapon/vsBaseline, 1v1/ShootMissile/Selfplay")
    parser.add_argument("--training-iterations", type=int, default=2000,
                      help="训练迭代次数")
    parser.add_argument("--env-registry-name", type=str, default="JSBSim-Combat",
                      help="Ray注册的环境名称")
    parser.add_argument("--experiment-name", type=str, default="jsbsim_combat_vs_baseline",
                      help="实验名称，用于保存结果和日志")

    return parser.parse_args()

def env_creator(env_config):
    # 使用传入的环境名称
    env = MultipleCombatEnv(env_config["env_name"])
    # env.seed(5)
    return env

def get_config(args):
    # 确定使用的设备类型和数量
    if args.use_npu and NPU_AVAILABLE:
        # 使用华为NPU
        device_type = "npu"
        num_devices = args.num_npus if args.num_npus > 0 else 1
        print(f"使用华为NPU进行训练，NPU数量: {num_devices}")
    else:
        # 使用GPU
        device_type = "gpu" 
        num_devices = args.num_gpus
        print(f"使用GPU进行训练，GPU数量: {num_devices}")
    
    config = (
        PPOConfig()
        .environment(args.env_registry_name, env_config={"env_name": args.env_name})
        .framework("torch")
        .resources(
            num_gpus=num_devices if device_type == "gpu" else 0,
            # 对于NPU，我们通过自定义资源来分配
            _fake_gpus=num_devices if device_type == "npu" else 0,
        )
        .env_runners(
            num_env_runners=args.num_env_runners,
            rollout_fragment_length='auto',
            num_envs_per_env_runner=1,
            num_cpus_per_env_runner=1,
            # num_gpus_per_env_runner=0 if device_type == "npu" else 0,
        )
        .learners(
            num_learners=args.num_learners,
            num_cpus_per_learner=1,
            num_gpus_per_learner=num_devices/args.num_learners if device_type == "gpu" or device_type == "npu" else 0
        )
        .training(
            gamma=0.99,
            lr=5e-5,
            train_batch_size=16384,
            lambda_=0.95,
            clip_param=0.2,
            vf_clip_param=10.0,
            entropy_coeff=0.01,
            num_sgd_iter=10,
            minibatch_size=2048,
            model={
                # 中央评论家网络配置
                "fcnet_hiddens": [512, 512, 256],
                "fcnet_activation": "relu",
                "vf_share_layers": False,  # 分离价值网络和策略网络
                "use_lstm": True,
                "lstm_cell_size": 256,
                "max_seq_len": 32,
            },
        )
        .multi_agent(
            policies=["red_policy", "blue_policy"],
            policy_mapping_fn=lambda agent_id, *args, **kwargs: (
                "red_policy" if agent_id.startswith("A") else "blue_policy"
            ),
            # policies_to_train=["red_policy"],  # 只训练红方策略
        )
        .evaluation(
            evaluation_interval=10,
            evaluation_duration=20,
            evaluation_num_workers=10,
            evaluation_config={
                "render_mode": "txt",
                "explore": False,
                "env_config": {
                    "env_name": args.env_name,
                },
            },
        )
    )
    
    # 华为NPU特定配置
    if args.use_npu and NPU_AVAILABLE:
        # 添加NPU特定的训练配置
        config = config.training(
            # NPU优化配置
            optimizer={
                "type": "Adam",
                "eps": 1e-7,  # NPU对数值稳定性要求更高
            }
        )
        
        # 为NPU设置环境变量（如果需要）
        os.environ.setdefault("ASCEND_RT_VISIBLE_DEVICES", "0")
        
    # 清空探索配置
    config.exploration_config = {}
    return config

if __name__ == "__main__":
    args = parse_args()
    
    timestamp = time.strftime("%Y%m%d-%H%M%S")
    experiment_name = f"{args.experiment_name}_{timestamp}"
    
    # 检查NPU可用性
    if args.use_npu and not NPU_AVAILABLE:
        print("警告：要求使用NPU但torch_npu未安装，将回退到GPU/CPU训练")
        args.use_npu = False
    
    # 启动tensorboard
    subprocess.Popen(['tensorboard --logdir ~/ray_results/' + experiment_name + ' --bind_all'], shell=True)
    
    # 注册环境
    register_env(args.env_registry_name, env_creator)  # 使用指定的环境注册名称
    
    # 初始化Ray - 支持NPU资源
    if args.use_npu and NPU_AVAILABLE:
        # 为NPU训练初始化Ray
        ray.init(
            resources={"npu": args.num_npus if args.num_npus > 0 else 1}
        )
    else:
        ray.init()
    
    # 获取配置
    config = get_config(args)
    
    # 运行训练
    tune.run(
        "PPO",
        config=config,
        # resume="Local",
        # restore=args.checkpoint_path if args.checkpoint_path else None,
        stop={
            "training_iteration": args.training_iterations,
            "episode_reward_mean": 1600,
        },
        checkpoint_freq=50,
        checkpoint_at_end=True,
        # local_dir=os.path.expanduser(args.local_dir),
        name=experiment_name,
        verbose=2,
    )
    
    # jsbsim_combat_vs_baseline_20250509-143640
    
    
