source /opt/conda/bin/activate
conda activate ray

python ray_eval_jsbsim3v1_record.py \
    --checkpoint-path /home/<USER>/xiangshuai/projects/xs/ray-ppo-job/ray_results/jsbsim-3v1-bsl-hierarchy_20250628-014732/PPO_JSBSim-Combat_ce7ec_00000_0_2025-06-28_01-47-34/checkpoint_000010 \
    --env-name 3v1/ShootMissile/HierarchyVsBaseline --experiment-name jsbsim-3v1-bsl-hierarchy --output-dir /home/<USER>/xiangshuai/projects/xs/ray-ppo-job/eval_results \
    --num-gpus 0 > eval_3v1_hierarchy.log 2>&1
