<!--
 * @Author: Szc
 * @Date: 2025-08-06 14:37:36
 * @LastEditors: Szc
 * @LastEditTime: 2025-08-18 16:06:10
 * @Description: 
-->
<template>
  <WorkflowStepWrapper
    :step-number="2"
    :training-type="TRAINING_TYPES.REINFORCEMENT_LEARNING"
    :need-save="true"
    @step-data-changed="onStepDataChanged"
    @step-completed="onStepCompleted"
  >
    <template #default="{ loading, onNextStep }">
      <div class="content">
        <div class="top">
            <div class="left">
                <div class="header-row">
                    <div class="title-section">
                        <img class="samllIcon" src="../../assets/images/icon_dz.png" alt="">
                        <div class="labelColor title">请设置模型参数</div>
                    </div>

                    <!-- 参数说明界面 -->
                    <!-- <div class="param-info">
                        <div class="info-text">参数说明详见</div>
                        <div class="tech-doc">
                            <img src="../../assets/images/icon_jswd.png" alt="" class="doc-icon">
                            <span>技术文档</span>
                        </div>
                    </div> -->
                </div>

                <!-- 模型参数设置 -->
                <div class="param-settings">
                    <div class="param-item">
                        <div class="param-row">
                            <div class="param-label">
                                <span class="required">*</span>
                                <span>轮次:</span>
                            </div>
                            <q-input class="param-input" v-model="params.epochs" outlined dense />
                        </div>
                        <div class="param-desc">训练轮次越大，耗时越久，最终精度通常越高</div>
                    </div>

                    <div class="param-item">
                        <div class="param-row">
                            <div class="param-label">
                                <span class="required">*</span>
                                <span>批大小:</span>
                            </div>
                            <q-input class="param-input" v-model="params.batchSize" outlined dense />
                        </div>
                        <div class="param-desc">单个 Batch Size，值越大，显存占用越高</div>
                    </div>

                    <!-- 高级配置 -->
                    <div class="advanced-config">
                        <div class="config-header" @click="toggleAdvanced">
                            <span>高级配置</span>
                            <q-icon :name="showAdvanced ? 'expand_less' : 'expand_more'" />
                        </div>

                        <div v-if="showAdvanced" class="config-content">
                            <div class="param-item">
                                <div class="param-row">
                                    <div class="param-label">优化器类型:</div>
                                    <q-select class="param-select" v-model="params.optimizer"
                                        :options="optimizerOptions" outlined dense emit-value map-options />
                                </div>
                                <div class="param-desc">从训练中断保存的 checkpoint 继续训练</div>
                            </div>

                            <div class="param-item">
                                <div class="param-row">
                                    <div class="param-label">学习率:</div>
                                    <q-input class="param-input" v-model="params.learningRate" outlined dense />
                                </div>
                                <div class="param-desc">从预训练的权重开始训练，提高训练效率</div>
                            </div>

                            <div class="param-item">
                                <div class="param-row">
                                    <div class="param-label">热启动步数:</div>
                                    <q-input class="param-input" v-model="params.warmupSteps" outlined dense />
                                </div>
                                <div class="param-desc">在训练初期缓慢增长小学习率到最初训练的 step 数</div>
                            </div>

                            <div class="param-item">
                                <div class="param-row">
                                    <div class="param-label">日志打印间隔:</div>
                                    <q-input class="param-input" v-model="params.logInterval" outlined dense />
                                </div>
                                <div class="param-desc">每隔多少个 step 打印一次 log 信息</div>
                            </div>

                            <div class="param-item">
                                <div class="param-row">
                                    <div class="param-label">评估、保存间隔:</div>
                                    <q-input class="param-input" v-model="params.evalInterval" outlined dense />
                                </div>
                                <div class="param-desc">每隔多少个 epoch 进行一次模型评估保存评估</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="right">
                <div class="one">
                    <img class="samllIcon" src="../../assets/images/icon_dz.png" alt="">
                    <div class="labelColor title">请设置计算资源配置</div>
                </div>

                <!-- 计算资源配置 -->
                <div class="resource-config">
                    <div class="resource-item">
                        <div class="resource-row">
                            <div class="resource-label">
                                <span class="required">*</span>
                                <span>NPU 数量:</span>
                            </div>
                            <q-input class="resource-input" v-model="resources.npuCount" outlined dense />
                        </div>
                        <div class="resource-desc">类似显卡数量，根据实际情况进行调整与</div>
                    </div>

                    <div class="resource-item">
                        <div class="resource-row">
                            <div class="resource-label">
                                <span class="required">*</span>
                                <span>CPU 数量:</span>
                            </div>
                            <q-input class="resource-input" v-model="resources.cpuCount" outlined dense />
                        </div>
                        <div class="resource-desc">学习率等IV参考 Batch Size 进行同比例的调整</div>
                    </div>
                </div>

                <!-- 下一步按钮 -->
                <div class="bottom">
                    <div class="next">
                        <q-btn class="prevBtn roundBox" color="grey-7" label="上一步" @click="prevStep" />
                        <q-btn
                          class="nextBtn roundBox"
                          color="primary"
                          label="下一步"
                          @click="handleNextStep(onNextStep)"
                          :loading="loading"
                        />
                    </div>
                </div>
            </div>
        </div>


      </div>
    </template>
  </WorkflowStepWrapper>
</template>

<script setup>
import { ref, defineEmits, onMounted } from 'vue'
import { usereinforcementLearingStore } from "../../stores/reinforcementLearingStore";
import WorkflowStepWrapper from '../../components/WorkflowStepWrapper.vue'
import { TRAINING_TYPES } from '../../constants/trainingTypes'

// 定义事件
const emit = defineEmits(['next-step', 'prev-step', 'step-data-changed', 'step-completed'])


const reinforcementLearingStore = usereinforcementLearingStore();

// 高级配置展开状态
const showAdvanced = ref(true)

// 模型参数
const params = ref({
    epochs: '5',
    batchSize: '5',
    optimizer: 'Adam',
    learningRate: '1e-5',
    warmupSteps: '1000',
    logInterval: '100',
    evalInterval: '10'
})

// 计算资源
const resources = ref({
    npuCount: '1',
    cpuCount: '16'
})

// 优化器选项
const optimizerOptions = [
    { label: 'Adam', value: 'Adam' },
    { label: 'SGD', value: 'SGD' },
    { label: 'AdamW', value: 'AdamW' }
]

// 组件挂载时从store加载数据
onMounted(() => {
    // 从store加载数据
    if (reinforcementLearingStore.stepTwoData) {
        params.value = { ...reinforcementLearingStore.stepTwoData.params }
        resources.value = { ...reinforcementLearingStore.stepTwoData.resources }
    }
})

// 切换高级配置显示
function toggleAdvanced() {
    showAdvanced.value = !showAdvanced.value
}

// 处理下一步按钮点击 - 使用新的工作流逻辑
async function handleNextStep(onNextStep) {
  try {
    // 准备步骤数据
    const stepData = {
      params: { ...params.value },
      resources: { ...resources.value },
      step_completed: true,
      completed_at: new Date().toISOString()
    }

    // 保存数据到旧的store (保持兼容性)
    reinforcementLearingStore.updateStepTwoData(stepData);
    console.log("stepTwoData", reinforcementLearingStore.stepTwoData);

    // 调用工作流的下一步方法
    await onNextStep(stepData)

  } catch (error) {
    console.error('保存步骤数据失败:', error)
  }
}

// 旧的下一步按钮点击事件 (保持兼容性)
function nextStep() {
    // 保存数据到store
    reinforcementLearingStore.updateStepTwoData({
        params: { ...params.value },
        resources: { ...resources.value }
    })

    // 触发下一步事件
    emit('next-step')
}

// 上一步按钮点击事件
function prevStep() {
    emit('prev-step')
}

// 工作流包装器事件处理
const onStepDataChanged = (data) => {
  console.log('步骤数据变化:', data)
  emit('step-data-changed', data)
}

const onStepCompleted = (data) => {
  console.log('步骤完成:', data)
  emit('step-completed', data)
}
</script>

<style lang="scss" scoped>
.content {
    display: flex;
    flex-direction: column;
    // height: 100%; /* 占满父容器高度 */
    flex: 1; /* 在父容器中伸缩 */
    // box-sizing: border-box; /* 包含内边距和边框 */


    .top {
        display: flex;
        margin-bottom: .125rem;
        flex: 1; /* 占满剩余空间 */
        min-height: 9.5rem; /* 最小高度 */

        .left,
        .right {
            width: 50%;
            height: inherit;
            border: .025rem solid #707070;
            background-color: #181a24;
            background-image:
                repeating-linear-gradient(130deg,
                    rgba(255, 255, 255, 0.05) 0px,
                    rgba(255, 255, 255, 0.01) 4px,
                    transparent 1px,
                    transparent 15px);
            padding: .3375rem;
            display: flex;
            flex-direction: column;


            .one {
                display: flex;
                align-items: center;
                font-size: .225rem;
                margin-bottom: .25rem;

                .samllIcon {
                    width: .2rem;
                    height: .2rem;
                    margin-right: .1875rem;
                }

                .title {
                    color: #4ab4ff;
                }
            }
        }

        .right {
            position: relative;

            &::before {
                position: absolute;
                z-index: 10000;
                content: '';
                left: -0.1rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198);
                border-radius: .125rem;
            }

            &::after {
                position: absolute;
                content: '';
                left: -0.075rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198, .5)
            }

            .one {
                display: flex;
                align-items: center;
                font-size: .225rem;
                margin-bottom: .25rem;

                .samllIcon {
                    width: .2rem;
                    height: .2rem;
                    margin-right: .1875rem;
                }

                .title {
                    color: #4ab4ff;
                }
            }
        }

        .left {
            margin-right: .125rem;
            position: relative;
            &::before {
                position: absolute;
                z-index: 10000;
                content: '';
                left: -0.1rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198);
                border-radius: .125rem;
            }

            &::after {
                position: absolute;
                content: '';
                left: -0.075rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198, .5)
            }
        }
    }

    // 头部行布局 - 标题与参数说明在一行
    .header-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: .375rem;

        .title-section {
            display: flex;
            align-items: center;

            .samllIcon {
                width: .2rem;
                height: .2rem;
                margin-right: .1875rem;
            }

            .title {
                color: #4ab4ff;
                font-size: .225rem;
            }
        }

        .param-info {
            display: flex;
            align-items: center;
            gap: .25rem;

            .info-text {
                color: white;
                font-size: .2rem;
            }

            .tech-doc {
                display: flex;
                align-items: center;
                color: #63d4ff;
                font-size: .175rem;
                cursor: pointer;

                .doc-icon {
                    width: .15rem;
                    height: .15rem;
                    margin-right: .0625rem;
                }
            }
        }
    }

    // 参数设置
    .param-settings {
        overflow-y: auto;
        .param-item {
            margin-bottom: .3rem;
            padding-left: .125rem;

            .param-row {
                display: flex;
                align-items: center;
                gap: .25rem;
                margin-bottom: .1rem;

                .param-label {
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    color: white;
                    font-size: .2rem;
                    white-space: nowrap;
                    min-width: 1.5rem;

                    .required {
                        color: #ff4757;
                        margin-right: .0625rem;
                    }
                }

                .param-input {
                    width: 1.5rem;

                    :deep(.q-field__control-container) {
                        padding-top: 0 !important;
                    }
                }

                .param-select {
                    width: 2.5rem;

                    :deep(.q-field__control-container) {
                        padding-top: 0 !important;
                    }
                }
            }

            .param-desc {
                color: #999;
                font-size: .175rem;
                line-height: 1.2;
                margin-left: 1.75rem;
            }
        }
    }

    // 高级配置
    .advanced-config {
        margin-top: .5rem;
        padding-left: .125rem;

        .config-header {
            display: flex;
            align-items: center;
            color: #63d4ff;
            font-size: .2rem;
            cursor: pointer;
            margin-bottom: .25rem;

            span {
                margin-right: .125rem;
            }
        }

        .config-content {
            .param-item {
                margin-bottom: .3rem;
                padding-left: 0;

                .param-row {
                    display: flex;
                    align-items: center;
                    gap: .25rem;
                    margin-bottom: .1rem;

                    .param-label {
                        display: flex;
                        align-items: center;
                        justify-content: flex-end;
                        color: white;
                        font-size: .2rem;
                        white-space: nowrap;
                        min-width: 1.5rem;
                        line-height: 1.2;
                    }

                    .param-input,
                    .param-select {
                        width: 1.5rem;

                        :deep(.q-field__control-container) {
                            padding-top: 0 !important;
                        }
                    }

                    .param-select {
                        width: 2.5rem;
                    }
                }

                .param-desc {
                    color: #999;
                    font-size: .175rem;
                    line-height: 1.2;
                    margin-left: 1.75rem;
                }
            }
        }
    }

    // 计算资源配置
    .resource-config {
        .resource-item {
            margin-bottom: .3rem;
            padding-left: .125rem;

            .resource-row {
                display: flex;
                align-items: center;
                gap: .25rem;
                margin-bottom: .1rem;

                .resource-label {
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    color: white;
                    font-size: .2rem;
                    white-space: nowrap;
                    min-width: 1.5rem;

                    .required {
                        color: #ff4757;
                        margin-right: .0625rem;
                    }
                }

                .resource-input {
                    width: 1.5rem;

                    :deep(.q-field__control-container) {
                        padding-top: 0 !important;
                    }
                }
            }

            .resource-desc {
                color: #999;
                font-size: .175rem;
                line-height: 1.2;
                margin-left: 1.75rem;
            }
        }
    }

    // 底部按钮
    .bottom {
        height: .8rem; // 固定高度
        display: flex;
        align-items: center;
        flex-shrink: 0; // 不允许缩小
        margin-top: auto;

        .next {
            display: flex;
            justify-content: space-between;
            width: 100%;
            padding: 0 .25rem;
            gap: .25rem;

            .prevBtn {
                margin-right: auto;
            }

            .nextBtn {
                margin-left: auto;
            }
        }
    }
}

// 通用样式
.labelColor {
    color: #4ab4ff;
}
</style>