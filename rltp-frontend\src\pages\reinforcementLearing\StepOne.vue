<template>
  <div class="scenario-container">
    <div class="left-panel">
      <div class="available-scenarios">
        <div class="onetop">
          <img src="../../assets/images/icon_dz.png" />
          <h2 class="available-title">可用想定（5）</h2>
        </div>
        <div class="container">
          <div
            class="scenario-card"
            v-for="(scenario, index) in scenarios"
            :key="index"
            :class="{ selected: selectedScenarioIndex === index }"
            @click="selectScenario(index)"
          >
            <div class="corner-top-left"></div>
            <div class="corner-bottom-right"></div>
            <div class="corner-bottom-left"></div>
            <div class="selected-icon" v-if="selectedScenarioIndex === index">
              <img src="../../assets/reinforcementImages/btn_xz.png" />
            </div>
            <h4 class="scenario-title">{{ scenario.title }}</h4>
            <div class="scenario-items">
              <div class="basic-info">
                <span class="info-left"> {{ scenario.status }}</span>
                <span class="info-right"> {{ scenario.environmenttype }}</span>
              </div>
              <span class="info-text">{{ scenario.info }}</span>
              <div class="detail-info">
                <div class="top">
                  <div class="top-item">
                    <img src="../../assets/reinforcementImages/kp_icon_dz.png" />
                    <p :title="scenario.detailinfo.detaillocation">
                      {{ scenario.detailinfo.detaillocation }}
                    </p>
                  </div>
                  <div class="top-item">
                    <img src="../../assets/reinforcementImages/kp_icon_sj.png" />
                    <p :title="scenario.detailinfo.detailtime">
                      {{ scenario.detailinfo.detailtime }}
                    </p>
                  </div>
                </div>
                <div class="bottom">
                  <div class="bottom-item">
                    <img src="../../assets/reinforcementImages/kp_icon_wf.png" />
                    <p :title="scenario.detailinfo.detailour">
                      {{ scenario.detailinfo.detailour }}
                    </p>
                  </div>
                  <div class="bottom-item">
                    <img src="../../assets/reinforcementImages/kp_icon_df.png" />
                    <p :title="scenario.detailinfo.detailenemy">
                      {{ scenario.detailinfo.detailenemy }}
                    </p>
                  </div>
                </div>
              </div>
              <div class="add-time">{{ scenario.addtime }}</div>
            </div>
          </div>
        </div>
        <div class="import-section" @click="importScenario">
          <div class="bts">
            <q-btn class="import-btn roundBox" color="primary" label="导入想定">
              <img
                src="../../assets/reinforcementImages/icon_drxd.png"
                class="btn-icon"
              />
            </q-btn>
          </div>
        </div>
      </div>
    </div>

    <div class="right-panel">
      <div class="scenario-detail">
        <div class="detail-top">
          <div class="detail-left">
            <div class="top-title">
              <div class="title-left">
                <div class="onetop">
                  <img src="../../assets/images/icon_dz.png" />
                  <h2>想定详情</h2>
                </div>

                <div class="center-content">
                  <span class="one-content">{{ scenariodetail.type }}</span>
                  <span class="two-content">{{ scenariodetail.info }}</span>
                </div>
              </div>
              <div class="right-content">
                <span>{{ scenariodetail.status }}</span>
              </div>
            </div>
            <div class="bottom-content">
              <div
                v-for="(item, index) in scenariodetail.detailinfo"
                :key="index"
                class="info-item"
              >
                <img :src="getImageByType(item.type)" />
                <div class="info-text">
                  <div class="row q-col-gutter-md items-center">
                    <q-select
                      v-model="item.info"
                      :options="getOptionsByType(item.type)"
                      emit-value
                      map-options
                      class="info-main"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="detail-right">
            <img src="../../assets/images/ai-pilot.png" class="detail-image" />
          </div>
        </div>

        <div class="detail-bottom">
          <div class="stats-section">
            <h5>场景统计</h5>
            <div class="stats-info">
              <div class="info-item">
                <span>我方总兵力:</span>
                <p>{{ detailedInfo.Ourforces }}</p>
              </div>
              <div class="info-item">
                <span>敌方总兵力:</span>
                <p>{{ detailedInfo.Enemyforces }}</p>
              </div>
              <div class="info-item">
                <span>兵力比:</span>
                <p>{{ detailedInfo.TroopRatio }}</p>
              </div>
            </div>
          </div>
          <div class="create-info">
            <h5>创建信息</h5>
            <div class="stats-info">
              <div class="info-item">
                <span>创建时间:</span>
                <p>{{ detailedInfo.AddTime }}</p>
              </div>
              <div class="info-item">
                <span>场景ID:</span>
                <p>{{ detailedInfo.SceneID }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="bottom-sections">
        <div class="section force-comparison">
          <div class="onetop">
            <img src="../../assets/images/icon_dz.png" />
            <h2>兵力对比</h2>
          </div>
          <div class="force-container">
            <div
              v-for="(force, index) in forceComparison"
              :key="index"
              class="force-info"
            >
              <div class="onetop">
                <img :src="getIconByType(force.type)" />
                <h5
                  :style="{
                    color:
                      force.type === 1 ? 'rgba(40, 124, 208, 1)' : 'rgba(232, 38, 38, 1)',
                  }"
                >
                  {{ getTitleByType(force.type) }}
                </h5>
              </div>

              <!-- 类型0: 我方兵力 -->
              <div v-if="force.type === 0" class="force-detail">
                <!-- 第一行：标题和数量输入框 -->
                <div class="fleet-count-control">
                  <span>{{ force.name }}</span>
                  <div class="count-input">
                    <span class="count-title">数量</span>
                    <div class="row items-center customAdd count-btn">
                      <q-btn
                        flat
                        dense
                        icon="remove"
                        size="sm"
                        @click="decreaseCount(force, index)"
                      />
                      <div class="q-mx-md">{{ force.totalSum }}</div>
                      <q-btn
                        flat
                        dense
                        icon="add"
                        size="sm"
                        @click="increaseCount(force, index)"
                      />
                    </div>
                  </div>
                </div>

                <!-- 小卡片滑动区域 -->
                <div
                  class="card-slider"
                  @mousedown="startDrag"
                  @mousemove="drag"
                  @mouseup="endDrag"
                  @mouseleave="endDrag"
                  @touchstart="startDrag"
                  @touchmove.prevent="drag"
                  @touchend="endDrag"
                >
                  <div class="slider-container">
                    <div
                      v-for="(fleet, fleetIndex) in force.fleets"
                      :key="fleetIndex"
                      class="fleet-card"
                      :style="{ transform: `translateX(-${currentSlide * 100}%)` }"
                    >
                      <div class="card-header">
                        <span class="card-title">{{ fleet.name }}</span>
                        <img
                          src="../../assets/reinforcementImages/icon_save.png"
                          @click="saveCardData(force, fleet)"
                        />
                      </div>
                      <div class="card-info-grid">
                        <div
                          v-for="(item, itemIndex) in fleet.type"
                          :key="itemIndex"
                          class="equipment-item"
                        >
                          <span class="equipment-desc" :title="item.name">
                            {{
                              item.name.length > 4
                                ? item.name.slice(0, 4) + "..."
                                : item.name
                            }}
                          </span>
                          <div class="count-input">
                            <div class="row items-center customAdd">
                              <q-btn
                                flat
                                dense
                                icon="remove"
                                size="sm"
                                @click="
                                  decreaseItemCount(force, index, fleetIndex, itemIndex)
                                "
                              />
                              <div class="q-mx-md">{{ item.count }}</div>
                              <q-btn
                                flat
                                dense
                                icon="add"
                                size="sm"
                                @click="
                                  increaseItemCount(force, index, fleetIndex, itemIndex)
                                "
                              />
                            </div>
                          </div>
                        </div>
                        <div class="card-status">
                          <span class="status-desc">状态：</span>
                          <img
                            v-if="fleet.status === true"
                            src="../../assets/reinforcementImages/btn_1.png"
                            @click="toggleFleetStatus(fleet)"
                          />
                          <img
                            v-else
                            src="../../assets/reinforcementImages/btn_2.png"
                            @click="toggleFleetStatus(fleet)"
                          />
                        </div>
                      </div>

                      <!-- 滑动圆圈指示器 -->
                      <div class="slider-indicator">
                        <span
                          v-for="(fleet, idx) in force.fleets"
                          :key="idx"
                          :class="{ active: currentSlide === idx }"
                          @click="currentSlide = idx"
                        ></span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 类型1:敌方兵力 -->
              <div v-else class="force-detail">
                <div class="fleet-count-control">
                  <span>{{ force.name }}</span>
                  <img
                    src="../../assets/reinforcementImages/icon_save.png"
                    @click="saveEnemyFleetData(force)"
                  />
                </div>
                <div class="fleet-item">
                  <div class="card-info-grid">
                    <div class="count-input">
                      <span class="count-title">数量</span>
                      <div class="row items-center customAdd">
                        <q-btn
                          flat
                          dense
                          icon="remove"
                          size="sm"
                          @click="decreaseCount(force, index)"
                        />
                        <div class="q-mx-md">{{ force.totalSum }}</div>
                        <q-btn
                          flat
                          dense
                          icon="add"
                          size="sm"
                          @click="increaseCount(force, index)"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div class="fleet-force">
                  <div
                    v-for="(fleet, fleetIndex) in force.fleets"
                    :key="fleetIndex"
                    class="fleet-item"
                  >
                    <div class="card-info-grid">
                      <div class="count-input">
                        <span class="card-title">{{ fleet.name }}</span>
                        <div class="row items-center customAdd">
                          <q-btn
                            flat
                            dense
                            icon="remove"
                            size="sm"
                            @click="decreaseFleetCount(force, index, fleetIndex)"
                          />
                          <div class="q-mx-md">{{ fleet.count }}</div>
                          <q-btn
                            flat
                            dense
                            icon="add"
                            size="sm"
                            @click="increaseFleetCount(force, index, fleetIndex)"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="section targetmask">
          <div class="onetop">
            <div class="left">
              <img src="../../assets/images/icon_dz.png" />
              <h2>目标任务</h2>
            </div>
            <div class="right">
              <img
                src="../../assets/reinforcementImages/icon_save.png"
                @click="saveMissionObjectives()"
              />
            </div>
          </div>
          <div class="task-step">
            <el-steps direction="vertical" align-center class="steps">
              <el-step
                v-for="(mission, index) in missionObjectives"
                :key="index"
                finish-status="success"
              >
                <template #description>
                  <q-input
                    v-model.trim="mission.title"
                    debounce="500"
                    class="steps-input"
                    borderless
                    dense
                    type="textarea"
                  >
                  </q-input>
                </template>
              </el-step>
            </el-steps>
          </div>
          <div class="next">
            <q-btn
              v-if="false"
              class="prevBtn roundBox"
              color="grey-7"
              label="上一步"
              @click="prevStep"
            />
            <q-btn
              class="nextBtn roundBox"
              color="primary"
              label="下一步"
              @click="nextStep"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 提示弹窗 -->
  <q-dialog v-model="showTipDialog" persistent>
    <div class="tip-dialog">
      <div class="tip-header">提醒</div>
      <div class="tip-content" v-if="isAddOperation">
        当前数量总和已达到上限，无法继续增加！
      </div>
      <div class="tip-content" v-else>
        当前总数量减去 1 后将小于下面每一个目标加和，不能进行减操作！
      </div>
      <div class="tip-actions">
        <q-btn class="tip-confirm-btn" @click="closeTipDialog">确定</q-btn>
      </div>
    </div>
  </q-dialog>
  <!-- 导入弹窗 -->
  <q-dialog v-model="showImportDialog" persistent>
    <div class="tip-dialog">
      <div class="imprt-container">
        <div class="q-gutter-sm row items-start">
          <q-uploader
            url="http://localhost:4444/upload"
            label="导入想定"
            multiple
            batch
          />
        </div>
      </div>
      <div class="tip-actions">
        <q-btn class="tip-confirm-btn" @click="showImportDialog = false">确定</q-btn>
      </div>
    </div>
  </q-dialog>
</template>

<script setup>
import { ref, defineEmits, watch, onMounted } from "vue";
import { usereinforcementLearingStore } from "../../stores/reinforcementLearingStore";
import { api } from "boot/axios";

// 定义事件
const emit = defineEmits(["next-step", "prev-step"]);

const selectedScenarioIndex = ref(0);

const selectScenario = (index) => {
  selectedScenarioIndex.value = index;
};
const reinforcementLearingStore = usereinforcementLearingStore();

const scenarios = ref([
  {
    title: "城市反恐作战",
    status: "困难",
    environmenttype: "城市环境",
    info: "在复杂城市环境中执行反恐任务，需要精确打击并保护平民。",
    detailinfo: {
      detaillocation: "高楼密集区111111111111111",
      detailtime: "2小时",
      detailour: "我方:14人",
      detailenemy: "敌方:200人",
    },
    addtime: "2025 /01 /01",
  },
  {
    title: "城市反恐作战",
    status: "困难",
    environmenttype: "城市环境",
    info: "在复杂城市环境中执行反恐任务，需要精确打击并保护平民。",
    detailinfo: {
      detaillocation: "高楼密集区111111111111111",
      detailtime: "2小时",
      detailour: "我方:14人",
      detailenemy: "敌方:200人",
    },
    addtime: "2025 /01 /01",
  },
]);
const scenariodetail = ref({
  status: "困难",
  type: "空中拦截作战",
  info: "执行空中拦截任务，阻止敌机侵入领空。",
  detailinfo: [
    {
      type: "Environment",
      info: "空中环境",
    },
    {
      type: "Terrain",
      info: "高空地形",
    },
    {
      type: "Weather",
      info: "晴天",
    },
  ],
});
// 根据类型获取选项的函数
function getOptionsByType(type) {
  const optionsMap = {
    Environment: [
      { label: "空中环境", value: "空中环境" },
      { label: "城市环境", value: "城市环境" },
    ],
    Terrain: [
      { label: "高空地形", value: "高空地形" },
      { label: "平原地形", value: "平原地形" },
      { label: "山地地形", value: "山地地形" },
    ],
    Weather: [
      { label: "晴天", value: "晴天" },
      { label: "多云", value: "多云" },
      { label: "雨天", value: "雨天" },
      { label: "阴天", value: "阴天" },
    ],
  };
  return optionsMap[type] || [];
}
const forceComparison = ref([
  {
    type: 0,
    totalSum: 2,
    name: "炮兵营",
    fleets: [
      {
        name: "炮1",
        status: false,
        type: [
          {
            name: "70毫米炮弹",
            count: 3,
          },
          {
            name: "280毫米炮弹",
            count: 2,
          },
          {
            name: "150毫米炮弹",
            count: 2,
          },
        ],
      },
      {
        name: "炮2",
        status: false,
        type: [
          {
            name: "70毫米炮弹",
            count: 3,
          },
          {
            name: "280毫米炮弹",
            count: 2,
          },
          {
            name: "150毫米炮弹",
            count: 2,
          },
        ],
      },
    ],
  },
  {
    type: 1,
    totalSum: 10,
    name: "时敏目标",
    fleets: [
      {
        name: "时敏目标1",
        count: 2,
      },
      {
        name: "时敏目标2",
        count: 2,
      },
      {
        name: "时敏目标3",
        count: 2,
      },
      {
        name: "时敏目标4",
        count: 2,
      },
      {
        name: "时敏目标5",
        count: 2,
      },
    ],
  },
]);

const missionObjectives = ref([
  { title: "保护南路" },
  { title: "距离海盗" },
  { title: "维护航道安全" },
  { title: "维护航道安全" },
  { title: "维护航道安全" },
]);
//导入想定内容
const showImportDialog = ref(false);
const importScenario = () => {
  showImportDialog.value = true;
};
// 计算我方总兵力
const calculateOurForces = () => {
  const ourForces = forceComparison.value.find((force) => force.type === 0);
  return ourForces ? ourForces.totalSum : 0;
};

// 计算兵力比
function gcd(a, b) {
  return b === 0 ? a : gcd(b, a % b);
}
const calculateEnemyForces = () => {
  const enemyForces = forceComparison.value.find((force) => force.type === 1);
  return enemyForces ? enemyForces.totalSum : 0;
};
const calculateTroopRatio = () => {
  const ourForces = calculateOurForces();
  const enemyForces = calculateEnemyForces();

  if (enemyForces === 0) {
    return "0:1";
  }

  const divisor = gcd(ourForces, enemyForces);
  return `${Math.floor(ourForces / divisor)}:${Math.floor(enemyForces / divisor)}`;
};

// 监听兵力变化，更新统计信息
watch(
  forceComparison,
  () => {
    detailedInfo.value.Ourforces = calculateOurForces().toString();
    detailedInfo.value.Enemyforces = calculateEnemyForces().toString();
    detailedInfo.value.TroopRatio = calculateTroopRatio();
  },
  { deep: true }
);

const detailedInfo = ref({
  Ourforces: calculateOurForces().toString(),
  Enemyforces: calculateEnemyForces().toString(),
  TroopRatio: calculateTroopRatio(),
  AddTime: "2025/01/20",
  SceneID: "92.0%",
});
// 下一步按钮点击事件
function nextStep() {
  // 保存数据到store
  reinforcementLearingStore.updateStepOneData({
    scenarios: scenarios.value,
    scenariodetail: scenariodetail.value,
    forceComparison: forceComparison.value,
    missionObjectives: missionObjectives.value,
    detailedInfo: detailedInfo.value,
  });
  console.log("stepOneData", reinforcementLearingStore.stepOneData);

  emit("next-step");
}

// 上一步按钮点击事件
function prevStep() {
  emit("prev-step");
}

function getImageByType(type) {
  switch (type) {
    case "Environment":
      return "../../src/assets/reinforcementImages/xq_icon_hj.png";
    case "Terrain":
      return "../../src/assets/reinforcementImages/xq_icon_dx.png";
    case "Weather":
      return "../../src/assets/reinforcementImages/xq_icon_tq.png";
    case "持续时间":
      return "../../src/assets/reinforcementImages/xq_icon_cxsj.png";
    default:
      return "";
  }
}

function getIconByType(type) {
  if (type === 0) {
    return "../../src/assets/reinforcementImages/icon_wfbl.png";
  } else if (type === 1) {
    return "../../src/assets/reinforcementImages/icon_dfbl.png";
  }
  return "";
}

function getTitleByType(type) {
  if (type === 0) {
    return "红方";
  } else if (type === 1) {
    return "蓝方";
  }
  return "";
}
const currentSlide = ref(0);
const isDragging = ref(false);
const startX = ref(0);
const currentX = ref(0);
const dragOffset = ref(0);
function startDrag(e) {
  isDragging.value = true;
  startX.value = e.type === "touchstart" ? e.touches[0].clientX : e.clientX;
  currentX.value = -currentSlide.value * 100;
}
function drag(e) {
  if (!isDragging.value) return;
  const x = e.type === "touchmove" ? e.touches[0].clientX : e.clientX;
  dragOffset.value = x - startX.value;
}
function endDrag() {
  if (!isDragging.value) return;
  isDragging.value = false;
  // 若拖动距离超过 50px 则切换卡片
  if (dragOffset.value > 50 && currentSlide.value > 0) {
    currentSlide.value--;
  } else if (
    dragOffset.value < -50 &&
    currentSlide.value < forceComparison.value[0].fleets.length - 1
  ) {
    currentSlide.value++;
  }
  dragOffset.value = 0;
}
// 增减总数量
onMounted(() => {});
// 减少数量函数
function decreaseCount(force, index) {
  if (force.type === 0) {
    // 我方兵力逻辑
    if (force.fleets.length > 0 && force.totalSum > 1) {
      if (currentSlide.value === force.fleets.length - 1) {
        currentSlide.value = Math.max(0, force.fleets.length - 2);
      }
      force.fleets.pop();
      force.totalSum--;
    }
  } else if (force.type === 1) {
    // 蓝方兵力逻辑
    const sumOfTargets = force.fleets.reduce((sum, fleet) => sum + fleet.count, 0);
    isAddOperation.value = false;
    if (sumOfTargets > force.totalSum - 1) {
      showTipDialog.value = true;
      return;
    }
    if (force.totalSum > 0) {
      force.totalSum--;
    }
  }
}

// 增加数量函数
const isAddOperation = ref(false);
function increaseCount(force, index) {
  if (force.type === 0) {
    // 红方兵力
    if (force.totalSum < 13) {
      const newFleet = JSON.parse(JSON.stringify(force.fleets[0]));
      newFleet.type.forEach((item) => {
        item.count = 0;
      });
      force.fleets.push(newFleet);
      force.totalSum++;
    }
  } else if (force.type === 1) {
    // 敌方兵力
    if (force.totalSum < 16) {
      force.totalSum++;
    }
  }
}

// 增减单个舰队数量
function increaseFleetCount(force, index, fleetIndex) {
  const sum = force.fleets.reduce((acc, fleet) => acc + fleet.count, 0);
  if (sum < force.totalSum) {
    force.fleets[fleetIndex].count++;
  } else {
    isAddOperation.value = true;

    showTipDialog.value = true;
  }
}

function decreaseFleetCount(force, index, fleetIndex) {
  if (force.fleets[fleetIndex].count > 0) {
    force.fleets[fleetIndex].count--;
  }
}

// 增减单个装备数量
function increaseItemCount(force, index, fleetIndex, itemIndex) {
  const fleet = force.fleets[fleetIndex];
  const item = fleet.type[itemIndex];
  item.count++;
}

function decreaseItemCount(force, index, fleetIndex, itemIndex) {
  const fleet = force.fleets[fleetIndex];
  const item = fleet.type[itemIndex];
  if (item.count > 0) {
    item.count--;
  }
}

// 兵力对比状态切换
function toggleFleetStatus(fleet) {
  fleet.status = !fleet.status;
}
const showTipDialog = ref(false);
const closeTipDialog = () => {
  showTipDialog.value = false;
};
// 保存红方当前卡片数据
const saveCardData = (force, fleet) => {
  // 这里可根据实际需求实现保存逻辑，示例仅打印数据
  console.log("保存当前卡片数据:", force, fleet);
  // 可添加实际保存逻辑，如调用API或更新store
  // api.saveCardData({ force, fleet });
  // reinforcementLearingStore.updateCardData({ force, fleet });
};
//保存蓝方当前数据
const saveEnemyFleetData = (force) => {
  // 这里可以添加实际的保存逻辑，当前示例仅打印数据
  console.log("保存敌方兵力数据:", force);
  // 后续可添加 API 调用等保存逻辑
  // api.post('/save-enemy-fleet', force);
};
// 保存任务目标
const saveMissionObjectives = () => {
  // 这里可以添加实际的保存逻辑，当前示例仅打印数据
  console.log("保存目标任务数据:", missionObjectives.value);
  // 后续可添加 API 调用等保存逻辑
  // api.post('/save-mission-objectives', missionObjectives.value);
};
</script>

<style lang="scss" scoped>
.scenario-container {
  display: flex;
  flex: 1;
  min-height: 9.5rem;

  .left-panel {
    display: flex;
    flex-direction: column;
    padding-right: 0.125rem;
    width: 25%;
    .available-scenarios {
      flex-grow: 1;
      border: 0.0125rem solid rgba(112, 112, 112, 1);
      padding: 0 0.25rem;
      background-color: #181a24;
      background-image: repeating-linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.05) 0,
        rgba(255, 255, 255, 0.01) 0.05rem,
        transparent 0.0125rem,
        transparent 0.1875rem
      );
      .container {
        height: 85%;
        overflow-y: auto;
        scrollbar-width: none;
        &::-webkit-scrollbar {
          display: none;
        }
        .scenario-card {
          position: relative;
          background: rgba(29, 45, 68, 0.4);
          border-radius: 0.0625rem;
          transition: border 0.3s;
          margin-bottom: 0.125rem;
          padding: 0.125rem;
          height: 3.65rem;
        }

        .scenario-card.selected {
          position: relative;
          background: rgba(29, 45, 68, 0.3);
          border-radius: 0.0625rem;
          margin-bottom: 0.125rem;
          padding: 0.125rem;
          .selected-icon {
            position: absolute;
            top: 0;
            right: 0;
          }
        }

        .scenario-card .corner-top-left {
          position: absolute;
          top: 0;
          left: 0;
          width: 20px;
          height: 20px;
          border-top: 2px solid rgba(112, 112, 112, 1);
          border-left: 2px solid rgba(112, 112, 112, 1);
        }

        .scenario-card .corner-bottom-right {
          position: absolute;
          bottom: 0;
          right: 0;
          width: 20px;
          height: 20px;
          border-bottom: 2px solid rgba(112, 112, 112, 1);
          border-right: 2px solid rgba(112, 112, 112, 1);
        }

        .scenario-card .corner-bottom-left {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 20px;
          height: 20px;
          border-bottom: 2px solid rgba(112, 112, 112, 1);
          border-left: 2px solid rgba(112, 112, 112, 1);
        }
        .scenario-card {
          .scenario-title {
            margin: 0.125rem 0.0625rem 0.1875rem;
            height: 0.1875rem;
            line-height: 0.1875rem;
            color: #ffffff;
            font-size: 0.25rem;
          }

          .scenario-items {
            .basic-info {
              margin-bottom: 0.125rem;

              .info-left {
                border: 0.0125rem solid #c02c2c;
                border-radius: 0.2rem;
                width: 0.875rem;
                height: 0.4rem;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                color: #e82626;
                margin-right: 0.125rem;
                font-size: 0.2rem;
                background: rgba(192, 44, 44, 0.3);
              }

              .info-right {
                width: 1.25rem;
                height: 0.4rem;
                border: 0.0125rem solid;
                border-color: #ffffff;
                border-radius: 0.2rem;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                color: #ffffff;
                font-size: 0.2rem;
                background: rgba(225, 225, 225, 0.2);
              }
            }

            .info-text {
              display: block;
              flex: wrap;
              margin-bottom: 0.0625rem;
              color: #9cacc6;
              font-size: 0.2rem;
            }

            .detail-info {
              background-color: rgba(225, 225, 225, 0.1);
              margin-bottom: 0.1rem;
              border-radius: 0.1rem;
              padding: 0.25rem 0;
              .top,
              .bottom {
                display: flex;
                .top-item {
                  margin-bottom: 0.0625rem;
                }
                .top-item,
                .bottom-item {
                  width: 40%;
                  display: flex;
                  align-items: center;
                  margin-left: 0.35rem;
                  p {
                    color: #ffffff;
                    font-size: 0.2rem;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    margin-bottom: 0;
                  }
                }
              }
            }

            .add-time {
              color: #9cacc6;
              font-size: 0.1625rem;
            }
          }
        }
      }
      .import-section {
        .bts {
          display: flex;
          align-items: center;
          justify-content: center;
          .import-btn {
            width: 1.75rem;
            position: relative;
            padding: 0.15rem;
            .btn-icon {
              position: absolute;
              margin-left: 0.3rem;
              left: 0;
            }
            :deep(.q-btn__content) {
              margin-left: 0.3rem;
            }
          }
        }
      }
    }
    .available-scenarios {
      margin-left: 0.125rem;
      position: relative;

      &::before {
        position: absolute;
        content: "";
        left: -0.1rem;
        top: 0;
        width: 0.025rem;
        height: 100%;
        background: rgba(156, 172, 198);
        border-radius: 0.125rem;
      }

      &::after {
        position: absolute;
        content: "";
        left: -0.075rem;
        top: 0;
        width: 0.025rem;
        height: 100%;
        background: rgba(156, 172, 198, 0.5);
      }
    }
  }

  .right-panel {
    display: flex;
    flex-direction: column;
    margin-left: 0.0625rem;
    width: 75%;
    .scenario-detail {
      border: 0.0125rem solid rgba(112, 112, 112, 1);
      margin-bottom: 0.25rem;
      height: 45%;
      padding: 0 0.25rem 0 0.25rem;
      display: flex;
      flex-direction: column;
      align-items: stretch;
      background-color: #181a24;
      background-image: repeating-linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.05) 0,
        rgba(255, 255, 255, 0.01) 0.05rem,
        transparent 0.0125rem,
        transparent 0.1875rem
      );
      .detail-top {
        display: flex;
        height: 70%;
        .detail-left {
          flex: 1;
        }

        .top-title {
          display: flex;
          justify-content: space-between;
          .title-left {
            .center-content {
              display: flex;
              flex-direction: column;
              margin-left: 0.375rem;

              .one-content {
                color: #ffffff;
                font-size: 0.25rem;
                margin-bottom: 0.0625rem;
              }

              .two-content {
                color: #cea345;
                font-size: 0.25rem;
              }
            }
          }

          .right-content {
            margin: 0.75rem 0.75rem 0.45rem 0.75rem;
            width: 1.25rem;
            height: 0.55rem;
            border: 0.0125rem solid;
            border-color: #c02c2c;
            border-radius: 0.275rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(192, 44, 44, 0.3);

            span {
              font-size: 0.225rem;
              height: 0.55rem;
              line-height: 0.55rem;
              text-align: center;
              color: #c02c2c;
            }
          }
        }

        .bottom-content {
          display: flex;
          margin-left: 0.25rem;
          gap: 1.25rem;

          .info-item {
            display: flex;
            align-items: center;
            flex: 1;
            img {
              margin-right: 0.125rem;
            }

            .info-text {
              text-align: center;
              position: relative;
              .info-main {
                width: 2rem;
                color: #ffffff;
                font-size: 0.25rem;
                :deep(.q-field__inner) {
                  background: rgba(112, 112, 112, 0.2);
                  font-size: 0.225rem;
                }
                :deep(.q-field__native span) {
                  margin-left: 0.125rem;
                }
              }
            }
          }
          .info-item {
            position: relative;
            &:not(:last-child)::before {
              position: absolute;
              content: "";
              top: 0;
              right: 0.525rem;
              width: 0.0225rem;
              height: 100%;
              background: rgba(112, 112, 112, 1);
            }
          }
        }
        .detail-right {
          margin: 0.3rem 0 0 0.375rem;
          width: 4.375rem;
          height: 2.15rem;

          .detail-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .detail-right {
          margin-left: 0.125rem;
          position: relative;

          &::before {
            position: absolute;
            content: "";
            left: -0.25rem;
            top: 0;
            width: 0.0225rem;
            height: 100%;
            background: rgba(112, 112, 112, 1);
          }
        }
      }

      .detail-bottom {
        display: flex;
        align-items: center;
        height: 30%;
        h5 {
          height: 0.325rem;
          line-height: 0.325rem;
          color: #cea345;
          font-size: 0.225rem;
          margin-right: 0.375rem;
        }
        .stats-info {
          display: flex;
          height: 0.45rem;
          gap: 0.8rem;
          .info-item {
            display: flex;
            height: 0.325rem;
            line-height: 0.4375rem;
            span {
              color: #ffffff;
              font-size: 0.225rem;
            }
            p {
              color: rgba(74, 180, 255, 1);
              font-size: 0.225rem;
            }
          }
        }
        .stats-section {
          position: relative;
          display: flex;
          align-items: center;
          padding-right: 0.625rem;
          height: 0.375rem;
          width: 50%;
          &::before {
            position: absolute;
            content: "";
            right: -0.1rem;
            top: 0;
            width: 0.0125rem;
            height: 100%;
            background: rgba(112, 112, 112, 1);
          }
        }
        .create-info {
          display: flex;
          align-items: center;
          padding-left: 0.625rem;
          height: 0.375rem;
          width: 50%;
        }
      }
      .detail-bottom {
        position: relative;
        &::after {
          position: absolute;
          content: "";
          top: 0;
          height: 0.0125rem;
          width: 100%;
          background: rgba(112, 112, 112, 1);
        }
      }
    }
    .bottom-sections {
      display: flex;
      height: 55%;
      gap: 0.25rem;
      background-color: #181a24;
      background-image: repeating-linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.05) 0,
        rgba(255, 255, 255, 0.01) 0.05rem,
        transparent 0.0125rem,
        transparent 0.1875rem
      );
      .targetmask {
        width: 25%;
        .onetop {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .left {
            display: flex;
            align-items: center;
          }
          .right {
            img {
              width: 0.325rem;
              height: 0.325rem;
              cursor: pointer;
            }
          }
        }
      }
      .section {
        border: 0.0125rem solid rgba(112, 112, 112, 1);
        border-radius: 0.0625rem;
        padding: 0 0.1875rem;
        display: flex;
        flex-direction: column;
        flex: 1;
        width: 75%;
        .task-step {
          .steps {
            margin-left: 0.125rem;
            margin-bottom: 0.5rem;

            :deep(.el-step__icon) {
              background: rgba(51, 106, 150, 1);
              width: 0.45rem;
              height: 0.45rem;
              margin-left: -0.1rem;
            }
            :deep(.el-step__icon-inner) {
              color: rgba(74, 180, 255, 1);
              font-size: 0.225rem;
            }
            :deep(.q-field__control-container) {
              height: 0.625rem;
            }
            .steps-input {
              margin-left: 0.25rem;
              width: 2.175rem;
            }
          }
        }

        .next {
          display: flex;
          gap: 0.25rem;
          .prevBtn {
            margin-right: auto;
          }

          .nextBtn {
            margin-left: auto;
          }
        }
      }

      .force-comparison {
        display: flex;
        flex-direction: column;
        .count-input {
          display: flex;
          align-items: center;
          margin-top: 0.125rem;
          span {
            height: 0.325rem;
            line-height: 0.4375rem;
            color: #ffffff;
            font-size: 0.225rem;
            margin-right: 0.2rem;
          }
        }
        .fleet-count-control {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 25%;
          img {
            width: 0.325rem;
            height: 0.325rem;
            cursor: pointer;
          }
          span {
            height: 0.3625rem;
            line-height: 0.4813rem;
            color: #cea345;
            font-size: 0.25rem;
          }
          .count-title {
            height: 0.325rem;
            line-height: 0.4375rem;
            color: #ffffff;
            font-size: 0.25rem;
          }
        }
        .force-container {
          flex: 1;
          display: flex;
          gap: 0.25rem;

          .force-info {
            border: 0.0125rem solid rgba(0, 252, 255, 1);
            height: 95%;
            padding: 0 0.125rem;
            display: flex;
            flex-direction: column;
            flex: 1;
            border-radius: 0.125rem;
            background: rgba(0, 252, 255, 0.1);

            .onetop {
              border-bottom: 0.0125rem solid rgba(0, 252, 255, 1);
              height: 15%;
              img {
                width: 0.2476rem;
                height: 0.3011rem;
                margin: 0;
              }
              h5 {
                height: 0.3625rem;
                line-height: 0.3625rem;
                font-size: 0.25rem;
                margin: 0.125rem;
              }
            }
            .force-detail {
              height: 70%;
            }
            .fleet-force {
              height: 2rem;
              overflow-x: hidden;
              overflow-y: auto;
            }
            .fleet-force::-webkit-scrollbar {
              width: 0.07rem;
            }

            .fleet-force::-webkit-scrollbar-track {
              background: rgba(0, 0, 0, 0.5);
              border-radius: 0.05rem;
              margin: 0.05rem 0;
            }

            .fleet-force::-webkit-scrollbar-thumb {
              background: #888;
              width: 0.05rem;
              border-radius: 0.05rem;
            }

            .fleet-force::-webkit-scrollbar-thumb:hover {
              background: #555;
            }
            .fleet-item {
              display: flex;
              flex-direction: column;
              margin-bottom: 0.0625rem;
              width: 4rem;
              .card-info-grid {
                margin-bottom: 0.1rem;
                height: 70%;
                .equipment-item {
                  display: flex;
                  align-items: center;
                  .equipment-desc {
                    height: 0.325rem;
                    line-height: 0.4375rem;
                    color: #ffffff;
                    font-size: 0.2rem;
                    margin-right: 0.25rem;
                  }
                }
              }
            }
          }
        }
        .card-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 15%;
          span {
            height: 0.3625rem;
            line-height: 0.4813rem;
            color: #4ab4ff;
            font-size: 0.225rem;
          }
          img {
            width: 0.325rem;
            height: 0.325rem;
            cursor: pointer;
          }
        }
        .card-slider {
          position: relative;
          width: 7.5rem;
          overflow: hidden;
          margin-top: 0.125rem;
          height: 90%;
          .slider-container {
            display: flex;
            transition: transform 0.3s ease;
            height: 100%;
            .fleet-card {
              background: rgba(4, 10, 31, 0.3);
              border-radius: 0.0625rem;
              padding: 0.1rem;
              margin-bottom: 0.1rem;
              flex: 0 0 100%;
              .card-info-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 0.1rem;
                margin-bottom: 0.1rem;
                .equipment-item {
                  display: flex;
                  align-items: center;
                  .equipment-desc {
                    width: 1rem;
                    height: 0.325rem;
                    line-height: 0.4375rem;
                    font-family: MicrosoftYaHi;
                    color: #ffffff;
                    font-size: 0.225rem;
                    margin-right: 0.15rem;
                  }
                }
              }
              .card-status {
                display: flex;
                align-items: center;
                img {
                  margin-left: 0.125rem;
                  cursor: pointer;
                }
                .status-desc {
                  width: 1rem;
                  height: 0.325rem;
                  color: #ffffff;
                  font-size: 0.225rem;
                }
              }
            }
          }
          .slider-indicator {
            display: flex;
            justify-content: center;
            gap: 0.05rem;
            margin-top: 0.4rem;
            height: 15%;
            span {
              width: 0.1rem;
              height: 0.1rem;
              border-radius: 50%;
              background-color: #ccc;
              cursor: pointer;
            }
            span.active {
              background-color: #333;
            }
          }
        }
      }
    }
  }
  .right-panel {
    position: relative;

    &::before {
      position: absolute;
      content: "";
      left: -0.1rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198);
      border-radius: 0.125rem;
    }

    &::after {
      position: absolute;
      content: "";
      left: -0.075rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198, 0.5);
    }
  }
}
.onetop {
  display: flex;
  align-items: center;
  h2 {
    height: 0.1875rem;
    line-height: 0.1875rem;
    color: #ffffff;
    font-size: 0.25rem;
  }
  img {
    width: 0.2rem;
    height: 0.2rem;
    margin-right: 0.1875rem;
  }
}
.tip-dialog {
  background: #102947;
  border: 0.05rem solid rgba(1, 74, 173, 0.8);
  border-radius: 0.125rem;
  padding: 0.25rem;
  min-width: 6rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.3);

  .tip-header {
    color: #fff;
    font-size: 0.25rem;
    font-weight: bold;
    margin-bottom: 0.175rem;
    text-align: left;
  }

  .tip-content {
    color: #fff;
    font-size: 0.2rem;
    line-height: 1.5;
    margin-bottom: 0.3rem;
    text-align: left;
  }

  .tip-actions {
    display: flex;
    justify-content: flex-end;

    .tip-confirm-btn {
      background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%) !important;
      color: white !important;
      border-radius: 0.0625rem;
      font-size: 0.175rem;
      min-width: 1.25rem;
      height: 0.5rem;

      &:hover {
        background: linear-gradient(135deg, #357abd 0%, #2a5a8a 100%) !important;
      }
    }

    .tip-cancel-btn {
      background: #273f5b !important;
      color: white !important;
      border-radius: 0.0625rem;
      font-size: 0.175rem;
      min-width: 1.25rem;
      height: 0.5rem;
      margin-left: 0.125rem;

      &:hover {
        background: #1e2f42 !important;
      }
    }
  }
}
:deep(.q-btn__content) {
  font-size: 0.175rem;
}
</style>
