# 强化学习模型管理指南

## 概述

强化学习训练平台会在训练完成后自动生成和保存模型文件，支持模型信息查询、下载和使用。

## 🎯 模型生成流程

### 1. 训练过程中的检查点保存

训练过程中，Ray RLlib会自动保存检查点：

```python
# 在ray_train_gym-npu.py中配置
tune.run(
    rl_type,
    checkpoint_freq=50,        # 每50个iteration保存一次检查点
    checkpoint_at_end=True,    # 训练结束时保存最终检查点
    local_dir="~/ray_results",
    name=experiment_name,
)
```

### 2. 训练完成后的模型导出

训练完成后，脚本会自动：

1. **选择最佳检查点**：基于`episode_reward_mean`指标
2. **导出多种格式**：
   - 完整检查点目录
   - PyTorch模型文件（.pth）
   - 策略权重文件（.pkl）
   - 使用说明文档

3. **生成模型信息**：保存到`/workspace/rl_model_info.json`

## 📁 模型文件结构

训练完成后，模型文件保存在配置指定的路径：

```
/workspace/models/my_rl_model/
├── my_rl_model_checkpoint/          # 完整的Ray RLlib检查点
│   ├── algorithm_state.pkl
│   ├── policies/
│   │   └── default_policy/
│   │       ├── policy_state.pkl
│   │       └── rllib_checkpoint.json
│   └── trainer.pkl
├── my_rl_model.pth                  # PyTorch模型文件
├── my_rl_model_weights.pkl          # 策略权重文件
└── my_rl_model_README.md            # 使用说明
```

## 🔍 模型信息查询

### API接口

```http
GET /backend/training/rl/{training_id}/model/info
```

### 响应格式

```json
{
    "success": true,
    "data": {
        "modelInfo": {
            "model_name": "PPO_model_20231201_143022",
            "model_path": "/workspace/models/PPO_model_20231201_143022",
            "algorithm": "PPO",
            "training_iterations": 1000,
            "best_checkpoint": "/workspace/ray_results/PPO-task_123/checkpoint_001000",
            "timestamp": "2023-12-01T14:30:22",
            "task_id": "rl_task_123"
        },
        "trainingId": "rl_train_20231201_143022_1234"
    }
}
```

### Python代码示例

```python
from utils.rl_trainer import RLTrainerManager

# 获取训练器
trainer = RLTrainerManager.get_trainer("rl_train_20231201_143022_1234")

# 获取模型信息
model_info = trainer.get_model_info()
if model_info:
    print(f"模型名称: {model_info['model_name']}")
    print(f"算法类型: {model_info['algorithm']}")
    print(f"训练轮数: {model_info['training_iterations']}")
```

## 📥 模型下载

### API接口

```http
POST /backend/training/rl/{training_id}/model/download
Content-Type: application/json

{
    "localPath": "/local/models/my_rl_model"
}
```

### 响应格式

```json
{
    "success": true,
    "data": {
        "message": "模型下载成功",
        "localPath": "/local/models/my_rl_model",
        "trainingId": "rl_train_20231201_143022_1234"
    }
}
```

### Python代码示例

```python
# 下载模型到本地
success = trainer.download_model("/local/models/my_rl_model")
if success:
    print("模型下载成功")
```

## 🚀 模型使用

### 1. 从检查点加载模型

```python
from ray.rllib.algorithms import Algorithm

# 加载算法
algo = Algorithm.from_checkpoint("/path/to/checkpoint")

# 获取策略
policy = algo.get_policy()

# 进行推理
observation = env.reset()
action = policy.compute_single_action(observation)
```

### 2. 评估模型性能

```python
import gym

# 创建环境
env = gym.make("Pendulum-v1")

# 运行一个episode
obs = env.reset()
total_reward = 0
done = False

while not done:
    action = policy.compute_single_action(obs)
    obs, reward, done, info = env.step(action)
    total_reward += reward

print(f"Total reward: {total_reward}")
```

### 3. 批量评估

```python
def evaluate_model(policy, env, num_episodes=10):
    """评估模型性能"""
    total_rewards = []
    
    for episode in range(num_episodes):
        obs = env.reset()
        episode_reward = 0
        done = False
        
        while not done:
            action = policy.compute_single_action(obs)
            obs, reward, done, info = env.step(action)
            episode_reward += reward
        
        total_rewards.append(episode_reward)
    
    avg_reward = sum(total_rewards) / len(total_rewards)
    print(f"Average reward over {num_episodes} episodes: {avg_reward}")
    return avg_reward
```

## 🔧 模型管理最佳实践

### 1. 模型版本管理

```python
# 在配置中指定有意义的模型名称
config = {
    "output": {
        "savePath": "/models/rl_training",
        "saveName": f"PPO_Pendulum_v{version}_{timestamp}"
    }
}
```

### 2. 模型性能记录

每个模型都会自动生成性能记录：

- 最佳episode奖励
- 训练迭代次数
- 算法类型和超参数
- 训练时间和资源使用

### 3. 模型备份策略

- 定期备份重要模型
- 保留训练日志和配置文件
- 记录模型的使用场景和性能表现

## 🛠️ 故障排除

### 常见问题

1. **模型信息文件不存在**
   - 检查训练是否完成
   - 验证训练过程中是否有错误

2. **模型下载失败**
   - 检查网络连接
   - 验证本地路径权限
   - 确认磁盘空间充足

3. **模型加载错误**
   - 检查Ray版本兼容性
   - 验证模型文件完整性
   - 确认环境依赖正确安装

### 调试命令

```python
# 检查模型文件是否存在
trainer.get_model_info()

# 查看训练日志
logs = trainer.get_training_logs(lines=100)

# 检查训练状态
status = trainer.get_task_status()
```

## 📊 模型性能监控

训练平台提供完整的模型性能监控：

- 训练过程中的奖励曲线
- 策略损失和价值损失变化
- 资源使用情况
- 模型收敛性分析

通过这些信息，可以：
- 评估模型质量
- 优化训练参数
- 选择最佳模型版本
- 进行模型对比分析

## 🎯 总结

强化学习训练平台提供了完整的模型生命周期管理：

1. **自动保存**：训练过程中自动保存检查点
2. **智能导出**：训练完成后导出多种格式的模型
3. **信息管理**：详细记录模型信息和性能指标
4. **便捷下载**：支持一键下载模型到本地
5. **使用指导**：提供详细的模型使用说明

这确保了训练生成的模型能够被有效管理和使用！
