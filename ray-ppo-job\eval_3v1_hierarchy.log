2025-06-30 18:39:03,472	WARNING utils.py:596 -- Detecting docker specified CPUs. In previous versions of Ray, CPU detection in containers was incorrect. Please ensure that <PERSON> has enough CPUs allocated. As a temporary workaround to revert to the prior behavior, set `RAY_USE_MULTIPROCESSING_CPU_COUNT=1` as an env var before starting Ray. Set the env var: `RAY_DISABLE_DOCKER_CPU_WARNING=1` to mute this warning.
2025-06-30 18:39:03,595	INFO worker.py:1852 -- Started a local Ray instance.
2025-06-30 18:39:03,929	WARNING deprecation.py:50 -- DeprecationWarning: `config.training(num_sgd_iter=..)` has been deprecated. Use `config.training(num_epochs=..)` instead. This will raise an error in the future!
2025-06-30 18:39:03,931	WARNING deprecation.py:50 -- DeprecationWarning: `AlgorithmConfig.evaluation(evaluation_num_workers=..)` has been deprecated. Use `AlgorithmConfig.evaluation(evaluation_num_env_runners=..)` instead. This will raise an error in the future!
2025-06-30 18:39:03,934	WARNING algorithm_config.py:4704 -- You are running PPO on the new API stack! This is the new default behavior for this algorithm. If you don't want to use the new API stack, set `config.api_stack(enable_rl_module_and_learner=False,enable_env_runner_and_connector_v2=False)`. For a detailed migration guide, see here: https://docs.ray.io/en/master/rllib/new-api-stack-migration-guide.html
2025-06-30 18:39:03,936	WARNING algorithm_config.py:4733 -- You configured a custom `model` config (probably through calling config.training(model=..), whereas your config uses the new API stack! In order to switch off the new API stack, set in your config: `config.api_stack(enable_rl_module_and_learner=False, enable_env_runner_and_connector_v2=False)`. If you DO want to use the new API stack, configure your model, instead, through: `config.rl_module(model_config={..})`.
/home/<USER>/.conda/envs/ray/lib/python3.11/site-packages/ray/rllib/algorithms/algorithm.py:512: RayDeprecationWarning: This API is deprecated and may be removed in future Ray releases. You could suppress this warning by setting env variable PYTHONWARNINGS="ignore::DeprecationWarning"
`UnifiedLogger` will be removed in Ray 2.7.
  return UnifiedLogger(config, logdir, loggers=None)
/home/<USER>/.conda/envs/ray/lib/python3.11/site-packages/ray/tune/logger/unified.py:53: RayDeprecationWarning: This API is deprecated and may be removed in future Ray releases. You could suppress this warning by setting env variable PYTHONWARNINGS="ignore::DeprecationWarning"
The `JsonLogger interface is deprecated in favor of the `ray.tune.json.JsonLoggerCallback` interface and will be removed in Ray 2.7.
  self._loggers.append(cls(self.config, self.logdir, self.trial))
/home/<USER>/.conda/envs/ray/lib/python3.11/site-packages/ray/tune/logger/unified.py:53: RayDeprecationWarning: This API is deprecated and may be removed in future Ray releases. You could suppress this warning by setting env variable PYTHONWARNINGS="ignore::DeprecationWarning"
The `CSVLogger interface is deprecated in favor of the `ray.tune.csv.CSVLoggerCallback` interface and will be removed in Ray 2.7.
  self._loggers.append(cls(self.config, self.logdir, self.trial))
/home/<USER>/.conda/envs/ray/lib/python3.11/site-packages/ray/tune/logger/unified.py:53: RayDeprecationWarning: This API is deprecated and may be removed in future Ray releases. You could suppress this warning by setting env variable PYTHONWARNINGS="ignore::DeprecationWarning"
The `TBXLogger interface is deprecated in favor of the `ray.tune.tensorboardx.TBXLoggerCallback` interface and will be removed in Ray 2.7.
  self._loggers.append(cls(self.config, self.logdir, self.trial))
[36m(MultiAgentEnvRunner pid=132040)[0m 2025-06-30 18:39:06,364	WARNING deprecation.py:50 -- DeprecationWarning: `RLModule(config=[RLModuleConfig object])` has been deprecated. Use `RLModule(observation_space=.., action_space=.., inference_only=.., model_config=.., catalog_class=..)` instead. This will raise an error in the future!


     JSBSim Flight Dynamics Model v1.2.2 Mar 22 2025 11:56:49
2025-06-30 18:39:06,646	WARNING deprecation.py:50 -- DeprecationWarning: `RLModule(config=[RLModuleConfig object])` has been deprecated. Use `RLModule(observation_space=.., action_space=.., inference_only=.., model_config=.., catalog_class=..)` instead. This will raise an error in the future!
2025-06-30 18:39:06,658	WARNING algorithm_config.py:4733 -- You configured a custom `model` config (probably through calling config.training(model=..), whereas your config uses the new API stack! In order to switch off the new API stack, set in your config: `config.api_stack(enable_rl_module_and_learner=False, enable_env_runner_and_connector_v2=False)`. If you DO want to use the new API stack, configure your model, instead, through: `config.rl_module(model_config={..})`.
/home/<USER>/.conda/envs/ray/lib/python3.11/site-packages/gymnasium/envs/registration.py:642: UserWarning: [33mWARN: Overriding environment rllib-multi-agent-env-v0 already in registry.[0m
  logger.warn(f"Overriding environment {new_spec.id} already in registry.")
[36m(_WrappedExecutable pid=132044)[0m Setting up process group for: env:// [rank=0, world_size=1]
[36m(MultiAgentEnvRunner pid=132043)[0m 2025-06-30 18:39:09,048	WARNING deprecation.py:50 -- DeprecationWarning: `RLModule(config=[RLModuleConfig object])` has been deprecated. Use `RLModule(observation_space=.., action_space=.., inference_only=.., model_config=.., catalog_class=..)` instead. This will raise an error in the future!
2025-06-30 18:39:12,775	WARNING util.py:61 -- Install gputil for GPU system monitoring.
[36m(_WrappedExecutable pid=132044)[0m 2025-06-30 18:39:12,753	WARNING deprecation.py:50 -- DeprecationWarning: `RLModule(config=[RLModuleConfig object])` has been deprecated. Use `RLModule(observation_space=.., action_space=.., inference_only=.., model_config=.., catalog_class=..)` instead. This will raise an error in the future!
2025-06-30 18:39:12,877	INFO trainable.py:577 -- Restored on 10.244.5.121 from checkpoint: Checkpoint(filesystem=local, path=/home/<USER>/xiangshuai/projects/xs/ray-ppo-job/ray_results/jsbsim-3v1-bsl-hierarchy_20250628-014732/PPO_JSBSim-Combat_ce7ec_00000_0_2025-06-28_01-47-34/checkpoint_000010)
使用检查点: /home/<USER>/xiangshuai/projects/xs/ray-ppo-job/ray_results/jsbsim-3v1-bsl-hierarchy_20250628-014732/PPO_JSBSim-Combat_ce7ec_00000_0_2025-06-28_01-47-34/checkpoint_000010
[36m(MultiAgentEnvRunner pid=132040)[0m 
[36m(MultiAgentEnvRunner pid=132040)[0m 
[36m(MultiAgentEnvRunner pid=132040)[0m      JSBSim Flight Dynamics Model v1.2.2 Mar 22 2025 11:56:49
[36m(MultiAgentEnvRunner pid=132040)[0m jsbsim agents:  {'A0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7fa54c79dd90>, 'A0200': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7fa54e4b33d0>, 'A0300': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7fa54cac38d0>, 'B0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7fa54a9a7a50>}
jsbsim agents:  {'A0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f59ddc16fd0>, 'A0200': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f5b7c2104d0>, 'A0300': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f59ddc17350>, 'B0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f5b7c21b510>}
[36m(MultiAgentEnvRunner pid=132043)[0m 
[36m(MultiAgentEnvRunner pid=132043)[0m 
jsbsim agents:  {'A0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f5b61416b50>, 'A0200': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f5b61416810>, 'A0300': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f5b61417510>, 'B0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f5b61416c90>}
jsbsim agents:  {'A0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f5b615c7690>, 'A0200': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f596c353cd0>, 'A0300': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f596c345210>, 'B0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f57d02b4dd0>}

Episode 0 completed:
Episode Length: 621
Agent A0100 Reward: 235.28292101738347
Agent A0200 Reward: 143.0293197853482
Agent A0300 Reward: 381.112415751656
Agent B0100 Reward: 285.9833517398575
Agent A0100 status: alive
Agent A0200 status: dead
Agent A0300 status: alive
Agent B0100 status: dead
------------------------
Average reward over 1 episodes: 0.0
回放文件已保存到: /home/<USER>/xiangshuai/projects/xs/ray-ppo-job/eval_results/jsbsim-3v1-bsl-hierarchy_20250630-183912.acmi
评估结果已保存到: /home/<USER>/xiangshuai/projects/xs/ray-ppo-job/eval_results/jsbsim-3v1-bsl-hierarchy_20250630-183912.txt
[36m(MultiAgentEnvRunner pid=132043)[0m      JSBSim Flight Dynamics Model v1.2.2 Mar 22 2025 11:56:49
[36m(MultiAgentEnvRunner pid=132043)[0m jsbsim agents:  {'A0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7fa6d8b82e10>, 'A0200': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7fa6d6d682d0>, 'A0300': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7fa6d67b0e50>, 'B0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7fa6d41c9710>}
            [JSBSim-ML v2.0]

JSBSim startup beginning ...

