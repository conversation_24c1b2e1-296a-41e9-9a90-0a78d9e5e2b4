<template>
  <div class="intelligent-algorithm-page">
    <!-- 标题区域 -->
    <!-- <div class="flexTitle">
      <div class="title labelColor">
        算法模型
      </div>
      <q-btn color="primary" class="roundBox" label="新建算法" />
    </div> -->

    <!-- 算法模型选择区域 -->
    <div class="algorithm-grid">
      <!-- 动态生成算法设备 -->
      <div v-for="device in algorithmDevices" :key="device.id" class="algorithm-item"
        @click="selectAlgorithm(device.id)">
        <div class="algorithm-image">
          <img :src="device.image" :alt="device.alt" />
          <!-- 动态生成标签 -->
          <div class="flexBox">
            <div v-for="(label, index) in device.labels" :key="index" class="detection-label noPosition"
              @click.stop="goToIntelligentModel(label.route, label)">
              {{ label.name }}
            </div>
          </div>
        </div>
        <div class="algorithm-title labelColor">{{ device.title }}</div>
      </div>


    </div>

  </div>
  <div class="bottomArea"></div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { usePlugin } from 'composables/plugin.js'

const router = useRouter()
const { notify } = usePlugin()

// 算法设备数据配置
const algorithmDevices = ref([
  {
    id: 'sjzhc',
    name: '车',
    title: '车',
    // image: new URL('../../assets/images/sjzhc.png', import.meta.url).href,

    labels: [
      { name: 'hl规划', route: '/ai-model/intelligent', type: 'intelligent' },
      { name: 'mbzddjbwsb', route: '/ai-model/reinforcement', type: 'reinforcement' },
      { name: 'znhspg', route: '/ai-model/deep-learning', type: 'deep-learning' },
      { name: 'rjznjhmx', route: '/ai-model/largeHome', type: 'largeHome' }
    ]
  },
  {
    id: 'mbbzc',
    name: '车',

    // image: new URL('../../assets/images/mbbzc.png', import.meta.url).href,
    alt: '车',
    labels: [
      { name: 'smmbsb', route: '/ai-model/intelligent', type: 'intelligent' },
      { name: 'smmbxwyc', route: '/ai-model/reinforcement', type: 'reinforcement' },
      { name: 'mbzddjbwsb', route: '/ai-model/textHome', type: 'textHome' },
      { name: 'rjjhznmx', route: '/ai-model/largeHome', type: 'largeHome' }
    ]
  },
  {
    id: 'ycxshjp',
    name: '炮',
    title: 's炮',
    // image: new URL('../../assets/images/ycxshjp.png', import.meta.url).href,

    labels: [
      { name: 'hlgh', route: '/ai-model/deep-learning', type: 'deep-learning' },
      { name: 'rjznjhmx', route: '/ai-model/deep-learning', type: 'deep-learning' }
    ]
  },
  {
    id: 'wrj',
    name: '机',
    title: '机',
    // image: new URL('../../assets/images/KVB802Atest.png', import.meta.url).href,

    labels: [
      { name: 'mbsb', route: '/ai-model/intelligent', type: 'intelligent' }
    ]
  },
  {
    id: 'hjd',
    name: '弹',
    title: '弹',
    // image: new URL('../../assets/images/hjd.jpg', import.meta.url).href,

    labels: [
      { name: 'znxtdj', route: '/ai-model/intelligent', type: 'intelligent' },
    ]
  },
  {
    id: 'znsxhj',
    name: '环境',
    title: '环境',
    // image: new URL('../../assets/images/znsxhj.png', import.meta.url).href,

    labels: [
      { name: 'znmx', route: '/ai-model/intelligent', type: 'intelligent' },
      { name: '强化学习', route: '/ai-model/reinforcement', type: 'reinforcement' },
      { name: '深度学习', route: '/ai-model/deep-learning', type: 'deep-learning' },
      { name: 'a', route: '/ai-model/intelligent', type: 'intelligent' },
      { name: 'b', route: '/ai-model/reinforcementStudy', type: 'intelligent' },
      { name: 'c', route: '/ai-model/deep-learning', type: 'deep-learning' },
      { name: 'b', route: '/ai-model/reinforcementStudy', type: 'intelligent' },
      { name: 'd', route: '/ai-model/deep-learning', type: 'deep-learning' },
      { name: 'e', route: '/ai-model/reinforcement', type: 'reinforcement' },
    ]
  }
])

// 选择算法
function selectAlgorithm(algorithmType) {
  notify(`已选择${getAlgorithmName(algorithmType)}`, 'positive')
  // 这里可以根据需要跳转到相应的页面或执行其他操作
  console.log('Selected algorithm:', algorithmType)
}

// 跳转到智能模型页面
function goToIntelligentModel(url, labelData) {
  // 传递标签信息到目标页面
  router.push({
    path: url,
    query: {
      labelName: labelData.name,
      labelType: labelData.type,
      timestamp: new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }).replace(/\//g, '-').replace(/,/g, '').replace(/\s/g, ' ')
    }
  })
}

// 获取算法名称
function getAlgorithmName(type) {
  const names = {
    'uav1': '无人车1',
    'uav2': '无人车2',
    'uav3': '无人车3',
    'drone': '无人机',
    'unknown': '无人机2'
  }
  return names[type] || '未知算法'
}
</script>

<style lang="scss" scoped>
.intelligent-algorithm-page {

  padding: 0;
  margin: 0;
  margin-top: .25rem;
  // background-color: #f5f7fa; origin
  background-color: #0a1828;
  width: 100%;
  display: flex;
  flex-direction: column;
  // overflow: hidden;
  border: .0125rem solid transparent !important;
  border-image: linear-gradient(to right, rgba(178, 215, 241, 0.8), #293f64) 1 !important;
  margin-top: 1.5rem;

  .flexTitle {
    padding: 0 .3rem;
    box-sizing: border-box;
    height: .8rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: .0125rem solid transparent !important;
    border-image: linear-gradient(to right, #3c66a4, #293f64) 1 !important;

    .title {
      font-size: .3rem;
      font-weight: 700;
    }
  }

}

.algorithm-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  width: 100%;
  gap: .2rem;
  margin: 0;
  padding: .125rem;
  // max-height: 10rem;
}

.algorithm-item {
  cursor: pointer;
  transition: all 0.3s ease;
  border: .0125rem solid #ddd;
  overflow: hidden;
  background: white;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 5.2rem !important;
  border-radius: .0625rem;
}

// 响应式高度调整
.algorithm-item {

  // 2K显示器（1920px以上）
  @media screen and (min-width: 1921px) {
    height: 4.6rem !important;
  }

  // 4K显示器优化
  @media screen and (min-width: 2560px) and (min-height: 1400px) {
    height: 5.2rem !important;
  }
}

.algorithm-item:hover {
  transform: scale(1.02);
  border: .0125rem solid transparent !important;
  box-shadow: 0 .1rem .3125rem rgba(25, 118, 210, 0.2);
  z-index: 10;
}

.algorithm-image {
  position: relative;
  flex: 1;
  overflow: hidden;
  min-height: 0;

  /* 修改布局 */
  .flexBox {
    width: 100%;
    position: absolute;
    top: 0;
    display: flex;
    flex-wrap: wrap;
    padding: .2rem .3rem;

    .noPosition {
      position: static;
      // margin-bottom: 20px;
    }
  }
}

.algorithm-image img {
  width: 100%;
  height: 100%;
  // object-fit: cover;
  // object-fit: fill;
  transition: transform 0.3s ease;
  opacity: 0.8;
}

.algorithm-item:hover .algorithm-image img {
  transform: scale(1.05);
}

.algorithm-title {
  position: absolute;
  width: 100%;
  bottom: 0;
  left: 0;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0), #000000);
  height: .75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: clamp(.2rem, 2vw, .25rem);
  transition: background-color 0.3s ease;
}

.algorithm-item:hover .algorithm-title {
  // background-color: #f0f8ff; //origin
  background: linear-gradient(180deg, transparent 0%, rgba(74, 180, 255, .7)100%);
}

.detection-label {
  padding: .1rem .15rem; // 适当内边距
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  position: absolute;
  background: rgba(57, 110, 164);
  color: white;
  font-size: clamp(.125rem, 1.2vw, .175rem);
  font-weight: bold;
  border-radius: 6px;
  z-index: 20;
  transition: all 0.3s ease;
  cursor: pointer;
  transform: scale(0.9);
  opacity: 0.8;
  min-width: 50%;
  max-width: 50%;
  width: 8em;
  text-align: center;
  height: fit-content;
  word-break: break-all;
  /* 允许在任意字符间断行 */
}

.detection-label:hover {
  transform: scale(1.1) rotate(3deg);
  background: #1565c0;
  box-shadow: 0 .075rem .25rem rgba(25, 118, 210, 0.4);
  opacity: 1;
  animation: pulse 0.6s ease-in-out;
}

@keyframes pulse {
  0% {
    transform: scale(1.1) rotate(3deg);
  }

  50% {
    transform: scale(1.2) rotate(-2deg);
    box-shadow: 0 8px 25px rgba(25, 118, 210, 0.6);
  }

  100% {
    transform: scale(1.1) rotate(3deg);
  }
}

.detection-label.top-left {
  top: .1875rem;
  left: .1875rem;
}

.detection-label.top-right {
  top: .1875rem;
  right: .1875rem;
}

.detection-label.bottom-left {
  bottom: .9375rem;
  left: .1875rem;
}

.detection-label.bottom-right {
  bottom: .9375rem;
  left: .1875rem;
}

.new-algorithm {
  border: none;
  background: #183448;
  display: flex;
  align-items: center;
  justify-content: center;
}

.new-algorithm:hover {
  background: #183448;
  transform: scale(1.02);
}

.new-algorithm-content {
  text-align: center;
  color: white;
  transition: all 0.3s ease;
}

.new-algorithm:hover .new-algorithm-content {
  transform: scale(1.1);
}

.new-algorithm-text {
  font-size: clamp(.175rem, 2vw, .2rem);
}

/* 确保在小屏幕上也能正确显示 */
@media (max-height: 600px) {
  .algorithm-title {
    height: .625rem;
    font-size: clamp(.175rem, 2vw, .2rem);
  }

  .detection-label {
    // padding: 4px 8px;
    font-size: clamp(.1rem, 1vw, .15rem);
  }

  .detection-label.bottom-left,
  .detection-label.bottom-right {
    bottom: .6875rem;
  }

  .new-algorithm-text {
    font-size: clamp(.175rem, 2vw, .2rem);
  }

}

:deep(.q-btn) {
  font-size: .2rem !important;
}

.bottomArea {
  height: .25rem;
}
</style>
