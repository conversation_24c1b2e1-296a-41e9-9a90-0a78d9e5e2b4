<!--
 * @Author: Szc
 * @Date: 2025-08-20 14:32:23
 * @LastEditors: Szc
 * @LastEditTime: 2025-08-20 14:33:40
 * @Description: 👉生成数据集
-->
<template>
  <div class="content">
    <div class="top">
      <div class="left">
        <div class="one">
          <div class="onetop">
            <img src="../../assets/images/icon_dz.png" />
            <h2>{{ oneData.title }}</h2>
          </div>
          <div class="grid-layout">
            <div class="grid-row top-row">
              <div class="grid-item top-left">
                <div class="top-content">{{ oneData.topLeft.topContent }}</div>
                <div class="bottom-content">{{ oneData.topLeft.bottomContent }}</div>
              </div>
              <div class="grid-item top-right">
                <div class="top-content">{{ oneData.topRight.topContent }}</div>
                <div class="bottom-content">{{ oneData.topRight.bottomContent }}</div>
              </div>
            </div>
            <div class="grid-row bottom-row">
              <div class="grid-item bottom-left">
                <div class="top-content">{{ oneData.bottomLeft.topContent }}</div>
                <div class="bottom-content">{{ oneData.bottomLeft.bottomContent }}</div>
              </div>
              <div class="grid-item bottom-right">
                <div class="top-content">{{ oneData.bottomRight.topContent }}</div>
                <div class="bottom-content">{{ oneData.bottomRight.bottomContent }}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="two">
          <div class="onetop">
            <img src="../../assets/images/icon_dz.png" />
            <h2 class="available-title">{{ twoData.title }}</h2>
          </div>
          <div class="content-upper">
            <div class="category-title">{{ twoData.contentUpper.categoryTitle }}</div>
            <div class="category-content">
              <div class="color-ball" ref="ballEchartsRef"></div>
              <div class="container">
                <!-- 第一行 -->
                <template v-for="(row, rowIndex) in twoData.contentUpper.rows" :key="rowIndex">
                  <div class="row">
                    <template v-for="(item, itemIndex) in row" :key="itemIndex">
                      <div class="card">
                        <div class="percentage">{{ item.percentage }}</div>
                        <div class="content">
                          <div class="label">{{ item.label }}</div>
                        </div>
                      </div>
                    </template>
                  </div>
                </template>
              </div>
            </div>
          </div>
          <div class="content-lower">
            <div class="category-title">{{ twoData.contentLower.categoryTitle }}</div>
            <div ref="echartsRef" class="echarts-container"></div>
          </div>
        </div>
      </div>

      <div class="right">
        <div class="onetop">
          <img src="../../assets/images/icon_dz.png" />
          <h2>数据预览</h2>
        </div>
        <div class="container">
          <div class="data-count">共 {{ tableData.length }} 条数据</div>
          <div class="tables">
            <div class="table-header">
              <div class="header-cell">ID</div>
              <div class="header-cell">问题</div>
              <div class="header-cell">答案</div>
              <div class="header-cell">类别</div>
              <div class="header-cell">来源</div>
            </div>

            <div class="info-table">
              <div v-for="(row, index) in tableData" :key="index" class="table-row">
                <div class="cell">{{ row.id }}</div>
                <div class="cell">{{ row.question }}</div>
                <div class="cell">{{ row.answer }}</div>
                <div class="cell">{{ row.category }}</div>
                <div class="cell">{{ row.source }}</div>
              </div>
            </div>
          </div>
        </div>
        <!-- 底部下一步按钮 -->
        <div class="bottom">
          <div class="next">
            <q-btn class="prevBtn roundBox" color="grey-7" label="上一步" @click="prevStep" />
            <q-btn class="nextBtn roundBox" color="primary" label="完成" @click="nextStep" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
const emit = defineEmits(["next-step", "prev-step"]);

const nextStep = () => {
  emit("next-step");
};

const prevStep = () => {
  emit("prev-step");
};
import * as echarts from "echarts";

const oneData = ref({
  title: "数据集基本信息",
  topLeft: {
    topContent: "问答对数据集 V2.0",
    bottomContent: "数据集名称",
  },
  topRight: {
    topContent: "2024 / 03 / 15",
    bottomContent: "创建时间",
  },
  bottomLeft: {
    topContent: "125,776 条",
    bottomContent: "数据量",
  },
  bottomRight: {
    topContent: "大模型",
    bottomContent: "关联模型",
  },
});

const twoData = ref({
  title: "数据统计概览",
  contentUpper: {
    categoryTitle: "样本类别分布",
    rows: [
      [
        { percentage: "45%", label: "技术" },
        { percentage: "26%", label: "编程" },
      ],
      [
        { percentage: "18%", label: "财务" },
        { percentage: "6%", label: "生活" },
      ],
      [
        { percentage: "3%", label: "健康" },
        { percentage: "2%", label: "其他" },
      ],
    ],
  },
  contentLower: {
    categoryTitle: "数据来源分布",
  },
});

const tableData = ref([
  {
    id: 1,
    question: "如何处置路由器密码？",
    answer: "通常可以通过路由器背面的重置按钮或访问管理界面进行密码重置",
    category: "技术",
    source: "用户手册",
  },
  {
    id: 2,
    question: "Python中如何读取CSX文件？",
    answer: "可以使用pandas库的read_csv模块来读取csv文件",
    category: "编程",
    source: "官方文档",
  },
  {
    id: 3,
    question: "如何申请退税？",
    answer: "可以通过税务APP或前往当地税务局办理退税手续，需要准备以下...",
    category: "财务",
    source: "网站",
  },
  {
    id: 4,
    question: "如何煮出完美的水煮蛋？",
    answer: "将鸡蛋放入冷水中，水开后煮6-7分钟可获得溏心蛋，9-10分...",
    category: "生活",
    source: "用户生成",
  },
]);

const echartsRef = ref(null);

const initECharts = () => {
  const chartDom = echartsRef.value;
  if (chartDom) {
    const myChart = echarts.init(chartDom);
    const option = {
      xAxis: {
        type: "category",
        data: ["来源1", "来源2", "来源3", "来源4", "来源5"],
        axisLine: {
          show: true,
          lineStyle: {
            color: "#ccc", // 设置 x 轴边框线条颜色
            width: 0.5, // 减小 x 轴边框线条宽度
          },
        },
        axisTick: {
          show: true,
          lineStyle: {
            width: 0.5, // 减小 x 轴刻度线宽度
          },
        },
        axisPointer: {
          type: "shadow",
          shadowStyle: {
            color: "rgba(157, 157, 157, 0.1)", // 透明背景色
          },
          show: true,
        },
      },
      yAxis: {
        type: "value",
        interval: 1000,
        axisLine: {
          show: true,
          lineStyle: {
            color: "#ccc",
            width: 0.5, // 减小 y 轴边框线条宽度
          },
          right: "0%",
        },
        axisTick: {
          show: true,
          lineStyle: {
            width: 0.5, // 减小 y 轴刻度线宽度
          },
        },
        axisLabel: {
          color: "rgba(108, 121, 138, 1)", // 修改纵轴单位内容颜色
          show: true,
        },
        splitLine: {
          lineStyle: {
            color: "rgba(172, 207, 255, 1)", // 修改纵轴横线颜色
            type: "dashed", // 修改纵轴横线为虚线
          },
          show: true,
        },
        boundaryGap: [0, "100%"],
        onZero: false,
        splitNumber: 10,
        axisPointer: {
          show: false,
        },
      },
      series: [
        {
          data: [1200, 2300, 1800, 3500, 2800],
          type: "bar",
          label: {
            show: true,
            position: 'top',
            color: 'rgba(255,255,255,1)',
            fontSiza: '14px'
          },
          itemStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "rgba(16, 40, 87, 1)", // 柱形图上面颜色
                },
                {
                  offset: 1,
                  color: "rgba(19, 149, 244, 1)", // 柱形图下面颜色
                },
              ],
            },
            opacity: 1,
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowOffsetY: 5,
            shadowColor: "rgba(0, 0, 0, 0.2)",
          },
          emphasis: {
            itemStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(16, 40, 87, 1)",
                  },
                  {
                    offset: 1,
                    color: "rgba(19, 149, 244, 1)",
                  },
                ],
              },
              shadowColor: "rgba(157, 157, 157, 0.1)", // 鼠标悬停阴影颜色
              shadowBlur: 20,
            },
            axisPointer: {
              type: "shadow",
              shadowStyle: {
                color: "rgba(157, 157, 157, 0.1)",
              },
            },
          },
        },
      ],
    };
    myChart.setOption(option);
  }
};
const ballEchartsRef = ref(null);

const initBallECharts = () => {
  const chartDom = ballEchartsRef.value;
  if (chartDom) {
    const myChart = echarts.init(chartDom);
    const option = {
      backgroundColor: 'transparent',
      series: [
        {
          type: 'scatter',
          coordinateSystem: 'cartesian2d',
          symbolSize: 100,
          data: [[0, 0]],
          itemStyle: {
            color: 'rgba(75, 137, 250, 1)',
            shadowBlur: 30,
            shadowColor: 'rgba(75, 137, 250, 0.8)',
            shadowOffsetX: 0,
            shadowOffsetY: 0
          },
          animation: true,
          animationDuration: 1000,
          animationEasing: 'cubicInOut'
        }
      ],
      xAxis: {
        show: false,
        min: -1,
        max: 1
      },
      yAxis: {
        show: false,
        min: -1,
        max: 1
      },
      grid: {
        left: 0,
        right: 0,
        top: 0,
        bottom: 0
      }
    };
    myChart.setOption(option);
  }
};

onMounted(() => {
  initECharts();
  initBallECharts();

});
</script>

<style lang="scss" scoped>
.content {
  display: flex;
  flex: 1;

  .top {
    display: flex;
    flex: 1;
    min-height: 9.5rem;
    gap: 0.125rem;

    .left {
      display: flex;
      flex-direction: column;
      width: 25%;

      .one {
        height: 25%;
        margin-bottom: 0.125rem;
        border: 0.0125rem solid rgba(112, 112, 112, 1);
        padding: 0 0.25rem;
        background-color: #181a24;
        background-image: repeating-linear-gradient(135deg,
            rgba(255, 255, 255, 0.05) 0,
            rgba(255, 255, 255, 0.01) 0.05rem,
            transparent 0.0125rem,
            transparent 0.1875rem);
        display: flex;
        flex-direction: column;

        .grid-layout {
          flex: 1;
          display: flex;
          flex-direction: column;

          .grid-row {
            display: flex;
            flex: 1;
            height: 0.6rem;

            .grid-item {
              flex: 1;
              flex-direction: column;
              justify-content: space-around;
              align-items: center;
              height: 0.65rem;
              margin: 0.1rem 0.2rem 0 0;

              .top-content {
                color: #ffffff;
                font-size: 0.2rem;
                align-self: flex-start;
              }

              .bottom-content {
                color: #9cacc6;
                font-size: 0.2rem;
                align-self: flex-start;
                margin-bottom: 0.1rem;
              }
            }
          }

          .top-row {
            border-bottom: 0.0125rem solid rgba(112, 112, 112, 1);
          }

          .grid-item:not(:last-child) {
            border-right: 0.0125rem solid rgba(112, 112, 112, 1);
          }
        }
      }

      .one {
        margin-left: 0.125rem;
        position: relative;

        &::before {
          position: absolute;
          content: "";
          left: -0.1rem;
          top: 0;
          width: 0.025rem;
          height: 100%;
          background: rgba(156, 172, 198);
          border-radius: 0.125rem;
        }

        &::after {
          position: absolute;
          content: "";
          left: -0.075rem;
          top: 0;
          width: 0.025rem;
          height: 100%;
          background: rgba(156, 172, 198, 0.5);
        }
      }

      .two {
        height: 75%;
        border: 0.0125rem solid rgba(112, 112, 112, 1);
        padding: 0 0.25rem;
        background-color: #181a24;
        background-image: repeating-linear-gradient(135deg,
            rgba(255, 255, 255, 0.05) 0,
            rgba(255, 255, 255, 0.01) 0.05rem,
            transparent 0.0125rem,
            transparent 0.1875rem);

        .content-upper {
          display: flex;
          flex-direction: column;
          height: 30%;

          .category-title {
            margin-bottom: 0.125rem;
            color: rgba(206, 163, 69, 1);
          }

          .category-content {
            display: flex;

            .color-ball {
              width: 2rem;
              height: 2rem;
              border-radius: 50%;
              margin-right: 0.5rem;
            }

            .container {
              display: flex;
              flex-direction: column;
              gap: 0.375rem;
              flex-grow: 1;

              .row {
                display: flex;
                width: 2.5rem;
                height: 0.35rem;
                gap: 0.275rem;

                .card {
                  flex: 1;
                  padding: 0;
                  box-shadow: 0 0.05rem 0.1875rem rgba(0, 0, 0, 0.1);
                  display: flex;
                  flex-direction: column;
                  position: relative;
                  height: 0.4rem;

                  .percentage {
                    position: absolute;
                    top: -0.1rem;
                    left: 0.1rem;
                    font-size: 0.175rem;
                    font-weight: 800;
                    padding: 0 0.1rem;
                    height: 0.35rem;
                    display: flex;
                    // align-items: center;
                    justify-content: center;
                    color: white;
                    border-radius: 0.05rem;
                  }

                  .content {
                    margin-top: 0.125rem;

                    // position: absolute;
                    // top: 15px; // 调整位置
                    // left: 0; // 调整位置
                    // right: 0;
                    // padding: 5px; // 减小内边距
                    // height: 0.375rem;
                    // display: flex;
                    // align-items: center;
                    .label {
                      font-size: 0.15rem;
                      font-weight: 600;
                      padding-left: 0.225rem;
                      position: relative;
                      line-height: 1.2;
                      display: inline-flex;
                      align-items: center;
                      height: 0.25rem;
                      color: white;
                    }
                  }
                }

                /* 左侧边框 */
                .label::before {
                  content: "";
                  position: absolute;
                  left: 0;
                  top: 0;
                  bottom: 0;
                  width: 0.05rem;
                  border-radius: 0.025rem;
                  height: 100%;
                }
              }
            }

            /* 为每个卡片设置不同的背景颜色 */
            .container .row:nth-child(1) .card:nth-child(1) {
              background: rgba(75, 137, 250, 0.5);
            }

            .container .row:nth-child(1) .card:nth-child(1) .label::before {
              background-color: rgba(75, 137, 250, 1);
            }

            .container .row:nth-child(1) .card:nth-child(2) {
              background: rgba(14, 177, 80, 0.5);
            }

            .container .row:nth-child(1) .card:nth-child(2) .label::before {
              background-color: rgba(14, 177, 80, 1);
            }

            /* 第二行第一个卡片 - 财务 */
            .container .row:nth-child(2) .card:nth-child(1) {
              background: rgba(39, 166, 208, 0.5);
            }

            .container .row:nth-child(2) .card:nth-child(1) .label::before {
              background-color: rgba(39, 166, 208, 1);
            }

            /* 第二行第二个卡片 - 生活 */
            .container .row:nth-child(2) .card:nth-child(2) {
              background: rgba(38, 23, 176, 0.5);
            }

            .container .row:nth-child(2) .card:nth-child(2) .label::before {
              background-color: rgba(38, 23, 176, 1);
            }

            /* 第三行第一个卡片 - 健康 */
            .container .row:nth-child(3) .card:nth-child(1) {
              background: rgba(216, 105, 5, 0.5);
            }

            .container .row:nth-child(3) .card:nth-child(1) .label::before {
              background-color: rgba(216, 105, 5, 1);
            }

            /* 第三行第二个卡片 - 其他 */
            .container .row:nth-child(3) .card:nth-child(2) {
              background: rgba(230, 16, 16, 0.5);
            }

            .container .row:nth-child(3) .card:nth-child(2) .label::before {
              background-color: rgba(230, 16, 16, 1);
            }
          }
        }

        .content-lower {
          margin-top: 0.2rem;
          height: 70%;
          width: 100%;
          .category-title {
            color: rgba(206, 163, 69, 1);
          }

          .echarts-container {
            width: 100%;
            height: 3.75rem;
          }
        }
      }

      .two {
        margin-left: 0.125rem;
        position: relative;

        &::before {
          position: absolute;
          content: "";
          left: -0.1rem;
          top: 0;
          width: 0.025rem;
          height: 100%;
          background: rgba(156, 172, 198);
          border-radius: 0.125rem;
        }

        &::after {
          position: absolute;
          content: "";
          left: -0.075rem;
          top: 0;
          width: 0.025rem;
          height: 100%;
          background: rgba(156, 172, 198, 0.5);
        }
      }
    }

    .right {
      display: flex;
      flex-direction: column;
      width: 75%;
      padding: 0 0.25rem;
      border: 0.0125rem solid rgba(112, 112, 112, 1);
      background-color: #181a24;
      background-image: repeating-linear-gradient(135deg,
          rgba(255, 255, 255, 0.05) 0,
          rgba(255, 255, 255, 0.01) 0.05rem,
          transparent 0.0125rem,
          transparent 0.1875rem);

      .container {
        height: 90%;

        .data-count {
          color: white;
          margin-bottom: 0.15rem;
          margin-left: 0.4rem;
        }

        .tables {
          margin: 0.15rem 0.4rem;
          height: 100%;

          .table-header {
            display: flex;
            font-weight: bold;
            background: linear-gradient(to right, #216f6e 30%, #003366 70%);
            align-items: center;
            justify-content: center;
            height: 0.8rem;

            .header-cell {
              color: rgba(99, 212, 255, 1);
              height: 100%;
              line-height: 0.8rem;
              display: flex;
              align-items: center;

              &:nth-child(1),
              &:nth-child(4),
              &:nth-child(5) {
                width: 15%;
              }

              &:nth-child(2) {
                width: 20%;
              }

              &:nth-child(3) {
                width: 35%;
              }

              padding: 0.125rem;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }

          .info-table {
            height: 80%;
            overflow-y: auto;

            .table-row {
              display: flex;
              color: white;
              height: 0.8rem;
              line-height: 0.8rem;
              align-items: center;

              .cell {

                &:nth-child(1),
                &:nth-child(4),
                &:nth-child(5) {
                  width: 15%;
                }

                &:nth-child(2) {
                  width: 20%;
                }

                &:nth-child(3) {
                  width: 35%;
                }

                padding: 0.125rem;
                height: 100%;
                display: flex;
                align-items: center;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;

              }

              .action-icon {
                width: 0.2rem;
                height: 0.2rem;
                margin-right: 0.1rem;
                cursor: pointer;
              }
            }
          }
        }
      }
    }

    // 底部按钮
    .bottom {
      height: 0.8rem;
      display: flex;
      align-items: center;
      flex-shrink: 0;
      margin-top: auto;

      .next {
        display: flex;
        justify-content: space-between;
        width: 100%;
        padding: 0 0.25rem;
        gap: 0.25rem;

        .prevBtn {
          margin-right: auto;
        }

        .nextBtn {
          margin-left: auto;
        }
      }
    }
  }

  .right {
    position: relative;

    &::before {
      position: absolute;
      content: "";
      left: -0.1rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198);
      border-radius: 0.125rem;
    }

    &::after {
      position: absolute;
      content: "";
      left: -0.075rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198, 0.5);
    }
  }
}


.onetop {
  display: flex;
  align-items: center;

  h2 {
    height: 0.1875rem;
    line-height: 0.1875rem;
    color: #ffffff;
    font-size: 0.225rem;
  }

  img {
    width: 0.2rem;
    height: 0.2rem;
    margin-right: 0.1875rem;
  }
}
</style>
