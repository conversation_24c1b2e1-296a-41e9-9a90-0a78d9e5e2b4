import uuid
from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()


class RLTrainingTask(models.Model):
    """强化学习训练任务模型"""
    
    class StatusChoices(models.TextChoices):
        PENDING = 'pending', '等待中'
        RUNNING = 'running', '运行中'
        COMPLETED = 'completed', '已完成'
        FAILED = 'failed', '失败'
        CANCELLED = 'cancelled', '已取消'
    
    class RLTypeChoices(models.TextChoices):
        PPO = 'PPO', 'PPO'
        A3C = 'A3C', 'A3C'
        SAC = 'SAC', 'SAC'
        DDPG = 'DDPG', 'DDPG'
    
    training_id = models.CharField('训练ID', max_length=100, unique=True, default=uuid.uuid4)
    status = models.CharField('状态', max_length=20, choices=StatusChoices.choices, default=StatusChoices.PENDING)
    
    # 算法配置
    algorithm_name = models.CharField('算法名称', max_length=50, default='PPO')
    algorithm_version = models.CharField('算法版本', max_length=50, default='torch-1.8.1')
    rl_type = models.CharField('强化学习类型', max_length=20, choices=RLTypeChoices.choices, default=RLTypeChoices.PPO)
    
    # 训练进度
    current_episode = models.IntegerField('当前回合', default=0)
    total_episodes = models.IntegerField('总回合数', default=1000)
    estimated_duration = models.IntegerField('预估时长(秒)', null=True, blank=True)
    
    # 仿真配置
    data_source = models.CharField('数据源', max_length=100, default='内置仿真环境')
    use_external_env = models.BooleanField('使用外部环境', default=False)
    external_env_address = models.CharField('外部环境地址', max_length=255, blank=True, null=True)
    
    # 智能体配置
    sample_time = models.CharField('采样时间', max_length=20, default='0.1')
    action_space = models.CharField('动作空间', max_length=50, default='离散动作空间')
    observation_space = models.CharField('观测空间', max_length=50, default='连续观测空间')
    reward_function = models.CharField('奖励函数', max_length=50, default='标准奖励函数')
    
    # 超参数配置
    learning_rate = models.CharField('学习率', max_length=20, default='1e-5')
    epochs = models.IntegerField('训练轮数', default=5000)
    batch_size = models.IntegerField('批次大小', default=32)
    learning_rate_strategy = models.CharField('学习率策略', max_length=50, default='余弦衰减')
    compute_type = models.CharField('计算类型', max_length=20, default='fp32')
    validation_ratio = models.FloatField('验证比例', default=0.2)
    
    # 集群配置
    cpu_count = models.CharField('CPU数量', max_length=20, default='4')
    gpu_count = models.CharField('GPU数量', max_length=20, default='1')
    
    # 输出配置
    save_path = models.CharField('保存路径', max_length=255, default='/models/rl_training')
    save_name = models.CharField('保存名称', max_length=100, default='my_rl_model')
    
    # 元数据
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='创建者')
    created_time = models.DateTimeField('创建时间', auto_now_add=True)
    updated_time = models.DateTimeField('更新时间', auto_now=True)
    started_time = models.DateTimeField('开始时间', null=True, blank=True)
    completed_time = models.DateTimeField('完成时间', null=True, blank=True)
    
    class Meta:
        verbose_name = '强化学习训练任务'
        verbose_name_plural = verbose_name
        ordering = ['-created_time']


class RLTrainingMetrics(models.Model):
    """强化学习训练指标模型"""
    
    task = models.ForeignKey(RLTrainingTask, on_delete=models.CASCADE, related_name='metrics')
    episode = models.IntegerField('回合数')
    cumulative_reward = models.FloatField('累积奖励')
    average_reward = models.FloatField('平均奖励', null=True, blank=True)
    episode_length = models.IntegerField('回合长度', null=True, blank=True)
    exploration_rate = models.FloatField('探索率', null=True, blank=True)
    policy_loss = models.FloatField('策略损失', null=True, blank=True)
    value_loss = models.FloatField('价值损失', null=True, blank=True)
    entropy = models.FloatField('熵', null=True, blank=True)
    timestamp = models.DateTimeField('时间戳', auto_now_add=True)
    
    class Meta:
        verbose_name = '强化学习训练指标'
        verbose_name_plural = verbose_name
        ordering = ['episode']
        unique_together = ['task', 'episode']


class RLResourceMetrics(models.Model):
    """强化学习资源利用率指标模型"""
    
    task = models.ForeignKey(RLTrainingTask, on_delete=models.CASCADE, related_name='resource_metrics')
    cpu_utilization = models.FloatField('CPU利用率')
    npu_utilization = models.FloatField('NPU利用率', default=0.0)
    memory_utilization = models.FloatField('内存利用率')
    network_io = models.FloatField('网络IO', null=True, blank=True)
    disk_io = models.FloatField('磁盘IO', null=True, blank=True)
    timestamp = models.DateTimeField('时间戳', auto_now_add=True)
    
    class Meta:
        verbose_name = '强化学习资源指标'
        verbose_name_plural = verbose_name
        ordering = ['timestamp']


class RLTrainingConfig(models.Model):
    """强化学习训练配置模型"""
    
    config_id = models.CharField('配置ID', max_length=100, unique=True, default=uuid.uuid4)
    config_name = models.CharField('配置名称', max_length=100)
    description = models.TextField('描述', blank=True)
    
    # 配置JSON数据
    algorithm_config = models.JSONField('算法配置')
    simulation_config = models.JSONField('仿真配置')
    agent_config = models.JSONField('智能体配置')
    hyper_params_config = models.JSONField('超参数配置')
    cluster_config = models.JSONField('集群配置')
    output_config = models.JSONField('输出配置')
    
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='创建者')
    created_time = models.DateTimeField('创建时间', auto_now_add=True)
    updated_time = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '强化学习训练配置'
        verbose_name_plural = verbose_name
        ordering = ['-created_time']

    def __str__(self):
        return f"{self.config_name} ({self.config_id})"


class RLTrainingModel(models.Model):
    """强化学习训练模型信息"""

    class ModelStatusChoices(models.TextChoices):
        TRAINING = 'training', '训练中'
        COMPLETED = 'completed', '已完成'
        FAILED = 'failed', '失败'
        EXPORTED = 'exported', '已导出'

    # 关联的训练任务
    task = models.ForeignKey(RLTrainingTask, on_delete=models.CASCADE, related_name='models')

    # 模型基本信息
    model_name = models.CharField('模型名称', max_length=255)
    model_path = models.CharField('模型路径', max_length=1024, null=True, blank=True)
    checkpoint_path = models.CharField('检查点路径', max_length=1024, null=True, blank=True)

    # 模型性能指标
    best_episode_reward = models.FloatField('最佳回合奖励', default=0.0)
    average_episode_reward = models.FloatField('平均回合奖励', default=0.0)
    final_policy_loss = models.FloatField('最终策略损失', null=True, blank=True)
    final_value_loss = models.FloatField('最终价值损失', null=True, blank=True)
    final_entropy = models.FloatField('最终熵值', null=True, blank=True)

    # 训练信息
    training_iterations = models.IntegerField('训练迭代次数', default=0)
    total_episodes = models.IntegerField('总回合数', default=0)
    convergence_episode = models.IntegerField('收敛回合', null=True, blank=True)

    # 模型文件信息
    model_size_mb = models.FloatField('模型大小(MB)', default=0.0)
    checkpoint_size_mb = models.FloatField('检查点大小(MB)', default=0.0)

    # 算法信息
    algorithm_type = models.CharField('算法类型', max_length=20, default='PPO')
    framework = models.CharField('框架', max_length=50, default='Ray RLlib')

    # 模型状态
    status = models.CharField('状态', max_length=20, choices=ModelStatusChoices.choices, default=ModelStatusChoices.TRAINING)
    is_best = models.BooleanField('是否为最佳模型', default=False)

    # 导出信息
    exported_formats = models.JSONField('导出格式', default=list, blank=True, help_text='已导出的模型格式列表')
    export_paths = models.JSONField('导出路径', default=dict, blank=True, help_text='各种格式的导出路径')

    # 元数据
    metadata = models.JSONField('元数据', default=dict, blank=True, help_text='额外的模型信息')

    # 时间戳
    created_time = models.DateTimeField('创建时间', auto_now_add=True)
    updated_time = models.DateTimeField('更新时间', auto_now=True)
    completed_time = models.DateTimeField('完成时间', null=True, blank=True)

    class Meta:
        verbose_name = '强化学习训练模型'
        verbose_name_plural = verbose_name
        ordering = ['-created_time']
        unique_together = ['task', 'model_name']

    def __str__(self):
        return f"{self.model_name} - {self.algorithm_type} (奖励: {self.best_episode_reward:.2f})"

    @property
    def performance_summary(self):
        """获取性能摘要"""
        return {
            'best_episode_reward': self.best_episode_reward,
            'average_episode_reward': self.average_episode_reward,
            'final_policy_loss': self.final_policy_loss,
            'final_value_loss': self.final_value_loss,
            'final_entropy': self.final_entropy,
            'training_iterations': self.training_iterations,
            'total_episodes': self.total_episodes
        }

    def update_performance(self, metrics_data):
        """更新模型性能指标"""
        from django.utils import timezone
        for key, value in metrics_data.items():
            if hasattr(self, key) and value is not None:
                setattr(self, key, value)
        self.updated_time = timezone.now()
        self.save()

    def mark_as_best(self):
        """标记为最佳模型"""
        # 先将同一任务的其他模型标记为非最佳
        RLTrainingModel.objects.filter(task=self.task).update(is_best=False)
        # 标记当前模型为最佳
        self.is_best = True
        self.save()

    def add_export_format(self, format_name, export_path):
        """添加导出格式"""
        if format_name not in self.exported_formats:
            self.exported_formats.append(format_name)
        self.export_paths[format_name] = export_path
        self.save()