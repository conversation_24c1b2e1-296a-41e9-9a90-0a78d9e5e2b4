<template>
  <div class="training-workflow-main">
    <div class="main-content">
      <!-- 左侧内容区域 -->
      <div class="content-area">
        <router-view />
      </div>
      
      <!-- 右侧任务列表 -->
      <div class="task-list-area">
        <div class="task-list-header">
          <h6 class="q-ma-none">训练任务列表</h6>
        </div>
        <WorkflowTaskList
          ref="taskListRef"
          :selected-task-id="currentTaskId"
          @task-selected="onTaskSelected"
        />
      </div>
    </div>

    <!-- 创建任务对话框 -->
    <q-dialog v-model="showCreateDialog" persistent>
      <q-card style="min-width: 400px">
        <q-card-section>
          <div class="text-h6">创建训练任务</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-form @submit="createWorkflow" class="q-gutter-md">
            <q-input
              v-model="createForm.name"
              label="任务名称"
              outlined
              dense
              :rules="[val => !!val || '请输入任务名称']"
            />
            
            <q-input
              v-model="createForm.description"
              label="任务描述"
              type="textarea"
              outlined
              dense
              rows="3"
            />
            
            <q-select
              v-model="createForm.training_type"
              :options="trainingTypeOptions"
              label="训练类型"
              outlined
              dense
              emit-value
              map-options
              :rules="[val => !!val || '请选择训练类型']"
            />
            
            <q-select
              v-model="createForm.task_type"
              :options="taskTypeOptions"
              label="任务类型"
              outlined
              dense
              emit-value
              map-options
              :rules="[val => !!val || '请选择任务类型']"
            />
            
            <q-input
              v-model="createForm.model_name"
              label="模型名称"
              outlined
              dense
            />
          </q-form>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" @click="showCreateDialog = false" />
          <q-btn 
            color="primary" 
            label="创建" 
            :loading="creating"
            @click="createWorkflow" 
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 浮动创建按钮 -->
    <q-page-sticky position="bottom-right" :offset="[18, 18]">
      <q-btn
        fab
        icon="add"
        color="primary"
        @click="showCreateDialog = true"
      />
    </q-page-sticky>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useWorkflowStore } from 'src/stores/workflowStore'
import { TRAINING_TYPES, getPageRoute } from 'src/services/workflowApi'
import WorkflowTaskList from 'src/components/WorkflowTaskList.vue'

const router = useRouter()
const workflowStore = useWorkflowStore()

// 响应式数据
const showCreateDialog = ref(false)
const creating = ref(false)
const taskListRef = ref(null)

// 创建表单
const createForm = ref({
  name: '',
  description: '',
  training_type: '',
  task_type: '',
  model_name: ''
})

// 计算属性
const currentTaskId = computed(() => workflowStore.currentTaskId)

// 训练类型选项
const trainingTypeOptions = [
  { label: '深度学习', value: TRAINING_TYPES.DEEP_LEARNING },
  { label: '强化学习', value: TRAINING_TYPES.REINFORCEMENT_LEARNING },
  { label: '大模型', value: TRAINING_TYPES.LARGE_MODEL }
]

// 任务类型选项 (根据训练类型动态变化)
const taskTypeOptions = computed(() => {
  const options = {
    [TRAINING_TYPES.DEEP_LEARNING]: [
      { label: '目标识别-YoloV8', value: 'object_detection_yolov8' },
      { label: '目标识别-YoloV5', value: 'object_detection_yolov5' },
      { label: '图像分类', value: 'image_classification' },
      { label: '语义分割', value: 'semantic_segmentation' },
      { label: '实例分割', value: 'instance_segmentation' }
    ],
    [TRAINING_TYPES.REINFORCEMENT_LEARNING]: [
      { label: 'DQN', value: 'dqn' },
      { label: 'PPO', value: 'ppo' },
      { label: 'A3C', value: 'a3c' },
      { label: 'DDPG', value: 'ddpg' },
      { label: 'SAC', value: 'sac' }
    ],
    [TRAINING_TYPES.LARGE_MODEL]: [
      { label: 'LLM微调', value: 'llm_fine_tuning' },
      { label: '文本生成', value: 'text_generation' },
      { label: '文本分类', value: 'text_classification' },
      { label: '问答系统', value: 'question_answering' },
      { label: '文本摘要', value: 'text_summarization' }
    ]
  }
  return options[createForm.value.training_type] || []
})

/**
 * 创建工作流
 */
const createWorkflow = async () => {
  try {
    creating.value = true
    
    // 验证表单
    if (!createForm.value.name || !createForm.value.training_type || !createForm.value.task_type) {
      return
    }
    
    // 创建工作流
    const result = await workflowStore.createWorkflow({
      name: createForm.value.name,
      description: createForm.value.description,
      training_type: createForm.value.training_type,
      task_type: createForm.value.task_type,
      model_name: createForm.value.model_name || createForm.value.name
    })
    
    // 关闭对话框
    showCreateDialog.value = false
    
    // 重置表单
    createForm.value = {
      name: '',
      description: '',
      training_type: '',
      task_type: '',
      model_name: ''
    }
    
    // 刷新任务列表
    if (taskListRef.value) {
      taskListRef.value.refresh()
    }
    
    // 跳转到第一步
    const firstStepRoute = getPageRoute(result.training_type, 1)
    if (firstStepRoute) {
      await router.push(firstStepRoute)
    }
    
  } catch (error) {
    console.error('创建工作流失败:', error)
  } finally {
    creating.value = false
  }
}

/**
 * 任务选中事件
 */
const onTaskSelected = (task) => {
  console.log('选中任务:', task)
}

// 组件挂载时的初始化
onMounted(() => {
  // 可以在这里进行一些初始化操作
})
</script>

<style scoped>
.training-workflow-main {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.content-area {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.task-list-area {
  width: 350px;
  border-left: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
}

.task-list-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f5f5f5;
}
</style>
