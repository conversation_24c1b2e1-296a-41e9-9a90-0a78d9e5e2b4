{"name": "rltp", "version": "0.0.1", "description": "Reinforcement learning training platform", "productName": "模型训练环境", "author": "yebinbin <<EMAIL>>", "private": true, "scripts": {"lint": "eslint --ext .js,.vue ./", "format": "prettier --write \"**/*.{js,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build": "quasar build"}, "dependencies": {"@dataview/datav-vue3": "^0.0.0-test.1672506674342", "@quasar/extras": "^1.16.4", "@vue-flow/core": "^1.45.0", "@xterm/addon-fit": "^0.9.0-beta.1", "animate.css": "^4.1.1", "axios": "^1.9.0", "echarts": "^5.4.3", "lodash": "^4.17.21", "monaco-editor": "^0.44.0", "pinia": "^2.3.1", "pinia-plugin-persistedstate": "^3.2.3", "quasar": "^2.6.0", "vue": "^3.0.0", "vue-router": "^4.0.0", "vue3-draggable-resizable": "^1.6.5", "xterm": "^5.3.0"}, "devDependencies": {"@element-plus/icons-vue": "^2.3.1", "@quasar/app-vite": "^1.3.0", "autoprefixer": "^10.4.2", "element-plus": "^2.10.2", "eslint": "^8.10.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-vue": "^9.0.0", "postcss": "^8.4.14", "prettier": "^2.5.1", "unplugin-element-plus": "^0.10.0", "vite-plugin-monaco-editor": "^1.1.0", "vite-svg-loader": "^5.1.0"}, "engines": {"node": "^18 || ^16 || ^14.19", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}