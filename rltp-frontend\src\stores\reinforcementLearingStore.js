import { defineStore } from 'pinia'
import { ref } from 'vue'

export const usereinforcementLearingStore = defineStore('stepOne', () => {
  // 第一步数据 - 数据集信息
  const stepOneData = ref({
    taskId: null,
    scenarios: [
      {
        title: '城市反恐作战',
        status: '困难',
        environmenttype: '城市环境',
        info: '在复杂城市环境中执行反恐任务，需要精确打击并保护平民。',
        detailinfo: {
          detaillocation: '高楼密集区',
          detailtime: '2小时',
          detailour: '我方：14人',
          detailenemy: '敌方：200人',
        },
        addtime: '2025 /01 /01',
      },
      {
        title: '空中拦截作战',
        status: '困难',
        environmenttype: '空中环境',
        info: '执行空中拦截任务，阻止敌机侵入领空',
        detailinfo: {
          detaillocation: '高楼密集区',
          detailtime: '2小时',
          detailour: '我方：14人',
          detailenemy: '敌方：200人',
        },
        addtime: '2025 /01 /01',
      },
    ],
    scenariodetail: {
      status: '困难',
      type: '空中拦截作战',
      info: '执行空中拦截任务，阻止敌机侵入领空。',
      detailinfo: [
        {
          type: 'Environment',
          info: '空中环境',
        },
        {
          type: 'Terrain',
          info: '高空地形',
        },
        {
          type: 'Weather',
          info: '晴天',
        },
      ],
    },
    forceComparison: [
      {
        type: 0,
        totalSum: 2,
        name: '炮兵营',
        fleets: [
          {
            name: '炮1',
            status: false,
            type: [
              {
                name: '70毫米炮弹',
                count: 3,
              },
              {
                name: '280毫米炮弹',
                count: 2,
              },
              {
                name: '150毫米炮弹',
                count: 2,
              },
            ],
          },
          {
            name: '炮2',
            status: false,
            type: [
              {
                name: '70毫米炮弹',
                count: 3,
              },
              {
                name: '280毫米炮弹',
                count: 2,
              },
              {
                name: '150毫米炮弹',
                count: 2,
              },
            ],
          },
        ],
      },
      {
        type: 1,
        totalSum: 10,
        name: '时敏目标',
        fleets: [
          {
            name: '时敏目标1',
            count: 2,
          },
          {
            name: '时敏目标2',
            count: 2,
          },
          {
            name: '时敏目标3',
            count: 2,
          },
          {
            name: '时敏目标4',
            count: 2,
          },
          {
            name: '时敏目标5',
            count: 2,
          },
        ],
      },
    ],
    missionObjectives: [
      { title: '保护南路' },
      { title: '距离海盗' },
      { title: '维护航道安全' },
      { title: '维护航道安全' },
      { title: '维护航道安全' },
    ],
    detailedInfo: {
      Ourforces: '4',
      Enemyforces: '6',
      TroopRatio: '0.7:1',
      AddTime: '2025/01/20',
      SceneID: '92.0%',
    },
  })
  // 第二步：模型参数配置
  const stepTwoData = ref({
    taskId: null,
    params: {
      epochs: '5',
      batchSize: '5',
      optimizer: 'Adam',
      learningRate: '1e-5',
      warmupSteps: '1000',
      logInterval: '100',
      evalInterval: '10',
    },
    resources: {
      npuCount: '1',
      cpuCount: '16',
    },
  })
  // 第三步：训练执行与监控
  const stepThreeData = ref({
    trainingStatus: 'not_started',
    trainingProgress: 0,
    currentEpoch: 0,
    totalEpochs: 0,
    trainingMetrics: {
      loss: [],
      accuracy: [],
      learningRate: [],
      epochs: [],
    },
    taskId: null,
    isTraining: false,
  })

  // 第四步：模型性能对比
  const stepFourData = ref({
    models: [],
    selectedModelId: null,
    comparedModels: [],
    conversionTasks: {},
  })

  // 第五步：模型部署推理
  const stepFiveData = ref({
    selectedModel: null,
    isDeployed: false,
    chatHistory: [],
  })
  // 更新各步骤数据的方法
  function updateStepOneData(data) {
    stepOneData.value = { ...stepOneData.value, ...data }
    console.log('大模型-第一步数据已更新:', stepOneData.value)
  }

  function updateStepTwoData(data) {
    stepTwoData.value = { ...stepTwoData.value, ...data }
    console.log('大模型-第二步数据已更新:', stepTwoData.value)
  }

  function updateStepThreeData(data) {
    stepThreeData.value = { ...stepThreeData.value, ...data }
    console.log('大模型-第三步数据已更新:', stepThreeData.value)
  }

  function updateStepFourData(data) {
    stepFourData.value = { ...stepFourData.value, ...data }
    console.log('大模型-第四步数据已更新:', stepFourData.value)
  }

  function updateStepFiveData(data) {
    stepSixData.value = { ...stepFiveData.value, ...data }
    console.log('大模型-第五步数据已更新:', stepFiveData.value)
  }

  // 重置所有数据
  function resetAllData() {
    stepOneData.value = {
      scenarios: [
        {
          title: '城市反恐作战',
          status: '困难',
          environmenttype: '城市环境',
          info: '在复杂城市环境中执行反恐任务，需要精确打击并保护平民。',
          detailinfo: {
            detaillocation: '高楼密集区',
            detailtime: '2小时',
            detailour: '我方：14人',
            detailenemy: '敌方：200人',
          },
          addtime: '2025 /01 /01',
        },
        {
          title: '空中拦截作战',
          status: '困难',
          environmenttype: '空中环境',
          info: '执行空中拦截任务，阻止敌机侵入领空',
          detailinfo: {
            detaillocation: '高楼密集区',
            detailtime: '2小时',
            detailour: '我方：14人',
            detailenemy: '敌方：200人',
          },
          addtime: '2025 /01 /01',
        },
      ],
      scenariodetail: {
        status: '困难',
        type: '空中拦截作战',
        info: '执行空中拦截任务，阻止敌机侵入领空。',
        detailinfo: [
          {
            type: 'Environment',
            info: '空中环境',
          },
          {
            type: 'Terrain',
            info: '高空地形',
          },
          {
            type: 'Weather',
            info: '晴天',
          },
        ],
      },
      forceComparison: [
        {
          type: 0,
          totalSum: 2,
          name: '炮兵营',
          fleets: [
            {
              name: '炮1',
              status: false,
              type: [
                {
                  name: '70毫米炮弹',
                  count: 3,
                },
                {
                  name: '280毫米炮弹',
                  count: 2,
                },
                {
                  name: '150毫米炮弹',
                  count: 2,
                },
              ],
            },
            {
              name: '炮2',
              status: false,
              type: [
                {
                  name: '70毫米炮弹',
                  count: 3,
                },
                {
                  name: '280毫米炮弹',
                  count: 2,
                },
                {
                  name: '150毫米炮弹',
                  count: 2,
                },
              ],
            },
          ],
        },
        {
          type: 1,
          totalSum: 10,
          name: '时敏目标',
          fleets: [
            {
              name: '时敏目标1',
              count: 2,
            },
            {
              name: '时敏目标2',
              count: 2,
            },
            {
              name: '时敏目标3',
              count: 2,
            },
            {
              name: '时敏目标4',
              count: 2,
            },
            {
              name: '时敏目标5',
              count: 2,
            },
          ],
        },
      ],
      missionObjectives: [
        { title: '保护南路' },
        { title: '距离海盗' },
        { title: '维护航道安全' },
        { title: '维护航道安全' },
        { title: '维护航道安全' },
      ],
      detailedInfo: {
        Ourforces: '4',
        Enemyforces: '6',
        TroopRatio: '0.7:1',
        AddTime: '2025/01/20',
        SceneID: '92.0%',
      },
    }
    stepTwoData.value = {
      params: {
        epochs: '5',
        batchSize: '5',
        optimizer: 'Adam',
        learningRate: '1e-5',
        warmupSteps: '1000',
        logInterval: '100',
        evalInterval: '10',
      },
      resources: {
        npuCount: '1',
        cpuCount: '16',
      },
    }
    stepThreeData.value = {
      trainingStatus: 'not_started',
      trainingProgress: 0,
      currentEpoch: 0,
      totalEpochs: 0,
      trainingMetrics: {
        loss: [],
        accuracy: [],
        learningRate: [],
        epochs: [],
      },
      taskId: null,
      isTraining: false,
    }
    stepFourData.value = {
      models: [],
      selectedModelId: null,
      comparedModels: [],
      conversionTasks: {},
    }
    stepFiveData.value = {
      selectedModel: null,
      isDeployed: false,
      chatHistory: [],
    }
    console.log('大模型-所有数据已重置')
  }

  // 获取完整的表单数据
  function getFormData() {
    return {
      stepOne: stepOneData.value,
      stepTwo: stepTwoData.value,
      stepThree: stepThreeData.value,
      stepFour: stepFourData.value,
      stepFive: stepFive.value,
    }
  }

  return {
    // 数据
    stepOneData,
    stepTwoData,
    stepThreeData,
    stepFourData,
    stepFiveData,

    // 方法
    updateStepOneData,
    updateStepTwoData,
    updateStepThreeData,
    updateStepFourData,
    updateStepFiveData,
    resetAllData,
    getFormData,
  }
})
