#!/usr/bin/env python3
"""
简化版强化学习训练脚本 - 专门用于解决NPU环境问题
"""

import os
import sys
import json
import time
import signal
import argparse
from datetime import datetime

# 设置环境变量，避免多进程问题
os.environ["RAY_DISABLE_IMPORT_WARNING"] = "1"
os.environ["RAY_DEDUP_LOGS"] = "0"
os.environ["PYTHONPATH"] = "/workspace"

import ray
from ray import tune
from ray.rllib.algorithms.ppo import PPOConfig

# 华为NPU支持
try:
    import torch_npu
    NPU_AVAILABLE = True
    print("✅ 华为NPU支持已加载")
except ImportError:
    NPU_AVAILABLE = False
    print("⚠️ 华为NPU支持未找到，将使用CPU训练")

# 全局退出标志
graceful_exit = False

def signal_handler(signum, frame):
    """优雅退出处理器"""
    global graceful_exit
    print(f"\n🛑 收到退出信号 {signum}")
    graceful_exit = True
    
    try:
        if ray.is_initialized():
            ray.shutdown()
    except:
        pass
    
    sys.exit(0)

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='简化版强化学习训练')
    parser.add_argument('--config', type=str, required=True, help='配置文件路径')
    parser.add_argument('--task_id', type=str, required=True, help='任务ID')
    parser.add_argument('--use-npu', action='store_true', help='使用NPU')
    parser.add_argument('--resume', type=str, default='False', help='是否继续训练')
    return parser.parse_args()

def load_config(config_path):
    """加载训练配置"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载配置失败: {e}")
        return None

def create_simple_config(use_npu=False):
    """创建简化的训练配置"""
    config = (
        PPOConfig()
        .environment("Pendulum-v1")
        .framework("torch")
        .env_runners(
            num_env_runners=1,  # 最小化env_runners
            rollout_fragment_length=200,
            num_envs_per_env_runner=1,
            num_cpus_per_env_runner=1,
            max_env_runner_restarts=0,  # 禁用重启
        )
        .learners(
            num_learners=1,
            num_cpus_per_learner=1,
            num_gpus_per_learner=0,
            custom_resources_per_learner={"NPU": 1} if use_npu else {}
        )
        .training(
            gamma=0.99,
            lr=0.0001,
            train_batch_size=512,
            model={
                "fcnet_hiddens": [128, 128],  # 简化网络
                "fcnet_activation": "relu",
            },
            lambda_=0.95,
            clip_param=0.2,
            entropy_coeff=0.01,
            num_sgd_iter=5,  # 减少SGD迭代
            minibatch_size=64,
        )
        .evaluation(
            evaluation_interval=50,  # 减少评估频率
            evaluation_duration=5,
            evaluation_num_workers=0,  # 禁用评估worker
        )
    )
    
    return config

def main():
    """主函数"""
    args = parse_args()
    
    print("🚀 启动简化版强化学习训练")
    print(f"📋 任务ID: {args.task_id}")
    print(f"🔧 使用NPU: {args.use_npu}")
    
    # 加载配置
    training_config = load_config(args.config)
    if not training_config:
        sys.exit(1)
    
    # 获取训练参数
    hyper_params = training_config.get('hyperParams', {})
    training_iterations = int(hyper_params.get('epochs', 100))
    
    print(f"🎯 训练轮数: {training_iterations}")
    
    # NPU环境设置
    if args.use_npu and NPU_AVAILABLE:
        os.environ["ASCEND_RT_VISIBLE_DEVICES"] = "0"
        print("🔧 NPU环境已配置")
    
    # 初始化Ray（简化配置）
    try:
        if ray.is_initialized():
            ray.shutdown()
        
        ray_config = {
            "ignore_reinit_error": True,
            "include_dashboard": False,
            "log_to_driver": False,
            "_temp_dir": "/tmp/ray_simple",
        }
        
        if args.use_npu and NPU_AVAILABLE:
            ray_config["resources"] = {"NPU": 1}
        
        ray.init(**ray_config)
        print("✅ Ray初始化成功")
        
    except Exception as e:
        print(f"❌ Ray初始化失败: {e}")
        sys.exit(1)
    
    try:
        # 创建训练配置
        config = create_simple_config(use_npu=args.use_npu and NPU_AVAILABLE)
        
        # 实验名称
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        experiment_name = f"PPO-simple-{args.task_id}-{timestamp}"
        
        print(f"🏃 开始训练: {experiment_name}")
        
        # 检查退出标志
        if graceful_exit:
            print("🛑 检测到退出信号")
            return
        
        # 运行训练
        analysis = tune.run(
            "PPO",
            config=config,
            stop={"training_iteration": training_iterations},
            checkpoint_freq=20,
            checkpoint_at_end=True,
            storage_path="/tmp/ray_results",
            name=experiment_name,
            verbose=1,
            max_failures=1,
            max_concurrent_trials=1,
        )
        
        print("✅ 训练完成")
        
        # 保存模型信息
        try:
            model_info = {
                "model_name": f"simple_rl_model_{timestamp}",
                "model_path": f"/tmp/ray_results/{experiment_name}",
                "algorithm_type": "PPO",
                "training_iterations": training_iterations,
                "device_type": "npu" if (args.use_npu and NPU_AVAILABLE) else "cpu",
                "task_id": args.task_id,
                "status": "completed",
                "created_time": datetime.now().isoformat(),
                "framework": "ray_rllib",
                "training_type": "reinforcement_learning"
            }
            
            # 保存到文件
            with open("/workspace/rl_model_info.json", "w", encoding='utf-8') as f:
                json.dump(model_info, f, ensure_ascii=False, indent=2)
            
            print("✅ 模型信息已保存")
            
        except Exception as e:
            print(f"⚠️ 保存模型信息失败: {e}")
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
        
    finally:
        # 清理资源
        try:
            if ray.is_initialized():
                ray.shutdown()
            print("🧹 资源清理完成")
        except:
            pass
        
        print("🎉 训练脚本执行完毕")

if __name__ == "__main__":
    main()
