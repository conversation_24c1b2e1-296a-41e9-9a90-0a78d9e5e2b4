import numpy as np
from skimage.transform import resize


def rgb2gray(state):
    # 使用加权平均法将 RGB 转换为灰度图像
    gray_state = np.dot(state[..., :3], [0.299, 0.587, 0.114])
    return gray_state  # 返回 210x160 的灰度图像

def preprocess(state):
    gray_state = rgb2gray(state)  # 灰度化
    resized_state = resize(gray_state, (84, 84))  # 缩放到 84x84 尺寸
    return resized_state  # 返回 84x84 的缩放灰度图

def normalize(state):
    return state / 255.0  # 将像素值从 [0, 255] 归一化为 [0, 1]

def flatten(state):
    return state.flatten()  # 将 84x84 的图像展平成 (7056,) 的一维向量


def preprocess_state(state):
    """
    对状态进行预处理，包括灰度化、缩放、标准化。
    
    参数:
    - state: 输入的状态 (210, 160, 3) 的 RGB 图像

    返回:
    - 处理后的状态图像，(84, 84) 的灰度图像
    """
    # gray_state = rgb2gray(state)        # 灰度化
    resized_state = resize(state, (84, 84, 3))  # 缩放到 84x84x3
    normalized_state = normalize(resized_state)  # 归一化
    # flattened_state = flatten(normalized_state)  # 展平成一维向量
    return normalized_state  # 返回 7056 维向量
