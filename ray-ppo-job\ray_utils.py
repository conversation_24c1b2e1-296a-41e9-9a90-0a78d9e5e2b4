
import yaml
from kubernetes import client, config
from kubernetes.client.rest import ApiException


def submit_ray_job(
                   # entrypoint: str = '/bin/bash -c /root/closeaircombat/scripts/train_vsbaseline-aim.sh',
                   entrypoint: str = 'python /root/ray-gym/train.py',

                   run_image: str = 'core.**************.nip.io:30670/public/ray-gym-cartpole:latest',
                   ray_version: str = '2.7.0',
                   actor_worker_count: int = 10,
                   actor_cpu_num: int = 1,
                   actor_gpu_num: int = 0,
                   actor_mem_num: str = '2Gi',
                   learner_worker_count: int = 1,
                   learner_cpu_num: int = 4,
                   learner_gpu_num: int = 1,
                   learner_mem_num: str = '4Gi',
                   namespace: str = 'rl-platform',
                   ray_job_name: str = 'ray-job',
                   job_name: str = 'rayjob-example',
                   volume_name: str = 'shared-volume',
                   volume_claim_name: str = 'common-pvc',
                   mount_path: str = '/mnt/shared'):
    # if env.bool('DJANGO_PRD', default=False):
    #     config_file = "config/kubeconfig-prd.yaml"
    # else:
    #     config_file = "config/kubeconfig-dev.yaml"
    # 加载Kubernetes配置
    # config.load_kube_config(config_file=config_file)
    config.load_kube_config()

    # 创建Kubernetes API实例
    api_instance = client.CustomObjectsApi()

    # 定义RayCluster YAML
    ray_job_yaml = {
        "apiVersion": "ray.io/v1",
        "kind": "RayJob",
        "metadata": {
            "name": ray_job_name,
            "namespace": namespace
        },
        "spec": {
            "entrypoint": entrypoint +' --num_actors 10 --actor_cpu_num 1 --actor_gpu_num 0 --actor_mem_num 2 --learner_cpu_num 4 --learner_gpu_num 1 --learner_mem_num 4',
            "rayClusterSpec": {
                "rayVersion": ray_version,
                "headGroupSpec": {
                    "rayStartParams": {
                        "dashboard-host": "0.0.0.0"
                    },
                    "template": {
                        "spec": {
                            "containers": [
                                {
                                    "name": "ray-head",
                                    "image": run_image,
                                    "ports": [
                                        {"containerPort": 6379, "name": "gcs-server"},
                                        {"containerPort": 8265, "name": "dashboard"},
                                        {"containerPort": 10001, "name": "client"}
                                    ],
                                    "resources": {
                                        "limits": {"cpu": "1"},
                                        "requests": {"cpu": "200m"}
                                    },
                                    "volumeMounts": [
                                        {
                                            "name": volume_name,
                                            "mountPath": mount_path
                                        }
                                    ]
                                }
                            ],
                            "volumes": [
                                {
                                    "name": volume_name,
                                    "persistentVolumeClaim": {
                                        "claimName": volume_claim_name
                                    }
                                }
                            ]
                        }
                    }
                },
                "workerGroupSpecs": [
                    {
                        "replicas": learner_worker_count,
                        "groupName": "gpu-group",
                        "rayStartParams": {},
                        "template": {
                            "spec": {
                                "containers": [
                                    {
                                        "name": "ray-worker",
                                        "image": run_image,
                                        "imagePullPolicy": "IfNotPresent",
                                        "resources": {
                                            "limits": {
                                                "cpu": learner_cpu_num,
                                                "memory": learner_mem_num,
                                                "nvidia.com/gpu": learner_gpu_num
                                            },
                                            "requests": {
                                                "cpu": learner_cpu_num,
                                                "memory": learner_mem_num,
                                                "nvidia.com/gpu": learner_gpu_num
                                            }
                                        },
                                        "volumeMounts": [
                                            {
                                                "name": volume_name,
                                                "mountPath": mount_path
                                            }
                                        ]
                                    }
                                ],
                                "volumes": [
                                    {
                                        "name": volume_name,
                                        "persistentVolumeClaim": {
                                            "claimName": volume_claim_name
                                        }
                                    }
                                ]
                            }
                        }
                    },
                    {
                        "replicas": actor_worker_count +3,
                        "groupName": "cpu-group",
                        "rayStartParams": {},
                        "template": {
                            "spec": {
                                "affinity": {
                                    "nodeAffinity": {
                                        "requiredDuringSchedulingIgnoredDuringExecution": {
                                            "nodeSelectorTerms": [
                                                {
                                                    "matchExpressions": [
                                                        {"key": "nvidia.com/gpu", "operator": "DoesNotExist"}
                                                    ]
                                                }
                                            ]
                                        }
                                    }
                                },
                                "containers": [
                                    {
                                        "name": "ray-worker",
                                        "image": run_image,
                                        "imagePullPolicy": "IfNotPresent",
                                        "resources": {
                                            "limits": {
                                                "cpu": actor_cpu_num,
                                                "memory": actor_mem_num,
                                                "nvidia.com/gpu": actor_gpu_num
                                            },
                                            "requests": {
                                                "cpu": actor_cpu_num,
                                                "memory": actor_mem_num,
                                                "nvidia.com/gpu": actor_gpu_num
                                            }
                                        },
                                        "volumeMounts": [
                                            {
                                                "name": volume_name,
                                                "mountPath": mount_path
                                            }
                                        ]
                                    }
                                ],
                                "volumes": [
                                    {
                                        "name": volume_name,
                                        "persistentVolumeClaim": {
                                            "claimName": volume_claim_name
                                        }
                                    }
                                ]
                            }
                        }
                    }
                ]
            }
        }
    }

    # 将RayCluster YAML转化为字符串
    ray_cluster_yaml_str = yaml.dump(ray_job_yaml)

    # 解析 YAML 字符串为 Kubernetes 对象
    ray_cluster = yaml.safe_load(ray_cluster_yaml_str)

    # 创建 RayCluster
    try:
        api_response = api_instance.create_namespaced_custom_object(
            group="ray.io",
            version="v1",
            namespace=ray_cluster["metadata"]["namespace"],
            plural="rayjobs",
            body=ray_cluster
        )
        print(api_response)
        print("RayCluster created successfully!")
    except ApiException as e:
        print(f"Exception when creating RayCluster: {e}")

if __name__ == '__main__':
    submit_ray_job()