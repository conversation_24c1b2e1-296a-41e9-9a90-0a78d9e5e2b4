#!/usr/bin/env python3
"""
强化学习模型信息导入工具
用于读取训练完成后的模型信息文件并导入数据库
"""

import os
import json
import logging
from datetime import datetime
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RLModelImporter:
    """强化学习模型信息导入器"""
    
    def __init__(self):
        self.model_info_file = "/workspace/rl_model_info.json"
    
    def get_rl_model_info(self):
        """
        获取强化学习模型信息
        
        Returns:
            list: 模型信息列表
        """
        try:
            if not os.path.exists(self.model_info_file):
                logger.warning(f"强化学习模型信息文件不存在: {self.model_info_file}")
                return []
            
            models_info = []
            with open(self.model_info_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:
                    logger.warning("强化学习模型信息文件为空")
                    return []
                
                # 处理多行JSON格式（每行一个JSON对象）
                for line in content.split('\n'):
                    if line.strip():
                        try:
                            model_info = json.loads(line)
                            models_info.append(model_info)
                        except json.JSONDecodeError as e:
                            logger.error(f"解析模型信息行失败: {line}, 错误: {e}")
                            continue
            
            logger.info(f"成功读取 {len(models_info)} 个强化学习模型信息")
            return models_info
            
        except Exception as e:
            logger.error(f"读取强化学习模型信息失败: {e}")
            return []
    
    def save_rl_models_to_db(self, training_task):
        """
        将强化学习模型信息保存到数据库
        
        Args:
            training_task: 训练任务实例
            
        Returns:
            bool: 保存成功返回True，否则返回False
        """
        try:
            # 导入模型
            from backend_api.models.rl_training import RLTrainingModel
            from django.utils import timezone
            
            # 获取模型信息
            models_data = self.get_rl_model_info()
            if not models_data:
                logger.warning("没有强化学习模型数据可保存")
                return False
            
            saved_count = 0
            
            # 处理每个模型信息
            for model_info in models_data:
                # 检查是否属于当前训练任务
                if model_info.get('task_id') != str(training_task.training_id):
                    continue
                
                # 检查模型是否已存在
                model_name = model_info.get('model_name', '')
                existing_model = RLTrainingModel.objects.filter(
                    task=training_task,
                    model_name=model_name
                ).first()
                
                # 提取模型信息
                model_path = model_info.get('model_path', '')
                checkpoint_path = model_info.get('checkpoint_path', '')
                algorithm_type = model_info.get('algorithm_type', 'PPO')
                training_iterations = model_info.get('training_iterations', 0)
                final_reward = model_info.get('final_reward', 0.0)
                training_time = model_info.get('training_time', 0.0)
                hyperparameters = model_info.get('hyperparameters', {})
                environment = model_info.get('environment', 'Unknown')
                device_type = model_info.get('device_type', 'cpu')
                is_best = model_info.get('is_best', True)
                
                if existing_model:
                    # 更新现有模型记录
                    existing_model.model_path = model_path
                    existing_model.checkpoint_path = checkpoint_path
                    existing_model.algorithm_type = algorithm_type
                    existing_model.training_iterations = training_iterations
                    existing_model.final_reward = final_reward
                    existing_model.training_time = training_time
                    existing_model.hyperparameters = hyperparameters
                    existing_model.environment = environment
                    existing_model.device_type = device_type
                    existing_model.is_best = is_best
                    existing_model.status = 'completed'
                    existing_model.completed_time = timezone.now()
                    existing_model.metadata = model_info
                    existing_model.updated_time = timezone.now()
                    existing_model.save()
                    logger.info(f"更新强化学习模型记录: {model_name}")
                else:
                    # 创建新模型记录
                    RLTrainingModel.objects.create(
                        task=training_task,
                        model_name=model_name,
                        model_path=model_path,
                        checkpoint_path=checkpoint_path,
                        algorithm_type=algorithm_type,
                        training_iterations=training_iterations,
                        final_reward=final_reward,
                        training_time=training_time,
                        hyperparameters=hyperparameters,
                        environment=environment,
                        device_type=device_type,
                        is_best=is_best,
                        status='completed',
                        completed_time=timezone.now(),
                        metadata=model_info,
                        created_time=timezone.now(),
                        updated_time=timezone.now()
                    )
                    logger.info(f"创建强化学习模型记录: {model_name}")
                
                saved_count += 1
            
            logger.info(f"已保存 {saved_count} 条强化学习模型记录")
            return saved_count > 0
            
        except Exception as e:
            logger.error(f"保存强化学习模型到数据库失败: {e}")
            return False
    
    def cleanup_model_info_file(self):
        """
        清理模型信息文件
        """
        try:
            if os.path.exists(self.model_info_file):
                os.remove(self.model_info_file)
                logger.info(f"已清理强化学习模型信息文件: {self.model_info_file}")
        except Exception as e:
            logger.error(f"清理强化学习模型信息文件失败: {e}")

def import_rl_models_for_task(training_task):
    """
    为指定训练任务导入强化学习模型信息
    
    Args:
        training_task: 训练任务实例
        
    Returns:
        bool: 导入成功返回True，否则返回False
    """
    importer = RLModelImporter()
    return importer.save_rl_models_to_db(training_task)

if __name__ == "__main__":
    # 测试代码
    importer = RLModelImporter()
    models = importer.get_rl_model_info()
    print(f"找到 {len(models)} 个强化学习模型")
    for model in models:
        print(f"- {model.get('model_name', 'Unknown')}: {model.get('algorithm_type', 'Unknown')}")
