<template>
  <WorkflowStepWrapper
    :step-number="2"
    :training-type="TRAINING_TYPES.DEEP_LEARNING"
    :need-save="true"
    @step-data-changed="onStepDataChanged"
    @step-completed="onStepCompleted"
  >
    <template #default="{ loading, onNextStep, onPrevStep }">
      <div class="step-two-content">
        <div class="header">
          <h5 class="q-ma-none">训练参数配备</h5>
          <p class="text-grey-6">配置模型训练的相关参数</p>
        </div>

        <div class="form-section">
          <q-form class="q-gutter-md">
            <!-- 基础参数 -->
            <div class="section-title">基础参数</div>
            
            <div class="row q-gutter-md">
              <div class="col-5">
                <q-input
                  v-model.number="formData.epochs"
                  label="训练轮数 (Epochs)"
                  type="number"
                  outlined
                  dense
                  :rules="[val => val > 0 || '请输入有效的训练轮数']"
                />
              </div>
              <div class="col-5">
                <q-input
                  v-model.number="formData.batch_size"
                  label="批次大小 (Batch Size)"
                  type="number"
                  outlined
                  dense
                  :rules="[val => val > 0 || '请输入有效的批次大小']"
                />
              </div>
            </div>

            <div class="row q-gutter-md">
              <div class="col-5">
                <q-input
                  v-model.number="formData.learning_rate"
                  label="学习率 (Learning Rate)"
                  type="number"
                  step="0.0001"
                  outlined
                  dense
                  :rules="[val => val > 0 || '请输入有效的学习率']"
                />
              </div>
              <div class="col-5">
                <q-select
                  v-model="formData.optimizer"
                  :options="optimizerOptions"
                  label="优化器"
                  outlined
                  dense
                  emit-value
                  map-options
                />
              </div>
            </div>

            <!-- 高级参数 -->
            <div class="section-title">高级参数</div>
            
            <div class="row q-gutter-md">
              <div class="col-5">
                <q-input
                  v-model.number="formData.weight_decay"
                  label="权重衰减"
                  type="number"
                  step="0.0001"
                  outlined
                  dense
                />
              </div>
              <div class="col-5">
                <q-input
                  v-model.number="formData.momentum"
                  label="动量"
                  type="number"
                  step="0.01"
                  outlined
                  dense
                />
              </div>
            </div>

            <div class="row q-gutter-md">
              <div class="col-5">
                <q-select
                  v-model="formData.lr_scheduler"
                  :options="schedulerOptions"
                  label="学习率调度器"
                  outlined
                  dense
                  emit-value
                  map-options
                />
              </div>
              <div class="col-5">
                <q-input
                  v-model.number="formData.patience"
                  label="早停耐心值"
                  type="number"
                  outlined
                  dense
                />
              </div>
            </div>

            <!-- GPU设置 -->
            <div class="section-title">GPU设置</div>
            
            <div class="row q-gutter-md">
              <div class="col-5">
                <q-select
                  v-model="formData.device"
                  :options="deviceOptions"
                  label="设备"
                  outlined
                  dense
                  emit-value
                  map-options
                />
              </div>
              <div class="col-5">
                <q-input
                  v-model.number="formData.num_workers"
                  label="数据加载进程数"
                  type="number"
                  outlined
                  dense
                />
              </div>
            </div>
          </q-form>
        </div>

        <!-- 按钮区域 -->
        <div class="button-section">
          <q-btn
            class="q-mr-md"
            color="grey-7"
            label="上一步"
            @click="onPrevStep"
          />
          <q-btn
            color="primary"
            label="下一步"
            :loading="loading"
            @click="handleNextStep(onNextStep)"
          />
        </div>
      </div>
    </template>
  </WorkflowStepWrapper>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useWorkflowStore } from 'src/stores/workflowStore'
import { TRAINING_TYPES } from 'src/services/workflowApi'
import WorkflowStepWrapper from 'src/components/WorkflowStepWrapper.vue'
import { Notify } from 'quasar'

const workflowStore = useWorkflowStore()

// 表单数据
const formData = ref({
  epochs: 100,
  batch_size: 16,
  learning_rate: 0.001,
  optimizer: 'Adam',
  weight_decay: 0.0001,
  momentum: 0.9,
  lr_scheduler: 'StepLR',
  patience: 10,
  device: 'cuda',
  num_workers: 4
})

// 选项数据
const optimizerOptions = [
  { label: 'Adam', value: 'Adam' },
  { label: 'SGD', value: 'SGD' },
  { label: 'AdamW', value: 'AdamW' },
  { label: 'RMSprop', value: 'RMSprop' }
]

const schedulerOptions = [
  { label: 'StepLR', value: 'StepLR' },
  { label: 'CosineAnnealingLR', value: 'CosineAnnealingLR' },
  { label: 'ReduceLROnPlateau', value: 'ReduceLROnPlateau' },
  { label: 'ExponentialLR', value: 'ExponentialLR' }
]

const deviceOptions = [
  { label: 'CUDA (GPU)', value: 'cuda' },
  { label: 'CPU', value: 'cpu' }
]

/**
 * 处理下一步按钮点击
 */
const handleNextStep = async (onNextStep) => {
  try {
    // 验证表单数据
    if (!validateForm()) {
      return
    }
    
    // 准备步骤数据
    const stepData = {
      ...formData.value,
      step_completed: true,
      completed_at: new Date().toISOString()
    }
    
    // 调用工作流的下一步方法
    await onNextStep(stepData)
    
  } catch (error) {
    console.error('保存步骤数据失败:', error)
  }
}

/**
 * 验证表单数据
 */
const validateForm = () => {
  if (formData.value.epochs <= 0) {
    Notify.create({
      type: 'negative',
      message: '请输入有效的训练轮数'
    })
    return false
  }
  
  if (formData.value.batch_size <= 0) {
    Notify.create({
      type: 'negative',
      message: '请输入有效的批次大小'
    })
    return false
  }
  
  if (formData.value.learning_rate <= 0) {
    Notify.create({
      type: 'negative',
      message: '请输入有效的学习率'
    })
    return false
  }
  
  return true
}

/**
 * 步骤数据变化事件
 */
const onStepDataChanged = (data) => {
  console.log('步骤2数据已更新:', data)
}

/**
 * 步骤完成事件
 */
const onStepCompleted = (data) => {
  console.log('步骤2已完成:', data)
}

/**
 * 从工作流数据加载表单
 */
const loadFormFromWorkflow = () => {
  const currentWorkflow = workflowStore.currentWorkflow
  if (currentWorkflow && currentWorkflow.step2_config) {
    formData.value = { ...formData.value, ...currentWorkflow.step2_config }
  }
}

// 监听工作流变化
watch(() => workflowStore.currentWorkflow, () => {
  loadFormFromWorkflow()
}, { immediate: true })

// 组件挂载时加载数据
onMounted(() => {
  loadFormFromWorkflow()
})
</script>

<style scoped>
.step-two-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
}

.header {
  margin-bottom: 32px;
}

.form-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #1976d2;
  margin: 24px 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e3f2fd;
}

.button-section {
  display: flex;
  justify-content: flex-end;
  padding-top: 24px;
  border-top: 1px solid #e0e0e0;
}
</style>
