import time
import os
import sys
import json
import psutil
from datetime import datetime
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
import ray
print("Ray 路径:", ray.__file__)
print("Ray 版本:", ray.__version__)
from ray import tune
from ray.rllib.algorithms.ppo import PPOConfig
# from ray.rllib.algorithms.sac import SACConfig
# from ray.rllib.algorithms.ddpg import DDPGConfig
from ray.rllib.algorithms import Algorithm
from ray.rllib.callbacks.callbacks import RLlibCallback
from torch import Tensor
import argparse
import subprocess
import signal

# 华为NPU支持
try:
    import torch_npu
    NPU_AVAILABLE = True
    print("华为NPU支持已加载")
except ImportError:
    NPU_AVAILABLE = False
    print("华为NPU支持未找到，将使用CPU/GPU")

# 全局变量用于优雅退出
graceful_exit = False

def get_policy_from_algorithm(algo):
    """
    从算法对象中获取策略，适配不同版本的Ray RLlib

    Args:
        algo: Ray RLlib算法对象

    Returns:
        policy: 策略对象，如果获取失败返回None
    """
    print("🔍 开始获取策略...")

    # 打印算法对象信息用于调试
    print(f"📋 算法类型: {type(algo)}")
    print(f"📋 算法属性: {[attr for attr in dir(algo) if not attr.startswith('_')][:10]}...")

    # 方法1: 新版本通过workers获取策略
    try:
        if hasattr(algo, 'workers') and algo.workers:
            print("🔧 尝试通过workers获取策略...")
            local_worker = algo.workers.local_worker()
            print(f"📋 Local worker类型: {type(local_worker)}")

            # 子方法1.1: 直接调用get_policy
            if hasattr(local_worker, 'get_policy'):
                try:
                    policy = local_worker.get_policy()
                    if policy is not None:
                        print("✅ 通过local_worker.get_policy()获取策略成功")
                        return policy
                except Exception as e:
                    print(f"⚠️ local_worker.get_policy()失败: {e}")

            # 子方法1.2: 通过policy_map获取
            if hasattr(local_worker, 'policy_map'):
                policy_map = local_worker.policy_map
                print(f"📋 Policy map keys: {list(policy_map.keys())}")

                # 尝试获取默认策略
                if "default_policy" in policy_map:
                    policy = policy_map["default_policy"]
                    print("✅ 通过policy_map['default_policy']获取策略成功")
                    return policy
                elif len(policy_map) > 0:
                    # 如果有其他策略，返回第一个
                    policy_key = list(policy_map.keys())[0]
                    policy = policy_map[policy_key]
                    print(f"✅ 通过policy_map['{policy_key}']获取策略成功")
                    return policy

            # 子方法1.3: 尝试其他可能的属性
            for attr_name in ['policy', 'default_policy', '_policy']:
                if hasattr(local_worker, attr_name):
                    try:
                        policy = getattr(local_worker, attr_name)
                        if policy is not None:
                            print(f"✅ 通过local_worker.{attr_name}获取策略成功")
                            return policy
                    except Exception as e:
                        print(f"⚠️ local_worker.{attr_name}失败: {e}")

    except Exception as e:
        print(f"❌ 通过workers获取策略失败: {e}")

    # 方法2: 旧版本直接从算法获取策略
    try:
        print("🔧 尝试直接从算法获取策略...")
        if hasattr(algo, 'get_policy'):
            policy = algo.get_policy()
            if policy is not None:
                print("✅ 通过algo.get_policy()获取策略成功")
                return policy
    except Exception as e:
        print(f"❌ 直接获取策略失败: {e}")

    # 方法3: 通过策略ID获取
    try:
        print("🔧 尝试通过策略ID获取策略...")
        if hasattr(algo, 'get_policy'):
            policy = algo.get_policy("default_policy")
            if policy is not None:
                print("✅ 通过algo.get_policy('default_policy')获取策略成功")
                return policy
    except Exception as e:
        print(f"❌ 通过策略ID获取策略失败: {e}")

    # 方法4: 尝试其他可能的属性
    try:
        print("🔧 尝试其他可能的属性...")
        for attr_name in ['policy', 'default_policy', '_policy', 'trainer']:
            if hasattr(algo, attr_name):
                try:
                    attr_value = getattr(algo, attr_name)
                    if attr_value is not None:
                        # 如果是trainer对象，尝试从中获取策略
                        if hasattr(attr_value, 'get_policy'):
                            policy = attr_value.get_policy()
                            if policy is not None:
                                print(f"✅ 通过algo.{attr_name}.get_policy()获取策略成功")
                                return policy
                        else:
                            print(f"✅ 通过algo.{attr_name}获取策略成功")
                            return attr_value
                except Exception as e:
                    print(f"⚠️ algo.{attr_name}失败: {e}")
    except Exception as e:
        print(f"❌ 其他属性获取失败: {e}")

    print("❌ 所有策略获取方法都失败了")
    print("💡 建议检查:")
    print("   1. Ray RLlib版本是否兼容")
    print("   2. 算法是否正确初始化")
    print("   3. 检查点是否有效")
    return None

def signal_handler(signum, frame):
    """信号处理器，用于优雅退出"""
    global graceful_exit
    print(f"\n收到信号 {signum}，准备优雅退出...")
    graceful_exit = True

    # 清理Ray资源
    try:
        if ray.is_initialized():
            print("正在关闭Ray...")
            ray.shutdown()
    except Exception as e:
        print(f"关闭Ray时出错: {e}")

    sys.exit(0)

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
signal.signal(signal.SIGTERM, signal_handler)  # 终止信号

# 训练指标回调
class TrainingMetricsCallback(RLlibCallback):
    """训练指标回调，记录训练过程中的指标并输出到JSON文件"""

    def __init__(self, metrics_file="/workspace/rl_training_metrics.json"):
        super().__init__()
        self.device_type = None
        self.metrics_file = metrics_file
        self.episode_count = 0

    def on_algorithm_init(self, *, algorithm, **kwargs):
        """算法初始化时设置设备"""
        # 检查是否应该使用NPU
        if NPU_AVAILABLE and hasattr(algorithm.config, 'framework_str'):
            # 尝试获取设备类型从配置
            if hasattr(algorithm.config, '_device_type'):
                self.device_type = algorithm.config._device_type
            else:
                # 默认检查NPU可用性
                import torch
                if hasattr(torch, 'npu') and torch.npu.is_available():
                    self.device_type = 'npu'
                    print("NPU设备回调已激活")

    def on_train_result(self, *, algorithm, result, **kwargs):
        """训练结果回调，记录训练指标"""
        try:
            # 获取基本训练指标
            episode = result.get("training_iteration", self.episode_count)
            self.episode_count = episode

            # 强化学习特有指标 - 使用更安全的获取方式
            episode_reward_mean = result.get("episode_reward_mean", 
                                            result.get("env_runners", {}).get("episode_reward_mean",
                                            result.get("sampler_results", {}).get("episode_reward_mean", 0.0)))
            episode_reward_min = result.get("episode_reward_min",
                                           result.get("env_runners", {}).get("episode_reward_min", 0.0))
            episode_reward_max = result.get("episode_reward_max",
                                           result.get("env_runners", {}).get("episode_reward_max", 0.0))
            episode_len_mean = result.get("episode_len_mean",
                                         result.get("env_runners", {}).get("episode_len_mean", 0.0))

            # 策略和价值损失 - 使用更安全的获取方式
            info = result.get("info", {})
            learner_info = info.get("learner", {})
            policy_info = learner_info.get("default_policy", {})

            # 尝试多种可能的键名
            policy_loss = (policy_info.get("policy_loss", 0.0) or
                          policy_info.get("total_loss", 0.0) or
                          result.get("policy_loss", 0.0))
            vf_loss = (policy_info.get("vf_loss", 0.0) or
                      policy_info.get("value_loss", 0.0) or
                      result.get("vf_loss", 0.0))
            entropy = (policy_info.get("entropy", 0.0) or
                      result.get("entropy", 0.0))

            # 获取系统资源使用情况
            cpu_usage = psutil.cpu_percent()
            memory_usage = psutil.virtual_memory().percent

            # NPU使用情况
            npu_usage = 0.0
            if self.device_type == 'npu':
                import torch
                if hasattr(torch, 'npu'):
                    try:
                        memory_allocated = torch.npu.memory_allocated() / 1024**3  # GB
                        memory_reserved = torch.npu.memory_reserved() / 1024**3   # GB
                        result["custom_metrics"]["npu_memory_allocated_gb"] = memory_allocated
                        result["custom_metrics"]["npu_memory_reserved_gb"] = memory_reserved
                        npu_usage = (memory_allocated / memory_reserved * 100) if memory_reserved > 0 else 0.0
                        print(f"NPU内存使用: 已分配 {memory_allocated:.2f}GB, 已保留 {memory_reserved:.2f}GB")
                    except Exception as e:
                        print(f"获取NPU内存使用情况失败: {e}")

            # 构建指标数据
            metrics_data = {
                "episode": episode,
                "cumulative_reward": episode_reward_mean,
                "average_reward": episode_reward_mean,
                "episode_length": int(episode_len_mean),
                "policy_loss": policy_loss,
                "value_loss": vf_loss,
                "entropy": entropy,
                "cpu_usage": cpu_usage,
                "npu_usage": npu_usage,
                "memory_usage": memory_usage,
                "timestamp": datetime.now().isoformat(),
                "reward_min": episode_reward_min,
                "reward_max": episode_reward_max
            }

            # 输出指标到JSON文件（追加模式）
            with open(self.metrics_file, "a") as f:
                f.write(json.dumps(metrics_data) + "\n")

            # 打印训练进度
            print(f"Episode {episode}: Reward={episode_reward_mean:.2f}, "
                  f"Policy Loss={policy_loss:.4f}, Value Loss={vf_loss:.4f}, "
                  f"CPU={cpu_usage:.1f}%, NPU={npu_usage:.1f}%, Memory={memory_usage:.1f}%")

        except Exception as e:
            print(f"记录训练指标时出错: {e}")
            # 打印更详细的调试信息
            print(f"Result keys: {list(result.keys())}")
            if "info" in result:
                print(f"Info keys: {list(result['info'].keys())}")
                if "learner" in result["info"]:
                    print(f"Learner keys: {list(result['info']['learner'].keys())}")
            # 尝试记录基本信息
            try:
                basic_metrics = {
                    "episode": result.get("training_iteration", self.episode_count),
                    "timestamp": datetime.now().isoformat(),
                    "error": str(e)
                }
                with open(self.metrics_file, "a") as f:
                    f.write(json.dumps(basic_metrics) + "\n")
            except Exception as e2:
                print(f"连基本指标记录也失败: {e2}")

def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--config", type=str, required=True,
                      help="训练配置文件路径")
    parser.add_argument("--task_id", type=str, required=True,
                      help="训练任务ID")
    parser.add_argument("--resume", type=bool, default=False,
                      help="是否继续训练")
    parser.add_argument("--checkpoint-path", type=str, default="",
                      help="检查点路径，如果不指定则使用随机动作")
    parser.add_argument("--num-env-runners", type=int, default=16,
                      help="环境运行器数量")
    parser.add_argument("--num-learners", type=int, default=1,
                      help="学习器数量")
    parser.add_argument("--num-gpus", type=int, default=0,
                      help="GPU数量")
    parser.add_argument("--num-npus", type=int, default=0,
                      help="华为NPU数量")
    parser.add_argument("--use-npu", action="store_true",
                      help="使用华为NPU进行训练")
    parser.add_argument("--local-dir", type=str, default="~/ray_results",
                      help="结果保存路径")
    parser.add_argument("--tensorboard-dir", type=str, default="~/tensorboard_logs",
                      help="TensorBoard日志路径")
    parser.add_argument("--training-iterations", type=int, default=2000,
                      help="训练迭代次数")

    return parser.parse_args()

def load_training_config(config_path):
    """加载训练配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        print(f"成功加载训练配置: {config_path}")
        return config
    except Exception as e:
        print(f"加载训练配置失败: {e}")
        return None

def export_model(checkpoint_path, save_path, save_name, algorithm_type):
    """导出训练好的模型"""
    try:
        import shutil
        from ray.rllib.algorithms import Algorithm

        print(f"开始导出模型: {checkpoint_path} -> {save_path}/{save_name}")

        # 方法1: 直接复制检查点目录
        checkpoint_save_path = os.path.join(save_path, f"{save_name}_checkpoint")
        if os.path.exists(checkpoint_save_path):
            shutil.rmtree(checkpoint_save_path)
        shutil.copytree(checkpoint_path, checkpoint_save_path)
        print(f"检查点已复制到: {checkpoint_save_path}")

        # 方法2: 加载算法并导出策略（可选）
        try:
            print("🔧 尝试加载算法并导出策略...")
            # 从检查点恢复算法
            algo = Algorithm.from_checkpoint(checkpoint_path)
            print(f"✅ 算法加载成功: {type(algo)}")

            # 获取策略
            policy = get_policy_from_algorithm(algo)
            if policy is None:
                print("⚠️ 无法获取策略，跳过策略导出")
            else:
                print(f"✅ 策略获取成功: {type(policy)}")

                # 导出策略模型（PyTorch格式）
                try:
                    if hasattr(policy, 'export_model'):
                        model_save_path = os.path.join(save_path, f"{save_name}.pth")
                        policy.export_model(model_save_path)
                        print(f"✅ 策略模型已导出到: {model_save_path}")
                    else:
                        print("⚠️ 策略不支持export_model方法")
                except Exception as export_error:
                    print(f"⚠️ 策略模型导出失败: {export_error}")

                # 保存策略权重
                try:
                    if hasattr(policy, 'get_weights'):
                        weights = policy.get_weights()
                        weights_save_path = os.path.join(save_path, f"{save_name}_weights.pkl")
                        import pickle
                        with open(weights_save_path, 'wb') as f:
                            pickle.dump(weights, f)
                        print(f"✅ 策略权重已保存到: {weights_save_path}")
                    else:
                        print("⚠️ 策略不支持get_weights方法")
                except Exception as weights_error:
                    print(f"⚠️ 策略权重保存失败: {weights_error}")

            # 清理算法实例
            try:
                algo.stop()
                print("✅ 算法实例已清理")
            except Exception as stop_error:
                print(f"⚠️ 清理算法实例失败: {stop_error}")

        except Exception as e:
            print(f"⚠️ 导出策略模型时出错: {e}")
            print("📁 将仅保存检查点文件")
            # 不抛出异常，继续执行后续代码

        # 方法3: 创建模型使用说明
        readme_content = f"""# {save_name} 强化学习模型

## 模型信息
- 算法类型: {algorithm_type}
- 生成时间: {datetime.now().isoformat()}
- 检查点路径: {checkpoint_save_path}

## 使用方法

### 1. 从检查点加载模型
```python
from ray.rllib.algorithms import Algorithm

# 加载算法
algo = Algorithm.from_checkpoint("{checkpoint_save_path}")

# 获取策略（使用工具函数）
policy = get_policy_from_algorithm(algo)
if policy is None:
    print("无法获取策略")
    exit(1)

# 进行推理
action = policy.compute_single_action(observation)
```

### 2. 评估模型
```python
# 创建环境
import gym
env = gym.make("Pendulum-v1")

# 运行一个episode
obs = env.reset()
total_reward = 0
done = False

while not done:
    action = policy.compute_single_action(obs)
    obs, reward, done, info = env.step(action)
    total_reward += reward

print(f"Total reward: {{total_reward}}")
```

## 文件说明
- `{save_name}_checkpoint/`: 完整的Ray RLlib检查点
- `{save_name}.pth`: PyTorch模型文件（如果可用）
- `{save_name}_weights.pkl`: 策略权重文件
- `README.md`: 本说明文件
"""

        readme_path = os.path.join(save_path, f"{save_name}_README.md")
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print(f"使用说明已保存到: {readme_path}")

        return True

    except Exception as e:
        print(f"导出模型失败: {e}")
        return False

def save_model_info_to_json(model_info, task_id):
    """
    将模型信息保存到JSON文件，供后续读取入库
    参考深度学习模型信息获取方式
    """
    try:
        # 构建模型信息文件路径
        model_info_file = "/workspace/rl_model_info.json"

        # 添加任务ID和时间戳
        model_info_with_meta = {
            **model_info,
            "task_id": task_id,
            "training_type": "reinforcement_learning",
            "framework": "ray_rllib",
            "created_time": datetime.now().isoformat(),
            "status": "completed"
        }

        # 检查文件是否已存在
        existing_models = []
        if os.path.exists(model_info_file):
            try:
                with open(model_info_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if content:
                        # 处理多行JSON格式（每行一个JSON对象）
                        for line in content.split('\n'):
                            if line.strip():
                                try:
                                    existing_models.append(json.loads(line))
                                except json.JSONDecodeError:
                                    # 如果是单个JSON对象格式
                                    existing_models = [json.loads(content)]
                                    break
            except Exception as e:
                print(f"读取现有模型信息文件失败: {e}")

        # 检查是否已存在相同的模型
        model_name = model_info_with_meta.get('model_name', 'unknown')
        model_exists = False
        for i, existing_model in enumerate(existing_models):
            if (existing_model.get('model_name') == model_name and
                existing_model.get('task_id') == task_id):
                # 更新现有模型信息
                existing_models[i] = model_info_with_meta
                model_exists = True
                print(f"更新现有模型信息: {model_name}")
                break

        if not model_exists:
            # 添加新模型信息
            existing_models.append(model_info_with_meta)
            print(f"添加新模型信息: {model_name}")

        # 保存到文件（每行一个JSON对象的格式，便于追加和读取）
        with open(model_info_file, 'w', encoding='utf-8') as f:
            for model in existing_models:
                f.write(json.dumps(model, ensure_ascii=False) + '\n')

        print(f"模型信息已保存到文件: {model_info_file}")
        return True

    except Exception as e:
        print(f"保存模型信息到文件失败: {e}")
        return False

def get_config(args, training_config):
    """根据训练配置生成Ray RLlib配置"""

    # 从配置文件获取参数
    algorithm_config = training_config.get('algorithm', {})
    hyper_params = training_config.get('hyperParams', {})
    cluster_config = training_config.get('cluster', {})
    agent_config = training_config.get('agent', {})

    # 获取算法类型
    rl_type = algorithm_config.get('rlType', 'PPO')

    # 获取超参数
    learning_rate = float(hyper_params.get('learningRate', '1e-4'))
    batch_size = int(hyper_params.get('batchSize', 32))
    epochs = int(hyper_params.get('epochs', 1000))

    # 获取集群配置
    cpu_count = int(cluster_config.get('cpuCount', '4'))
    gpu_count = int(cluster_config.get('gpuCount', '1'))

    # 确定使用的设备类型和数量
    use_npu = gpu_count > 0 and NPU_AVAILABLE
    if use_npu:
        device_type = "npu"
        num_devices = gpu_count
        print(f"使用华为NPU进行训练，NPU数量: {num_devices}")
        args.use_npu = True
        args.num_npus = num_devices
    else:
        device_type = "gpu" if gpu_count > 0 else "cpu"
        num_devices = gpu_count
        print(f"使用{device_type.upper()}进行训练，设备数量: {num_devices}")
        args.use_npu = False
        args.num_gpus = gpu_count

    # 根据算法类型创建配置
    if rl_type == 'PPO':
        config = PPOConfig()
    # elif rl_type == 'SAC':
    #     config = SACConfig()
    # elif rl_type == 'DDPG':
    #     config = DDPGConfig()
    else:
        print(f"不支持的算法类型: {rl_type}，使用默认PPO")
        config = PPOConfig()

    # 基础配置
    config = (
        config
        .environment("Pendulum-v1")  # 可以根据配置文件修改环境
        .framework("torch")
        .env_runners(
            num_env_runners=min(2, max(1, cpu_count // 4)),  # 减少env_runners数量
            # rollout_fragment_length=200,  # 使用固定值而不是auto
            num_envs_per_env_runner=1,
            num_cpus_per_env_runner=1,
            # max_env_runner_restarts=0,  # 禁用重启避免资源问题
            # restart_failed_sub_environments=False,  # 禁用子环境重启
        )
        .learners(
            num_learners=1,
            num_cpus_per_learner=1,
            num_gpus_per_learner=0 if use_npu else min(1, gpu_count),
            custom_resources_per_learner={"NPU": 1} if use_npu else {}
        )
        .training(
            gamma=0.99,
            lr=learning_rate,
            train_batch_size=max(512, batch_size * 16),
            model={
                "fcnet_hiddens": [256, 256, 128],
                "fcnet_activation": "relu",
                "vf_share_layers": False,
                "use_lstm": True,
                "lstm_cell_size": 128,
                "max_seq_len": 32,
            },
        )
        .evaluation(
            evaluation_interval=20,  # 减少评估频率
            evaluation_duration=10,  # 减少评估时长
            evaluation_num_workers=1,  # 固定使用1个worker
            evaluation_config={
                "render_mode": "txt",
                "explore": False,
                "num_envs_per_env_runner": 1,  # 确保每个worker只有1个环境
            },
        )
    )

    # PPO特定配置
    if rl_type == 'PPO':
        config = config.training(
            lambda_=0.95,
            clip_param=0.2,
            vf_clip_param=10.0,
            entropy_coeff=0.01,
            num_sgd_iter=10,
            minibatch_size=max(64, batch_size),
        )

    # 添加训练指标回调
    config = config.callbacks(TrainingMetricsCallback)

    # 华为NPU特定配置
    if args.use_npu and NPU_AVAILABLE:
        # 添加设备类型标记到配置
        config._device_type = "npu"
        # 添加NPU特定的训练配置
        config = config.training(
            # NPU优化配置
            optimizer={
                "type": "Adam",
                "eps": 1e-7,  # NPU对数值稳定性要求更高
            }
        )

        # 设置NPU环境变量
        os.environ.setdefault("ASCEND_RT_VISIBLE_DEVICES", "0")
        # 设置PyTorch默认设备为NPU
        import torch
        if hasattr(torch, 'npu') and torch.npu.is_available():
            torch.npu.set_device(0)
            print(f"设置默认NPU设备: {torch.npu.current_device()}")
    else:
        config._device_type = "gpu" if args.num_gpus > 0 else "cpu"

    # 清空探索配置
    config.exploration_config = {}
    return config

if __name__ == "__main__":
    args = parse_args()

    # 加载训练配置
    training_config = load_training_config(args.config)
    if training_config is None:
        print("无法加载训练配置，退出")
        sys.exit(1)

    print(f"训练任务ID: {args.task_id}")
    print(f"是否继续训练: {args.resume}")

    # 从配置获取训练参数
    hyper_params = training_config.get('hyperParams', {})
    algorithm_config = training_config.get('algorithm', {})

    # 设置训练迭代次数
    training_iterations = int(hyper_params.get('epochs', 1000))
    rl_type = algorithm_config.get('rlType', 'PPO')

    timestamp = time.strftime("%Y%m%d-%H%M%S")
    experiment_name = f"{rl_type}-{args.task_id}-{timestamp}"

    # 清理旧的指标文件（如果不是继续训练）
    metrics_file = "/workspace/rl_training_metrics.json"
    if not args.resume and os.path.exists(metrics_file):
        os.remove(metrics_file)
        print("已清理旧的训练指标文件")

    # 检查NPU可用性
    if hasattr(args, 'use_npu') and args.use_npu and not NPU_AVAILABLE:
        print("警告：要求使用NPU但torch_npu未安装，将回退到GPU/CPU训练")
        args.use_npu = False

    # 启动tensorboard
    try:
        experiment_name = "Pendulum-v1"
        subprocess.Popen(['tensorboard --logdir ~/ray_results/' + experiment_name + ' --bind_all'])
        # subprocess.Popen(['tensorboard', '--logdir', f'~/ray_results/{experiment_name}', '--bind_all'],
        #                 stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        print("TensorBoard已启动")
    except Exception as e:
        print(f"启动TensorBoard失败: {e}")

    # 初始化Ray - 添加更稳定的配置
    try:
        # 检查Ray是否已经初始化
        if ray.is_initialized():
            ray.shutdown()
            print("关闭现有Ray实例")

        # 配置Ray初始化参数
        ray_init_config = {
            "ignore_reinit_error": True,
            "include_dashboard": False,  # 禁用dashboard减少资源占用
            "log_to_driver": False,      # 减少日志输出
            "_temp_dir": "/tmp/ray",     # 指定临时目录
        }

        # NPU环境下的特殊配置
        if hasattr(args, 'use_npu') and args.use_npu and NPU_AVAILABLE:
            # 设置NPU相关环境变量
            os.environ.setdefault("ASCEND_RT_VISIBLE_DEVICES", "0")
            os.environ.setdefault("RANK_TABLE_FILE", "")
            os.environ.setdefault("RANK_SIZE", "1")
            os.environ.setdefault("RANK_ID", "0")

            # 添加NPU资源配置
            ray_init_config["resources"] = {"NPU": 1}

            print("配置NPU环境变量")

        # 初始化Ray
        ray.init(**ray_init_config)
        print("Ray初始化成功")

        # NPU设备验证和设置
        if hasattr(args, 'use_npu') and args.use_npu and NPU_AVAILABLE:
            import torch
            if hasattr(torch, 'npu') and torch.npu.is_available():
                print(f"NPU设备数量: {torch.npu.device_count()}")
                for i in range(torch.npu.device_count()):
                    try:
                        print(f"NPU {i}: {torch.npu.get_device_name(i)}")
                    except:
                        print(f"NPU {i}: 设备可用")
                print("NPU设备验证成功")
            else:
                print("警告: NPU设备不可用，回退到CPU训练")
                args.use_npu = False

    except Exception as e:
        print(f"Ray初始化失败: {e}")
        print("尝试使用简化配置重新初始化...")
        try:
            if ray.is_initialized():
                ray.shutdown()
            ray.init(ignore_reinit_error=True, include_dashboard=False)
            print("Ray简化初始化成功")
        except Exception as e2:
            print(f"Ray简化初始化也失败: {e2}")
            sys.exit(1)
            args.use_npu = False

    # 获取配置
    config = get_config(args, training_config)

    # 设置检查点路径（用于继续训练）
    restore_path = None
    if args.resume and args.checkpoint_path:
        restore_path = args.checkpoint_path
        print(f"继续训练，从检查点恢复: {restore_path}")

    # 运行训练
    try:
        # 获取输出配置
        output_config = training_config.get('output', {})
        save_path = output_config.get('savePath', '/workspace/models')
        save_name = output_config.get('saveName', f'{rl_type}_model_{timestamp}')

        # 确保保存目录存在
        os.makedirs(save_path, exist_ok=True)

        print(f"开始训练，模型将保存到: {save_path}/{save_name}")

        # 检查是否需要优雅退出
        if graceful_exit:
            print("检测到退出信号，跳过训练")
            os.exit(0)

        # 运行训练
        print(f"开始训练，目标迭代次数: {training_iterations}")
        analysis = tune.run(
            rl_type,
            config=config,
            resume="LOCAL" if args.resume else False,
            restore=restore_path,
            stop={
                "training_iteration": training_iterations,
                "env_runners/episode_return_mean": 800,  # 可以根据配置调整
            },
            checkpoint_freq=50,
            checkpoint_at_end=True,
            storage_path=os.path.expanduser("~/ray_results"),
            name=experiment_name,
            verbose=2,
            # 添加失败容忍度
            max_failures=3,
            # 减少并发试验数量
            max_concurrent_trials=1,
        )

        print("训练完成，开始保存模型...")

        # 获取最佳检查点 - 使用更强的兼容性方法
        best_checkpoint = None

        try:
            print("🔍 开始查找最佳检查点...")

            # 首先检查训练结果中有哪些指标
            if hasattr(analysis, 'trials') and analysis.trials:
                print(f"📊 找到 {len(analysis.trials)} 个试验")

                # 检查第一个试验的指标
                first_trial = analysis.trials[0]
                if hasattr(first_trial, 'last_result') and first_trial.last_result:
                    available_metrics = list(first_trial.last_result.keys())
                    print(f"📈 可用指标: {available_metrics}")

                # 尝试多种指标名称（按优先级排序）
                metric_candidates = [
                    "env_runners/episode_return_mean",
                    "episode_reward_mean",
                    "sampler_results/episode_reward_mean",
                    "episode_reward_max",
                    "env_runners/episode_reward_max",
                    "reward_mean",
                    "mean_reward",
                    "episode_return_mean",
                    "return_mean"
                ]

                best_trial = None
                used_metric = None

                for metric in metric_candidates:
                    try:
                        trial = analysis.get_best_trial(metric, mode="max")
                        if trial is not None:
                            best_trial = trial
                            used_metric = metric
                            print(f"✅ 使用指标 '{metric}' 找到最佳试验")
                            break
                    except Exception as metric_error:
                        print(f"⚠️ 指标 '{metric}' 获取失败: {metric_error}")
                        continue

                # 如果所有指标都失败，使用最后一个试验
                if best_trial is None:
                    print("⚠️ 所有指标都失败，使用最后一个试验")
                    best_trial = analysis.trials[-1]
                    used_metric = "last_trial"

                # 获取检查点
                if best_trial:
                    try:
                        # 方法1: 使用analysis.get_best_checkpoint
                        if used_metric != "last_trial":
                            best_checkpoint = analysis.get_best_checkpoint(
                                trial=best_trial,
                                metric=used_metric,
                                mode="max"
                            )
                        else:
                            # 方法2: 直接从试验获取检查点
                            if hasattr(best_trial, 'checkpoint') and best_trial.checkpoint:
                                best_checkpoint = best_trial.checkpoint.path
                            elif hasattr(best_trial, 'get_best_checkpoint'):
                                best_checkpoint = best_trial.get_best_checkpoint()
                    except Exception as checkpoint_error:
                        print(f"⚠️ 获取检查点失败: {checkpoint_error}")

                        # 方法3: 手动查找检查点目录
                        try:
                            import os
                            trial_dir = best_trial.local_dir if hasattr(best_trial, 'local_dir') else str(best_trial.path)
                            if os.path.exists(trial_dir):
                                # 查找checkpoint目录
                                for item in os.listdir(trial_dir):
                                    if item.startswith('checkpoint_'):
                                        checkpoint_path = os.path.join(trial_dir, item)
                                        if os.path.isdir(checkpoint_path):
                                            best_checkpoint = checkpoint_path
                                            print(f"📁 手动找到检查点: {checkpoint_path}")
                                            break
                        except Exception as manual_error:
                            print(f"⚠️ 手动查找检查点也失败: {manual_error}")

            else:
                print("❌ 没有找到任何试验结果")

        except Exception as e:
            print(f"❌ 获取检查点过程出错: {e}")
            import traceback
            traceback.print_exc()

        if best_checkpoint:
            print(f"✅ 找到最佳检查点: {best_checkpoint}")

            # 导出模型到指定路径
            try:
                export_model(best_checkpoint, save_path, save_name, rl_type)
                print("✅ 模型导出成功")
            except Exception as export_error:
                print(f"⚠️ 模型导出失败: {export_error}")
                print("📁 将仅保存检查点文件")

                # 备用方案：直接复制检查点
                try:
                    import shutil
                    checkpoint_save_path = os.path.join(save_path, f"{save_name}_checkpoint")
                    os.makedirs(save_path, exist_ok=True)
                    shutil.copytree(best_checkpoint, checkpoint_save_path)
                    print(f"✅ 检查点已复制到: {checkpoint_save_path}")
                except Exception as copy_error:
                    print(f"❌ 复制检查点也失败: {copy_error}")

            # 构建完整的模型信息
            model_info = {
                "model_name": save_name,
                "model_path": f"{save_path}/{save_name}",
                "checkpoint_path": str(best_checkpoint) if best_checkpoint else "",
                "algorithm_type": rl_type,
                "training_iterations": training_iterations,
                "final_reward": 0.0,  # 可以从训练结果中获取
                "training_time": 0.0,  # 可以计算训练耗时
                "hyperparameters": {
                    "learning_rate": float(hyper_params.get('learningRate', '1e-4')),
                    "batch_size": int(hyper_params.get('batchSize', 32)),
                    "epochs": training_iterations,
                    "algorithm": rl_type
                },
                "environment": "Pendulum-v1",  # 可以从配置中获取
                "framework": "ray_rllib",
                "device_type": "npu" if args.use_npu else ("gpu" if args.num_gpus > 0 else "cpu"),
                "created_time": datetime.now().isoformat(),
                "task_id": args.task_id,
                "status": "completed",
                "is_best": True
            }

            print(f"训练完成！模型已保存到: {save_path}/{save_name}")
            print(f"检查点路径: {best_checkpoint}")

            # 保存模型信息到JSON文件，供后续读取入库
            try:
                save_model_info_to_json(model_info, args.task_id)
                print("模型信息已保存到JSON文件，等待系统读取入库")
            except Exception as e:
                print(f"保存模型信息到文件失败: {e}")
                print("请检查文件系统权限或磁盘空间")
        else:
            print("警告：未找到有效的检查点，模型可能未正确保存")

    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    finally:
        # 清理资源
        try:
            print("开始清理资源...")

            # 清理Ray资源
            if ray.is_initialized():
                ray.shutdown()
                print("Ray资源已清理")

            # 清理NPU相关资源
            if hasattr(args, 'use_npu') and args.use_npu and NPU_AVAILABLE:
                try:
                    import torch
                    if hasattr(torch, 'npu'):
                        torch.npu.empty_cache()
                        print("NPU缓存已清理")
                except Exception as npu_error:
                    print(f"清理NPU资源失败: {npu_error}")

            # 清理临时文件
            import shutil
            temp_dirs = ["/tmp/ray", "/tmp/tensorboard"]
            for temp_dir in temp_dirs:
                try:
                    if os.path.exists(temp_dir):
                        shutil.rmtree(temp_dir, ignore_errors=True)
                        print(f"已清理临时目录: {temp_dir}")
                except Exception as cleanup_error:
                    print(f"清理临时目录失败 {temp_dir}: {cleanup_error}")

            print("资源清理完成")

        except Exception as cleanup_error:
            print(f"资源清理过程中出错: {cleanup_error}")

        print("训练脚本执行完毕")