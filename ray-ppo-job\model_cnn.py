import torch
import torch.nn as nn
import torch.nn.functional as F

class PPOActorCriticCNN(nn.Module):
    def __init__(self, action_dim):
        super(PPOActorCriticCNN, self).__init__()

        # 使用卷积网络来处理 3 通道的 RGB 输入
        self.conv = nn.Sequential(
            nn.Conv2d(3, 32, kernel_size=8, stride=4),  # 输入通道为3 (RGB)，输出通道为32
            nn.ReLU(),
            nn.Conv2d(32, 64, kernel_size=4, stride=2),
            nn.ReLU(),
            nn.Conv2d(64, 64, kernel_size=3, stride=1),
            nn.ReLU()
        )

        # 计算卷积层输出的特征大小
        # 输入图像大小为 (210, 160)，经过卷积层后得到的特征图大小为 (9, 6)
        # 因为卷积的输出形状为：[(输入尺寸 - kernel_size + 2 * padding) / stride] + 1
        self.fc = nn.Sequential(
            nn.Linear(64 * 7 * 7, 512),  # 卷积层输出展平后输入全连接层
            nn.ReLU()
        )

        # Actor 负责动作选择
        self.actor = nn.Linear(512, action_dim)

        # Critic 负责状态价值估计
        self.critic = nn.Linear(512, 1)

    def forward(self, x):
        # 将输入 reshape 成 (batch_size, 3, 210, 160)，适应卷积层的输入
        x = x.permute(0, 3, 1, 2)  # 转换成 (batch_size, 3, 210, 160)
        x = F.interpolate(x, size=(84, 84), mode='bilinear', align_corners=False)
        x = x / 255.0  # 归一化到 0-1 之间

        # 卷积层提取特征
        conv_out = self.conv(x)
        conv_out = conv_out.reshape(conv_out.size(0), -1)  # 展平成一维向量

        # 全连接层
        fc_out = self.fc(conv_out)

        # Actor 和 Critic 输出
        action_probs = F.softmax(self.actor(fc_out), dim=-1)  # 动作概率
        value = self.critic(fc_out)  # 状态价值

        return action_probs, value
