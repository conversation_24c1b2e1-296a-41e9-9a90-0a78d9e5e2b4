/*
 * @Author: <PERSON>z<PERSON>
 * @Date: 2025-08-26 10:37:38
 * @LastEditors: Szc
 * @LastEditTime: 2025-08-26 11:03:44
 * @Description: 👉工作流接口封装👈
 */

import { api } from '../../boot/axios';

// 1. 创建训练任务工作流
export function createWorkFlow(data) {
    return api({
        method: 'POST',
        url: '/backend/workflows/create/',
        data
    })
}

// 2. 跳转到指定步骤
export function jumpToStep(data) {
    return api({
        method: 'POST',
        url: '/backend/workflows/jump/',
        data
    })
}

// 3. 保存步骤数据
export function saveStepData(data) {
    return api({
        method: 'POST',
        url: '/backend/workflows/step/save/',
        data
    })
}

// 4. 开始数据分析
export function startDataAnalysis(data) {
    return api({
        method: 'POST',
        url: '/backend/workflows/data/analysis/',
        data
    })
}

// 5. 提交模型训练
export function submitTraining(data) {
    return api({
        method: 'POST',
        url: '/backend/workflows/training/submit/',
        data
    })
}

// 6. 训练控制
export function controlTraining(data) {
    return api({
        method: 'POST',
        url: '/backend/workflows/training/control/',
        data
    })
}

// 7. 完成步骤
export function completeStep(data) {
    return api({
        method: 'POST',
        url: '/backend/workflows/step/complete/',
        data
    })
}

// 8. 获取任务列表
export function getWorkflowList(params = {}) {
    return api({
        method: 'GET',
        url: '/backend/workflows/list/',
        params
    })
}

// 9. 更新训练指标
export function updateTrainingMetrics(data) {
    return api({
        method: 'POST',
        url: '/backend/workflows/metrics/update/',
        data
    })
}

// 便捷方法：训练控制操作
export const trainingControl = {
    // 暂停训练
    pause: (taskId) => controlTraining({ task_id: taskId, action: 'pause' }),
    
    // 恢复训练
    resume: (taskId) => controlTraining({ task_id: taskId, action: 'resume' }),
    
    // 停止训练
    stop: (taskId) => controlTraining({ task_id: taskId, action: 'stop' })
}

