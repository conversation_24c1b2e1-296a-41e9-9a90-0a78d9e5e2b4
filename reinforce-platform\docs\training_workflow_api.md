# 统一训练工作流API文档

## 概述

新的统一训练工作流系统支持三种类型的训练：
- **深度学习** (deep_learning): 数据加载分割 → 训练参数配备 → 提交模型训练 → 训练结果评估 → 模型导出存储
- **强化学习** (reinforcement_learning): 交互环境接入 → 训练参数配置 → 提交模型训练 → 训练结果评估 → 模型导出存储
- **大模型** (large_model): 数据加载分割 → 训练参数配备 → 提交微调训练 → 训练结果评估 → 模型量化剪枝 → 模型部署推理

## 核心设计

### 数据库设计
- 使用统一的 `TrainingWorkflow` 表存储所有类型的训练任务
- 使用 `task_id` (UUID) 作为唯一标识符
- 使用 `step1_config` ~ `step6_config` JSON字段存储各步骤配置
- 使用 `training_metrics` 和 `training_logs` 存储训练过程数据
- 使用 `status` 和 `training_status` 分别管理整体状态和训练状态

### 工作流程
1. **点击开始训练** → 创建 task_id 和 TrainingModel 记录
2. **前两步点击下一步** → 保存配置数据，更新状态和步骤
3. **第三步训练控制** → 开始/暂停/恢复/停止训练，实时更新指标
4. **右侧列表跳转** → 根据保存状态跳转到正确的步骤页面

## API接口

### 1. 创建训练工作流 (点击开始训练时调用)

**POST** `/backend/workflows/create/`

```json
{
  "name": "我的训练任务",
  "description": "任务描述",
  "training_type": "deep_learning",  // deep_learning, reinforcement_learning, large_model
  "task_type": "object_detection_yolov8",
  "model_name": "YOLOv8模型"
}
```

**响应:**
```json
{
  "success": true,
  "message": "训练任务创建成功",
  "task_id": "uuid-string",
  "workflow_id": 123,
  "model_info_id": 456,
  "training_type": "deep_learning",
  "current_step": 1,
  "status": "draft"
}
```

### 2. 开始数据分析 (第一步点击开始分析)

**POST** `/backend/workflows/data/analysis/`

```json
{
  "task_id": "uuid-string",
  "dataset_config": {
    "dataset_id": 1,
    "dataset_name": "COCO数据集",
    "total_samples": 1000,
    "train_ratio": 0.8,
    "val_ratio": 0.2,
    "classes": ["person", "car", "dog"]
  }
}
```

### 3. 保存步骤数据 (前两步点击下一步)

**POST** `/backend/workflows/step/save/`

```json
{
  "task_id": "uuid-string",
  "step_number": 1,  // 1 或 2
  "step_data": {
    // 步骤1: 数据集配置和占比信息
    // 步骤2: 训练参数配置
  }
}
```

**响应:**
```json
{
  "success": true,
  "message": "步骤1数据保存成功",
  "current_step": 2,
  "status": "step1_saved",
  "next_step_number": 2
}
```

### 4. 提交模型训练 (第三步点击开始训练)

**POST** `/backend/workflows/training/submit/`

```json
{
  "task_id": "uuid-string",
  "training_config": {
    "algorithm_version": "v8",
    "model_path": "yolov8n.pt",
    "epochs": 100,
    "batch_size": 16,
    "learning_rate": 0.001
  }
}
```

**响应:**
```json
{
  "success": true,
  "message": "训练任务已开始",
  "training_task_id": 789,
  "status": "training",
  "training_status": "running"
}
```

### 5. 训练控制 (第三步的暂停/恢复/停止按钮)

**POST** `/backend/workflows/training/control/`

```json
{
  "task_id": "uuid-string",
  "action": "pause"  // pause, resume, stop
}
```

**响应:**
```json
{
  "success": true,
  "message": "训练已暂停",
  "status": "paused",
  "training_status": "paused"
}
```

### 6. 获取训练任务列表 (右侧任务列表)

**GET** `/backend/workflows/list/`

**查询参数:**
- `training_type`: 训练类型筛选
- `status`: 状态筛选
- `page`: 页码
- `page_size`: 每页数量

**响应:**
```json
{
  "success": true,
  "data": {
    "total": 50,
    "page": 1,
    "page_size": 10,
    "workflows": [
      {
        "task_id": "uuid-string",
        "name": "任务名称",
        "training_type": "deep_learning",
        "status": "step1_saved",
        "training_status": "not_started",
        "current_step": 2,
        "step_name": "训练参数配备",
        "progress_percentage": 40,
        "created_at": "2024-01-01T10:00:00Z"
      }
    ]
  }
}
```

### 7. 跳转到指定步骤 (从右侧列表点击跳转)

**POST** `/backend/workflows/jump/`

```json
{
  "task_id": "uuid-string"
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "task_id": "uuid-string",
    "name": "任务名称",
    "training_type": "deep_learning",
    "current_step": 2,
    "target_step": 2,
    "target_page": "step2",
    "step_name": "训练参数配备",
    "step_data": {
      "epochs": 100,
      "batch_size": 16
    },
    "status": "step1_saved",
    "training_status": "not_started",
    "progress_percentage": 40,
    "training_data": {}
  }
}
```

### 8. 更新训练过程指标 (训练过程中实时调用)

**POST** `/backend/workflows/metrics/update/`

```json
{
  "task_id": "uuid-string",
  "metrics": {
    "epoch": 10,
    "step": 100,
    "train_loss": 0.5,
    "val_loss": 0.6,
    "train_accuracy": 0.85,
    "val_accuracy": 0.82
  },
  "log_entry": "Epoch 10 completed, loss: 0.5"
}
```

### 9. 完成步骤 (第三步点击下一步)

**POST** `/backend/workflows/step/complete/`

```json
{
  "task_id": "uuid-string",
  "step_number": 3,
  "final_metrics": {
    "best_accuracy": 0.95,
    "final_loss": 0.1
  }
}
```

## 数据库模型

### TrainingWorkflow 主要字段

- `task_id`: 唯一任务标识符 (UUID)
- `training_type`: 训练类型 (deep_learning/reinforcement_learning/large_model)
- `task_type`: 具体任务类型
- `current_step`: 当前步骤
- `status`: 任务状态 (draft/configuring/training/paused/evaluating/completed/failed/cancelled)
- `step1_config` ~ `step6_config`: 各步骤配置数据 (JSON)
- `training_metrics`: 训练过程指标 (JSON)
- `training_logs`: 训练日志 (JSON Array)

## 使用流程

1. **创建工作流**: 调用创建接口，获得task_id
2. **配置步骤**: 根据训练类型，依次配置各个步骤
3. **提交训练**: 提交训练任务，系统创建training_task_id
4. **监控训练**: 通过WebSocket或轮询获取训练状态和指标
5. **控制训练**: 可以暂停、恢复或停止训练
6. **完成流程**: 训练完成后进行评估和模型导出

## 前端集成

前端可以通过右侧任务列表点击跳转到对应的步骤页面，系统会自动加载保存的配置数据。
