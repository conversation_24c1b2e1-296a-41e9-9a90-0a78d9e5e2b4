<!--
 * @Author: <PERSON>zc
 * @Date: 2025-08-20 14:26:56
 * @LastEditors: Szc
 * @LastEditTime: 2025-08-20 19:51:45
 * @Description: 文档格式转换 - 第一步
-->
<template>
    <div class="content">
        <div class="top">
            <!-- 左侧文件列表区域 (2份) -->
            <div class="left">
                <!-- 搜索区域 -->
                <div class="search-section">
                    <q-select class="filter-select" v-model="filterType" :options="filterOptions" outlined dense
                        emit-value map-options>
                    </q-select>

                    <div class="search-wrapper">
                        <q-input class="search-input" v-model="searchText" placeholder="搜索内容" outlined dense>
                            <template v-slot:append>
                                <q-btn class="search-btn" flat round dense color="primary">
                                    <img src="../../assets/images/btn_icon_sousuo.png" class="search-icon" />
                                </q-btn>
                            </template>
                        </q-input>
                    </div>
                </div>

                <q-separator />

                <!-- 文件列表 -->
                <div class="file-list">
                    <div class="file-item" v-for="file in fileList" :key="file.id">
                        <div class="file-icon">
                            <img src="../../assets/images/lb_icon_wj.png" alt="文件" />
                        </div>
                        <div class="file-info">
                            <div class="file-name">{{ file.name }}</div>
                            <div class="file-date">{{ file.date }}</div>
                        </div>
                        <div class="file-status">
                            <div v-if="file.completed" class="status-success">
                                <img class="done" src="../../assets/images/icon_wancheng.png" alt="">
                                <span class="text">完成</span>
                            </div>
                            <div v-else class="status-error">
                                <img class="faied" src="../../assets/images/icon_weiwancheng.png" alt="">
                                <span class="text">未完成</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 中间文档预览区域 (4份) -->
            <div class="center">
                <!-- 分页控制 -->
                <div class="pagination-controls">
                    <div class="left-controls">
                        <q-btn class="control-btn" flat round dense>
                            <img src="../../assets/images/icon_+.png" class="control-icon" />
                        </q-btn>
                        <q-btn class="control-btn" flat round dense>
                            <img src="../../assets/images/icon_-.png" class="control-icon" />
                        </q-btn>
                        <q-btn class="control-btn" flat round dense>
                            <img src="../../assets/images/icon_zk.png" class="control-icon" />
                        </q-btn>
                    </div>
                    <div class="page-controls">
                        <q-btn class="page-btn" flat round dense>
                            <img src="../../assets/images/icon_a_left.png" class="page-icon" />
                        </q-btn>
                        <div class="page-input-wrapper">
                            <q-input 
                                class="page-input" 
                                v-model="currentPage" 
                                outlined 
                                dense
                            />
                            <span class="page-separator">/ {{ totalPages }}</span>
                        </div>
                        <q-btn class="page-btn" flat round dense>
                            <img src="../../assets/images/icon_a_right.png" class="page-icon" />
                        </q-btn>
                    </div>
                </div>

                <!-- 文档内容 -->
                <div class="document-preview">

                </div>
            </div>

            <!-- 右侧结果展示区域 (4份) -->
            <div class="right">
                <!-- 结果类型切换 -->
                <div class="result-tabs">
                    <q-btn class="tab-btn" :class="{ 'active': resultType === 'recognition' }"
                        @click="resultType = 'recognition'" no-caps flat>
                        识别结果
                    </q-btn>
                    <q-btn class="tab-btn" :class="{ 'active': resultType === 'json' }" @click="resultType = 'json'"
                        no-caps flat>
                        JSON结果
                    </q-btn>
                </div>

                <!-- 结果内容 -->
                <div class="result-content">
                    <div class="result-title">文档格式转换产品文档</div>

                    <div class="result-section">
                        <div class="section-title">一、产品介绍</div>
                        <div class="section-text">
                            文档格式转换可识别图片/PDF/OFD文档版面布局，提取文字内容，并转换为保留原文档版式的word/excel/双层PDF/双层ODF文件，方便二次编辑或复制，可支持各类格/印章/水印/手写等内容的文档，满足文档格式转换，企业档案电子化等场景需求。
                        </div>

                        <div class="result-image">
                            <img src="../../assets/images/ai-car.png" alt="结果图片" />
                        </div>
                    </div>

                    <div class="result-section">
                        <div class="section-title">二、功能介绍</div>
                        <div class="sub-title">文档版式分析</div>
                        <div class="section-text">识别全文信息，并分析文档版式结构，识别文档中包含的插图/表格/标题/段落/印章等版式元素。</div>

                        <div class="sub-title">文档版式还原</div>
                        <div class="section-text">还原文档版式信息，转换为保留原文档版式布局的word文档，方便二次编辑和复制。</div>
                    </div>
                </div>

                <!-- 底部操作按钮 -->
                <div class="result-actions">
                    <q-btn class="action-btn save-btn roundBox" color="grey-7" @click="saveResult">
                        保存结果
                    </q-btn>
                    <q-btn class="action-btn next-btn roundBox" color="primary" @click="nextStep">
                        下一步
                    </q-btn>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, defineEmits } from 'vue'

const emit = defineEmits(['next-step', 'prev-step'])

// 搜索和筛选
const filterType = ref('all')
const filterOptions = [
    { label: '全部', value: 'all' },
    { label: 'PDF', value: 'pdf' },
    { label: 'DOC', value: 'doc' },
    { label: 'TXT', value: 'txt' }
]
const searchText = ref('')

// 文件列表数据
const fileList = ref([
    {
        id: 1,
        name: '条令.pdf',
        date: '2025/01/20 13:00',
        completed: true,
    },
    {
        id: 2,
        name: '文书.doc',
        date: '2025/01/20 13:00',
        completed: true,

    },
    {
        id: 3,
        name: '目标检测.txt',
        date: '2025/01/20 13:00',
        completed: true,

    },
    {
        id: 4,
        name: '超长占位符展示效...',
        date: '2025/01/20 13:00',
        completed: false,
    }
])

// 分页信息
const currentPage = ref(1)
const totalPages = ref(12)

// 结果类型
const resultType = ref('recognition')

// 方法
const saveResult = () => {
    console.log('保存结果')
}

const nextStep = () => {
    emit('next-step')
}
</script>

<style lang="scss" scoped>
.content {
    display: flex;
    flex-direction: column;
    flex: 1;

    .top {
        display: flex;
        flex: 1;
        min-height: 9.5rem;
        gap: .125rem;

        .left,
        .center,
        .right {
            border: .025rem solid #707070;
            background-color: #181a24;
            background-image:
                repeating-linear-gradient(130deg,
                    rgba(255, 255, 255, 0.05) 0px,
                    rgba(255, 255, 255, 0.01) 4px,
                    transparent 1px,
                    transparent 15px);
            padding: .25rem;
            position: relative;

            &::before {
                position: absolute;
                content: '';
                left: -0.1rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198);
                border-radius: .125rem;
                z-index: 1;
            }

            &::after {
                position: absolute;
                content: '';
                left: -0.075rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198, .5);
                z-index: 1;
            }
        }

        // 左侧 2份
        .left {
            flex: 2;
            display: flex;
            flex-direction: column;

            .search-section {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: .1875rem;

                .filter-select {
                    :deep(.q-field__inner) {
                        background: transparent !important;
                    }

                    :deep(.q-field__control) {
                        background: transparent !important;
                    }

                    :deep(.q-field__native) {
                        font-size: .225rem;
                        color: #ffb628 !important;
                    }

                    :deep(.q-field__label) {
                        color: #ffb628 !important;
                    }

                    :deep(.q-select__dropdown-icon) {
                        color: #ffb628 !important;
                    }

                    :deep(.q-field__marginal) {
                        color: #ffb628 !important;
                    }

                }

                .search-wrapper {
                    width: 2.75rem;

                    .search-input {
                        :deep(.q-field__control) {
                            padding-right: 0 !important;
                            color: #fff;
                        }

                        :deep(.q-field__native) {
                            color: #fff;
                        }

                        :deep(.q-field__append) {
                            padding: 0 !important;
                            background: #23a79f;
                        }

                        :deep(.q-field__label) {
                            color: #999;
                        }
                    }

                    .search-btn {
                        color: #4ab4ff;

                        .search-icon {
                            width: .2rem;
                            height: .2rem;
                        }
                    }
                }

                .dropdown-icon {
                    width: .15rem;
                    height: .15rem;
                }
            }

            .toolbar {
                display: flex;
                gap: .125rem;
                margin-bottom: .25rem;

                .tool-btn {
                    color: #999;

                    &:hover {
                        color: #4ab4ff;
                    }

                    .tool-icon {
                        width: .2rem;
                        height: .2rem;
                    }
                }
            }

            .file-list {
                flex: 1;
                padding-top: .25rem;
                border-top: .0125rem solid #607387;
                overflow-y: auto;

                .file-item {
                    display: flex;
                    align-items: center;
                    padding: .125rem;
                    margin-bottom: .125rem;
                    background-color: rgba(42, 45, 58, 0.3);
                    border-bottom: .0125rem solid #9cacc6;
                    transition: background-color 0.3s ease;

                    &:hover {
                        background-color: rgba(42, 45, 58, 0.6);
                    }

                    .file-icon {
                        width: .375rem;
                        height: .375rem;
                        margin-right: .125rem;

                        img {
                            width: 100%;
                            height: 100%;
                        }
                    }

                    .file-info {
                        flex: 1;

                        .file-name {
                            color: #fff;
                            font-size: .225rem;
                            margin-bottom: .0625rem;
                        }

                        .file-date {
                            color: #999;
                            font-size: .15rem;
                        }
                    }

                    .file-status {
                        display: flex;
                        flex-direction: column;
                        gap: .0625rem;
                        .done{
                            width: .225rem;
                            height: .225rem;
                        }
                        .faied{
                            width: .225rem;
                            height: .225rem;
                        }

                        .status-success,
                        .status-error {
                            display: flex;
                            align-items: center;
                            gap: .0625rem;
                            font-size: .125rem;
                            padding: .0625rem;
                            border-radius: .0625rem;

                            span {
                                font-size: .2rem;
                                color: white;
                            }
                        }

                        .status-success {
                            color: #4CAF50;
                        }

                        .status-error {
                            color: #f44336;
                        }
                    }
                }
            }
        }

        // 中间 4份
        .center {
            flex: 4;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
            .pagination-controls {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: .25rem;

                .left-controls {
                    display: flex;
                    gap: .125rem;

                    .control-btn {
                        color: #4ab4ff;
                        border-radius: 0 !important;
                        .control-icon {
                            width: .3rem;
                            height: .3rem;
                        }
                    }
                }

                .page-controls {
                    display: flex;
                    align-items: center;
                    gap: .25rem;

                    .page-btn {
                        color: #4ab4ff;
                        border-radius: 0;
                        .page-icon {
                            width: .125rem;
                            height: .225rem;
                        }
                    }

                    .page-input-wrapper {
                        display: flex;
                        align-items: center;
                        gap: .125rem;
                        font-size: .20rem;
                        .page-input {
                            width: .675rem;
                            height: .45rem;
                            :deep(.q-field__control) {
                                color: #fff;
                                height:100%;
                            }

                            :deep(.q-field__native) {
                                color: #fff;
                                text-align: center;
                                font-size: .20rem;
                            }

                            :deep(.q-field__control-container) {
                                padding: 0 .125rem;
                            }
                        }

                        .page-separator {
                            color: #fff;
                            font-size: .20rem;
                        }
                    }
                }
            }

            .document-preview {
                flex: 1;
                background: rgba(255, 255, 255, 0.95);
                border-radius: .125rem;
                overflow-y: auto;
                color: #333;

                .document-header {
                    text-align: center;
                    margin-bottom: .375rem;

                    h2 {
                        font-size: .3rem;
                        margin: 0;
                        color: #333;
                    }
                }

                .document-section {
                    margin-bottom: .375rem;

                    h3 {
                        font-size: .225rem;
                        color: #333;
                        margin-bottom: .1875rem;
                    }

                    h4 {
                        font-size: .2rem;
                        color: #444;
                        margin-bottom: .125rem;
                    }

                    p {
                        font-size: .175rem;
                        line-height: 1.6;
                        margin-bottom: .1875rem;
                        text-align: justify;
                    }

                    .document-image {
                        text-align: center;
                        margin: .25rem 0;

                        img {
                            max-width: 100%;
                            height: 2.5rem;
                            object-fit: contain;
                            border-radius: .125rem;
                        }
                    }

                    .feature-grid {
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: .25rem;
                        margin-bottom: .25rem;

                        .feature-item {
                            background: #f8f9fa;
                            padding: .1875rem;
                            border-radius: .0625rem;

                            h4 {
                                margin-bottom: .125rem;
                            }

                            p {
                                margin: 0;
                                font-size: .15rem;
                            }
                        }
                    }

                    .usage-list {
                        padding-left: .25rem;

                        li {
                            margin-bottom: .125rem;
                            font-size: .175rem;
                            line-height: 1.5;
                        }
                    }

                    .price-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-top: .1875rem;

                        th,
                        td {
                            border: .0125rem solid #ddd;
                            padding: .125rem;
                            text-align: center;
                            font-size: .15rem;
                        }

                        th {
                            background: #f5f5f5;
                            font-weight: bold;
                        }

                        tbody tr:nth-child(even) {
                            background: #f9f9f9;
                        }
                    }
                }
            }
        }

        // 右侧 4份
        .right {
            flex: 4;
            display: flex;
            flex-direction: column;

            .result-tabs {
                display: flex;
                margin-bottom: .1875rem;

                .tab-btn {
                    width: 1.875rem;
                    height: .625rem;
                    color: #999;
                    font-size: .225rem;
                    background: #2a2d3a;
                    border-radius: 0;
                    &.active {
                        color: #fff;
                        background: url('../../assets/images/bq_bg.png') no-repeat center center;
                        background-size: cover;
                    }

                    &:hover:not(.active) {
                        color: #fff;
                    }
                }
            }

            .result-content {
                flex: 1;
                background: rgba(21,27,41,.6);
                border-radius: .125rem;
                padding: .25rem;
                overflow-y: auto;
                margin-bottom: .1875rem;

                .result-title {
                    color: #fff;
                    font-size: .225rem;
                    margin-bottom: .25rem;
                    font-weight: bold;
                }

                .section-title {
                    color: #4ab4ff;
                    font-size: .2rem;
                    margin-bottom: .1875rem;
                    font-weight: bold;
                }

                .sub-title {
                    color: #4ab4ff;
                    font-size: .175rem;
                    margin-bottom: .125rem;
                    font-weight: bold;
                }

                .section-text {
                    color: #ccc;
                    font-size: .15rem;
                    line-height: 1.6;
                    margin-bottom: .1875rem;
                }

                .result-section {
                    margin-bottom: .25rem;
                }

                .result-image {
                    text-align: center;
                    margin: .1875rem 0;

                    img {
                        max-width: 100%;
                        height: 1.875rem;
                        object-fit: contain;
                        border-radius: .0625rem;
                    }
                }
            }

            .result-actions {
                display: flex;
                gap: .1875rem;
                justify-content: flex-end;

                .action-btn {
                    height: .5rem;
                    font-size: .175rem;
                    padding: 0 .25rem;

                    &.save-btn {
                        background: #666;
                        color: #fff;

                        &:hover {
                            background: #777;
                        }
                    }

                    &.next-btn {
                        background: #4ab4ff;
                        color: #fff;

                        &:hover {
                            background: #3a9de8;
                        }
                    }
                }
            }
        }
    }
}

// 滚动条样式
::-webkit-scrollbar {
    width: .0625rem;
}

::-webkit-scrollbar-track {
    background: #1a1a1a;
    border-radius: .03125rem;
}

::-webkit-scrollbar-thumb {
    background: rgba(74, 180, 255, 0.6);
    border-radius: .03125rem;

    &:hover {
        background: rgba(74, 180, 255, 0.8);
    }
}
</style>
