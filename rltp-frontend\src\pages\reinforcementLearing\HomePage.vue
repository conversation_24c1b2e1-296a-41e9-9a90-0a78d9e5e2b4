<template>
  <div class="q-pa-md mt90 custom">
    <div class="row q-col-gutter-md">
      <div class="col-7">
        <div class="showBorder q-mb-md">
          <q-card-section class="h-100">
            <div class="labelColor">
              <span class="title">人机交互智能模型：</span>
              <span class="content">
                接入汇集侦察传感器（无人机、卫星）的红外、可见光以及SAR图像，从图像中检测识别海马斯、雷霆-2000等时敏目标。
              </span>
            </div>
          </q-card-section>
        </div>

        <div class="marquee-container q-mb-md" @click="resetMarquee">
          <div class="labelColor font16 algotitle botLine">模型列表</div>
          <div
            class="marquee-content"
            ref="marqueeContent"
            :style="{ transform: `translateX(-${translateX}px)` }"
          >
            <div
              class="quard-item marquee-item"
              v-for="(algo, index) in displayedItems"
              :key="index"
              @click="toggleImageSize(algo)"
              :class="{ enlarged: selectedItem === algo || isCenterImage(index) }"
            >
              <q-card
                class="algorithm-card"
                :class="{ 'selected-algorithm': currentAlgorithm === algo.id }"
                @click="selectAlgorithm(algo)"
              >
                <q-img :src="algo.image_url || defaultBackground" style="height: 100%">
                  <template v-slot:error>
                    <div class="absolute-full flex flex-center bg-grey-3 text-grey-7">
                      <q-icon name="image" size="2em" />
                    </div>
                  </template>
                </q-img>
                <div class="imgType">{{ algo.version }}</div>
                <div class="imgName">{{ algo.name }}</div>
              </q-card>
            </div>
          </div>
        </div>

        <div class="training-section q-mb-md">
          <q-btn class="training-btn cus-bg" @click="startTraining" no-caps>
            <div class="training-btn-content">
              <div class="labelColor textSize">开始训练</div>
              <img src="../../assets/images/entry.png" alt="" class="entry-icon" />
            </div>
          </q-btn>
        </div>

        <div class="showBorder radar">
          <q-card-section
            style="
              padding-left: 0 !important;
              padding-right: 0 !important;
              padding-bottom: 0 !important;
            "
          >
            <div class="mb10 labelColor font16 botLine">模型评估</div>
            <div class="evaluation-content">
              <!-- 雷达图：评估指标 -->
              <div ref="radarChart" class="radar-chart-container"></div>
            </div>
          </q-card-section>
        </div>
      </div>

      <div class="col-5">
        <div v-if="loadingTasks" class="flex flex-center q-pa-lg">
          <q-spinner color="primary" size="3em" />
          <div class="q-ml-sm labelColor font16">加载训练任务中...</div>
        </div>

        <div v-else-if="taskError" class="text-negative q-pa-md">
          {{ taskError }}
          <q-btn
            flat
            color="primary"
            label="重试"
            @click="fetchTrainTasks"
            class="q-ml-sm"
          />
        </div>

        <!-- 训练任务卡片列表 -->
        <div v-else class="task-cards-container">
          <div v-for="task in trainTasks" :key="task.id" class="task-card q-mb-md">
            <q-card class="task-card-inner showBorder">
              <q-card-section class="task-card-content">
                <div class="task-header">
                  <div class="task-name labelColor font16">{{ task.name }}</div>
                </div>

                <div class="task-info">
                  <div class="info-item">
                    <span class="info-label">资源配置:</span>
                    <span class="info-value"
                      >{{
                        task.actor_num * task.actor_per_cpu +
                          task.learner_num * task.learner_per_cpu || "未知"
                      }}
                      CPU</span
                    >
                  </div>
                  <div class="info-item">
                    <span class="info-label">训练时长:</span>
                    <span class="info-value">{{
                      convertSecondsToHMS(task.running_time)
                    }}</span>
                  </div>
                </div>

                <div class="task-actions">
                  <div class="status-display">
                    <span class="status-label-text">训练状态:</span>
                    <span
                      :class="`text-${trainingStatus[task.status]?.color}`"
                      class="status-value"
                    >
                      {{ trainingStatus[task.status]?.label }}
                    </span>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 添加算法弹窗 -->
  <q-dialog v-model="showAddAlgorithmDialog">
    <q-card style="width: 600px; max-width: 95vw">
      <q-card-section class="row items-center">
        <div class="labelColor">添加算法</div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-card-section class="q-pt-none">
        <q-form @submit="onSubmitAlgorithm" class="q-gutter-md">
          <q-input
            v-model="newAlgorithm.name"
            :label="newAlgorithm.name ? '' : '算法名称'"
            :rules="[(val) => !!val || '请输入算法名称']"
            outlined
            dense
          />

          <q-input
            v-model="newAlgorithm.artifact_name"
            :label="newAlgorithm.artifact_name ? '' : '镜像名称'"
            :rules="[(val) => !!val || '请输入镜像名称']"
            outlined
            dense
          />

          <q-input
            v-model="newAlgorithm.desc"
            :label="newAlgorithm.desc ? '' : '算法描述'"
            type="textarea"
            :rules="[(val) => !!val || '请输入算法描述']"
            outlined
            autogrow
          />

          <div class="row justify-end q-mt-md">
            <q-btn label="取消" color="grey-7" v-close-popup class="q-mr-sm roundBox" />
            <q-btn
              label="确定"
              type="submit"
              color="primary"
              class="roundBox"
              :loading="submitting"
            />
          </div>
        </q-form>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed, nextTick } from "vue";
import { api } from "boot/axios";
import { usePlugin } from "composables/plugin.js";
import { useRouter, useRoute } from "vue-router";
import { trainingStatus } from "assets/const";
import { convertSecondsToHMS } from "assets/utils";
import defaultBackground from "assets/images/smart_model_backgroud.png";
import algoImg from "assets/images/algo.png";
import jun1 from "assets/images/jun1.png";
import jun2 from "assets/images/jun2.png";
import jun3 from "assets/images/jun3.png";
import jun4 from "assets/images/jun4.png";
import jun5 from "assets/images/jun5.png";
import jun6 from "assets/images/jun6.png";
import jun7 from "assets/images/jun7.png";
import jun8 from "assets/images/jun8.png";
import jun9 from "assets/images/jun9.png";
import jun10 from "assets/images/jun10.png";
import * as echarts from "echarts";
import { createWorkFlow } from "../../request/workFlow/index.js";

const { notify, dialog, customComponentDialog } = usePlugin();
const router = useRouter();
const route = useRoute();

// 添加算法相关的状态
const showAddAlgorithmDialog = ref(false);
const submitting = ref(false);
const newAlgorithm = ref({
  name: "",
  artifact_name: "",
  desc: "",
});

// 图表引用
const radarChart = ref(null);
let radarChartInstance = null;

// 算法列表数据
const algorithms = ref([]);
const currentAlgorithm = ref(null);
const loadingAlgorithms = ref(false);
const algorithmError = ref("");

// 训练任务数据 - 根据路由参数动态生成任务名称
const trainTasks = computed(() => {
  const { labelName, labelType, timestamp } = route.query;

  // 根据标签类型生成不同的算法名称
  const algorithmsByType = {
    intelligent: ["YoloV8", "YoloV5", "SSD", "R-CNN"],
    reinforcement: ["PPO", "DQN", "A3C", "DDPG"],
    "deep-learning": ["ResNet", "Transformer", "BERT", "GPT"],
  };

  const algorithms = algorithmsByType[labelType] || ["YoloV8", "YoloV5"];
  const taskName = labelName || "目标识别";
  const startTime = timestamp || "暂无时间";

  return [
    {
      id: "001",
      name: `${taskName}--${algorithms[0]}--${startTime}`,
      status: 2,
      start_time: startTime,
      running_time: 5280,
      creater_name: "张工程师",
      task_id: "task_001",
      actor_num: 4,
      actor_per_cpu: 2,
      learner_num: 2,
      learner_per_cpu: 4,
    },
    {
      id: "002",
      name: `${taskName}-${algorithms[1] || algorithms[0]}-${startTime}`,
      status: 4,
      start_time: "2024-12-14 14:20:30",
      running_time: 8640,
      creater_name: "李研究员",
      task_id: "task_002",
      actor_num: 6,
      actor_per_cpu: 1,
      learner_num: 3,
      learner_per_cpu: 2,
    },
    {
      id: "003",
      name: `${taskName}-${algorithms[2] || algorithms[0]}-${startTime}`,
      status: 2,
      start_time: startTime,
      running_time: 5280,
      creater_name: "张工程师",
      task_id: "task_003",
      actor_num: 4,
      actor_per_cpu: 2,
      learner_num: 2,
      learner_per_cpu: 4,
    },
    {
      id: "004",
      name: `${taskName}-${algorithms[3] || algorithms[1] || algorithms[0]}-${startTime}`,
      status: 4,
      start_time: "2024-12-14 14:20:30",
      running_time: 8640,
      creater_name: "李研究员",
      task_id: "task_004",
      actor_num: 6,
      actor_per_cpu: 1,
      learner_num: 3,
      learner_per_cpu: 2,
    },
    {
      id: "005",
      name: `${taskName}-${algorithms[0]}-${startTime}`,
      status: 2,
      start_time: startTime,
      running_time: 5280,
      creater_name: "张工程师",
      task_id: "task_005",
      actor_num: 4,
      actor_per_cpu: 2,
      learner_num: 2,
      learner_per_cpu: 4,
    },
    {
      id: "006",
      name: `${taskName}-${algorithms[1] || algorithms[0]}-${startTime}`,
      status: 4,
      start_time: "2024-12-14 14:20:30",
      running_time: 8640,
      creater_name: "李研究员",
      task_id: "task_006",
      actor_num: 6,
      actor_per_cpu: 1,
      learner_num: 3,
      learner_per_cpu: 2,
    },
    {
      id: "007",
      name: `${taskName}-${algorithms[2] || algorithms[0]}-${startTime}`,
      status: 4,
      start_time: "2024-12-14 14:20:30",
      running_time: 8640,
      creater_name: "李研究员",
      task_id: "task_007",
      actor_num: 6,
      actor_per_cpu: 1,
      learner_num: 3,
      learner_per_cpu: 2,
    },
  ];
});
const loadingTasks = ref(false);
const taskError = ref("");
const taskPagination = ref({
  sortBy: "start_time",
  descending: true,
  page: 1,
  rowsPerPage: 5,
  rowsNumber: 0,
});

// 跑马灯相关状态
const marqueeContent = ref(null);
const translateX = ref(0);
const speed = 3;
let intervalId = null;
const selectedItem = ref(null);
const isClick = ref(false);

const displayedItems = computed(() => [...algorithms.value, ...algorithms.value]);

// 固定的卡片尺寸配置（基于CSS中的rem值）
const CARD_CONFIG = {
  widthRem: 3.125,
  marginRightRem: 0.5,
};

// 获取根元素字体大小
const getRootFontSize = () => {
  return parseFloat(getComputedStyle(document.documentElement).fontSize);
};
let containerWidthCache = 0;
const getContainerWidth = () => {
  if (containerWidthCache === 0) {
    const container = document.querySelector(".marquee-container");
    containerWidthCache = container ? container.offsetWidth : 800;
  }
  return containerWidthCache;
};

const resetContainerWidthCache = () => {
  containerWidthCache = 0;
};

// 缓存计算结果，减少重复计算
let cachedRootFontSize = 0;
let cachedItemWidth = 0;

const getCachedItemWidth = () => {
  if (cachedItemWidth === 0 || cachedRootFontSize === 0) {
    //说明重置了 或者 第一次
    cachedRootFontSize = getRootFontSize();
    const cardWidth = CARD_CONFIG.widthRem * cachedRootFontSize;
    const margin = CARD_CONFIG.marginRightRem * cachedRootFontSize;
    cachedItemWidth = cardWidth + margin;
  }
  return cachedItemWidth;
};

const resetItemWidthCache = () => {
  cachedRootFontSize = 0;
  cachedItemWidth = 0;
};
const centerItemIndex = computed(() => {
  const itemsLen = algorithms.value.length 
  if (itemsLen === 0) return 0
  
  const containerWidth = getContainerWidth()
  const itemWidth = getCachedItemWidth()
  
  return Math.floor((translateX.value + (containerWidth / 2)) / itemWidth) % itemsLen
})
// 跑马灯状态控制
const isMarqueeActive = ref(true)

const startMarquee = () => {
  clearInterval(intervalId)
  intervalId = setInterval(() => {
    if (isMarqueeActive.value && !document.hidden) {
      translateX.value += speed
      if (marqueeContent.value && translateX.value >= marqueeContent.value.scrollWidth / 2) {
        translateX.value = 0
      }
    }
  }, 20)
}
const stopMarquee = () => {
  clearInterval(intervalId)
  intervalId = null // 志超专属防御性编程
}

const pauseMarquee = () => {
  isMarqueeActive.value = false //暂停
}

const resumeMarquee = () => {
  isMarqueeActive.value = true // 恢复
}
const selectedAlgorithmName = ref("");
function toggleImageSize(item) {
  console.log(3, item, selectedItem.value);

  if (selectedItem.value === item) {
    console.log('第二次点击嘿嘿'); //再次
    isClick.value = false
    selectedItem.value = null
    startMarquee()
  } else {
    console.log('第一次点击哈哈');//首次
    isClick.value = true
    selectedItem.value = item
    stopMarquee() // 使用封装的停止方法
    selectedAlgorithmName.value = item.name;
  }
}
const isCenterImage = (index) => {
  const itemsLen = algorithms.value.length 
  return (index === centerItemIndex.value || index === centerItemIndex.value + itemsLen) && !selectedItem.value
}

// 获取算法列表
async function fetchAlgorithms() {
  loadingAlgorithms.value = true;
  algorithmError.value = "";

  try {
    // 调用算法列表API  实际/backend/algorithms
    const response = await api.get("/backend/algorithms");
    if (response && response.results) {
      algorithms.value = response.results;
      if (algorithms.value.length > 0) {
        currentAlgorithm.value = algorithms.value[0].id;
      } else {
        currentAlgorithm.value = "empty";
      }
    } else {
      throw new Error("获取数据格式错误");
    }
  } catch (error) {
    console.error("获取算法列表失败", error);
    // algorithmError.value = '获取算法列表失败：' + (error.response?.data?.message || error.message)

    // 设置一些演示数据（仅用于开发阶段）
    algorithms.value = [
      { id: "v1", name: "SSD算法", version: "v1.0.2", image_url: algoImg },
      {
        id: "v2",
        name: "YOLO算法",
        version: "v2.1.3",
        image_url: "assets/images/version2.jpg",
      },
      {
        id: "v3",
        name: "R-CNN算法",
        version: "v1.5.0",
        image_url: "assets/images/version3.jpg",
      },
      {
        id: "v4",
        name: "BERT模型",
        version: "v2.0.1",
        image_url: "assets/images/version4.jpg",
      },
      {
        id: "v4",
        name: "BERT模型",
        version: "v2.0.1",
        image_url: "assets/images/version4.jpg",
      },
      {
        id: "v4",
        name: "BERT模型",
        version: "v2.0.1",
        image_url: "assets/images/version4.jpg",
      },
      {
        id: "v4",
        name: "BERT模型",
        version: "v2.0.1",
        image_url: "assets/images/version4.jpg",
      },
      {
        id: "v4",
        name: "BERT模型",
        version: "v2.0.1",
        image_url: "assets/images/version4.jpg",
      },
      {
        id: "v4",
        name: "BERT模型",
        version: "v2.0.1",
        image_url: "assets/images/version4.jpg",
      },
      {
        id: "v4",
        name: "BERT模型",
        version: "v2.0.1",
        image_url: "assets/images/version4.jpg",
      },
    ];
    currentAlgorithm.value = "v1";
    console.log("catch", algorithms.value);
  } finally {
    loadingAlgorithms.value = false;
    // 设置一些演示数据（仅用于开发阶段）
    algorithms.value = [
      { id: "v1", name: "YOLOv5(红外)", version: "v1.1", image_url: jun1 },
      { id: "v2", name: "YOLOv5(SAR)", version: "v1.1", image_url: jun2 },
      { id: "v3", name: "YOLOv5(可见光)", version: "v1.1", image_url: jun3 },
      { id: "v4", name: "YOLOv8(红外)", version: "v1.1", image_url: jun4 },
      { id: "v5", name: "YOLOv8(可见光)", version: "v1.1", image_url: jun5 },
      { id: "v6", name: "YOLOv8(SAR)", version: "v1.1", image_url: jun6 },
      { id: "v7", name: "YoloV8(红外)", version: "v1.2", image_url: jun7 },
      { id: "v8", name: "YoloV8(可见光)", version: "v1.2", image_url: jun8 },
      { id: "v9", name: "YoloV8(可见光)", version: "v1.3", image_url: jun9 },
      { id: "v10", name: "YoloV8(可见光)", version: "v1.4", image_url: jun10 },
    ];
  }
}

// 获取训练任务列表
async function fetchTrainTasks() {
  loadingTasks.value = true;
  taskError.value = "";

  try {
    // 构造查询参数
    const params = {
      page: taskPagination.value.page,
      page_size: taskPagination.value.rowsPerPage,
      ordering: taskPagination.value.descending
        ? "-" + taskPagination.value.sortBy
        : taskPagination.value.sortBy,
    };

    // 调用训练任务列表API
    const response = await api.get("/backend/tasks/", { params });

    if (response && response.results) {
      trainTasks.value = response.results;
      taskPagination.value.rowsNumber = response.count || 0;
    } else {
      throw new Error("获取数据格式错误");
    }
  } catch (error) {
    console.error("获取训练任务列表失败", error);
    // taskError.value = '获取训练任务列表失败：' + (error.response?.data?.message || error.message)
    // 使用固定数据，不显示错误信息
    console.log("使用固定模拟数据展示卡片");
  } finally {
    loadingTasks.value = false;
  }
}

// 处理任务表格分页请求
function onTaskRequest(props) {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;
  taskPagination.value.page = page;
  taskPagination.value.rowsPerPage = rowsPerPage;
  taskPagination.value.sortBy = sortBy || "start_time";
  taskPagination.value.descending = descending;

  fetchTrainTasks();
}

// 表格列定义
const trainColumns = [
  { name: "name", field: "name", label: "训练名称", align: "center" },
  {
    name: "allocation",
    label: "资源配置",
    align: "center",
    format: (val, row) =>
      `${
        row.actor_num * row.actor_per_cpu + row.learner_num * row.learner_per_cpu ||
        "未知"
      } CPU`,
  },
  {
    name: "runningTime",
    field: "running_time",
    label: "训练时长",
    align: "center",
    sortable: true,
    format: (val) => convertSecondsToHMS(val),
  },
  { name: "status", field: "status", label: "训练状态", align: "center", sortable: true },
];

// 训练任务操作函数
function showLog(id) {
  console.log("查看日志:", id);
  // 实际项目中应调用日志对话框
}

function suspendTraining(row) {
  dialog("确认中断吗？").onOk(() => {
    api
      .post(`backend/tasks/suspending/`, {
        task_id: row.task_id,
      })
      .then(() => {
        notify("中断成功", "positive");
        fetchTrainTasks();
      })
      .catch(() => {
        notify("中断失败");
      });
  });
}

function resumeTraining(row) {
  dialog("确认恢复吗？").onOk(() => {
    api
      .post(`backend/tasks/resume/`, {
        task_id: row.task_id,
      })
      .then(() => {
        notify("恢复成功", "positive");
        fetchTrainTasks();
      })
      .catch(() => {
        notify("恢复失败");
      });
  });
}

function onDelete(id) {
  dialog("确认删除吗？").onOk(() => {
    api
      .delete(`backend/tasks/${id}/`)
      .then(() => {
        notify("删除成功", "positive");
        fetchTrainTasks();
      })
      .catch(() => {
        notify("删除失败");
      });
  });
}

// 初始化图表
function initCharts() {
  // 初始化雷达图
  if (radarChart.value) {
    radarChartInstance = echarts.init(radarChart.value);
    const radarOption = {
      title: {
        text: "",
      },
      tooltip: {
        // trigger: 'axis',
        axisPointer: {
          type: "shadow",
        },
        textStyle: {
          fontSize: window.screen.width > 1536 ? "20" : "14", // 文字大小设为14px
          color: "#333", // 文字颜色设为#333
        },
      },
      legend: {
        show: false,
        data: ["模型性能"],
        textStyle: {
          color: "#fff",
          fontSize: 14,
        },
        top: "0px",
        left: "0px",
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "15%",
        top: "15%",
        containLabel: true,
      },
      radar: {
        shape: "polygon",
        // 全局设置指标文字大小为14px
        name: {
          textStyle: {
            // color: '#333', origin
            color: "#fff",
            padding: [3, 5],
          },
        },
        indicator: [
          { name: "累计奖励", max: 100 },
          { name: "稳定性", max: 100 },
          { name: "收敛速度", max: 100 },
          { name: "复杂度", max: 100 },
          { name: "样本效率", max: 100 },
          { name: "鲁棒性", max: 100 },
        ],
        axisLine: {
          lineStyle: {
            // color: '#ddd' origin
            color: "#fff",
          },
        },
        splitLine: {
          lineStyle: {
            color: "#fff",
          },
        },
        splitArea: {
          show: true,
          areaStyle: {
            color: ["#f2f3f5", "#121724"],
          },
        },
      },
      textStyle: { fontSize: window.screen.width > 1536 ? "22" : "14" },
      series: [
        {
          name: "模型性能",
          type: "radar",
          areaStyle: {
            normal: {
              opacity: 0.5,
              color: "rgba(25, 183, 182, 0.4)", // 区域色
            },
          },
          lineStyle: {
            color: "rgb(25, 183, 182)", // 区域线条
          },
          data: [
            {
              value: [85, 90, 78, 88, 92, 65],
              name: "模型性能",
            },
          ],
        },
      ],
    };
    radarChartInstance.setOption(radarOption);
  }
}

// 窗口大小改变时重置图表大小
function resizeCharts() {
  radarChartInstance?.resize();
}

// 组件挂载时获取数据和初始化图表
onMounted(() => {
  fetchAlgorithms();
  // fetchTrainTasks() // 注释掉API调用，使用固定卡片数据
  initCharts();
  nextTick(() => {
    startMarquee(); // 页面加载后立即启动跑马灯
    window.addEventListener("resize", resizeCharts);
  });
});

// 组件销毁前清理事件监听
onBeforeUnmount(() => {
  window.removeEventListener("resize", resizeCharts);
  radarChartInstance?.dispose();
  clearInterval(intervalId);
});

// 跳转到强化学习界面
const startTraining = async () => {
  try {
    const timestamp = new Date().getTime();
    const nameWithTimestamp = `${selectedAlgorithmName.value}_${timestamp}`;

    const response = await createWorkFlow({
      name: nameWithTimestamp,
      training_type: "reinforcement_learning",
      task_type: "object_detection_yolov8",
    });
    if (response.success === true && response.task_id) {
      router.push({
        path: "/ai-model/reinforcementStudyForm",
        query: {
          task_id: response.task_id,
        },
      });
    }
  } catch (error) {
    console.error("创建训练任务工作流失败:", error);
    notify("创建训练任务失败", "negative");
  }
};

// 跳转到模型管理页面并打开上传模型对话框
function importModel() {
  // 跳转到模型管理页面
  router.push({
    path: "/system/model", // 正确的模型管理页面路径
    query: {
      openUploadDialog: "true", // 传递参数，表示需要打开上传对话框
    },
  });
}

// 跳转到数据集管理页面并打开上传数据集对话框
function uploadDataset() {
  // 跳转到数据集管理页面
  router.push({
    path: "/system/dataset", // 数据集管理页面路径
    query: {
      openUploadDialog: "true", // 传递参数，表示需要打开上传对话框
    },
  });
}

// 跳转到强化学习页面
function startValidation() {
  // 获取当前选中的算法
  const selectedAlgo = algorithms.value.find(
    (algo) => algo.id === currentAlgorithm.value
  );

  // 跳转到强化学习页面
  router.push({
    path: "/ai-model/reinforcement", // 强化学习页面路径
    query: {
      algorithm_id: selectedAlgo?.id,
      algorithm_name: selectedAlgo?.name,
      algorithm_version: selectedAlgo?.version,
      validation_mode: "true", // 标记为验证模式
    },
  });
}

// 添加算法
function addAlgorithm() {
  showAddAlgorithmDialog.value = true;
}

// 提交添加算法表单
async function onSubmitAlgorithm() {
  try {
    submitting.value = true;
    const response = await api.post("/backend/algorithms/", newAlgorithm.value);

    if (response.data) {
      notify("添加算法成功", "positive");
      showAddAlgorithmDialog.value = false;
      // 重置表单
      newAlgorithm.value = {
        name: "",
        artifact_name: "",
        desc: "",
      };
      // 刷新算法列表
      fetchAlgorithms();
    }
  } catch (error) {
    console.error("添加算法失败:", error);
    notify(
      "添加算法失败: " + (error.response?.data?.message || error.message),
      "negative"
    );
  } finally {
    submitting.value = false;
  }
}
</script>

<style lang="scss" scoped>
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .custom {
    /* 仅在Chrome中生效，设置padding-bottom为0 */
    padding-bottom: 0 !important;
  }
}
.mt90 {
  margin-top: 1.125rem;
}

.q-page {
  background-color: #f5f7fa;
}

.algorithm-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  height: 100%;
  position: relative;
  width: 3.125rem;
  height: 1.875rem;

  .imgType {
    position: absolute;
    left: 0.125rem;
    top: 0.075rem;
    font-size: 0.15rem;
    color: $slideText;
    padding: 0.075rem;
    border: 0.0125rem solid #4ab4ff;
    border-radius: 0.0625rem;
    background: rgba(87, 88, 89, 0.6);
  }

  .imgName {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 0.55rem;
    line-height: 0.55rem;
    font-size: 0.15rem;
    text-align: center;
    background: rgba(0, 0, 0, 0.45);
    color: $slideText;
  }
}

.algorithm-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.selected-algorithm {
  border-color: var(--q-primary);
  box-shadow: 0 0 10px rgba(var(--q-primary-rgb), 0.3);
}

/* 确保文本不会溢出 */
.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.myCard {
  background: transparent !important;
  display: flex;
  border: none;
}

.showBorder {
  background: $tableBg;
  border: 1px solid transparent;
  border-image: linear-gradient(to bottom, #64778a, #293f64) 1;
}

.line {
  margin-top: 0.25rem;
  position: absolute;
  left: 0;
  top: 40px;
  width: 100%;
}

.cardContent {
  height: 80px;
  width: 100%;
  // padding: 10px 0px 0px 20px;
}

.mr {
  margin: 0 10px;
}

.mb {
  margin-bottom: 10px;
}

.leftTag {
  position: absolute;
  left: 10px;
  top: 6px;
  border: 1px solid #4083bc;
  border-radius: 5px;
  background: rgba(87, 88, 89, 0.5);
}

.title {
  font-size: 0.3rem;
  font-weight: 600;
}

.content {
  font-size: 0.225rem;
}

.font16 {
  font-size: 0.2rem;
}

.font14 {
  font-size: 0.175rem;
}

.quard-item {
  width: 3.125rem;
  height: 1.875rem;
  margin-right: 0.5rem;
}

.q-table__container {
  margin-top: 0.25rem;
  border: none !important;
  padding: 0 !important;
  min-height: 4.9rem;
  padding-left: 0.1875rem !important;
  padding-right: 0.1875rem !important;
  // padding-top:0 !important;
  // padding-bottom: .5rem !important;
  // box-shadow: 0 .0125rem .0625rem #0003, 0 .025rem .025rem #00000024, 0 .0375rem .0125rem -0.025rem #0000001f;
}

.mb10 {
  margin-bottom: 0.125rem;
}

.marquee-container {
  width: 100%;
  height: 3rem;
  margin: 0 auto;
  overflow: hidden;
  position: relative;
  background: $tableBg;
  margin: 0.125rem 0;
}

.marquee-content {
  display: flex;
  white-space: nowrap;
  width: max-content;
  padding: 0;
  list-style-type: none;
  margin-top: 0.35rem;
}

.marquee-item {
  display: inline-block;
  transition: transform 0.3s;
}

.marquee-item:hover {
  cursor: pointer;
  transform: scale(1.1);
}

.marquee-item.enlarged {
  z-index: 2;
}

.marquee-item.enlarged .algorithm-card {
  transform: scale(1.1);
  transition: transform 0.3s;
  box-shadow: 0 0 0.0625rem #fff;
}

.algotitle {
  padding-top: 0.2rem;
  padding-left: 0.2rem;
}

.botLine {
  position: relative;
  padding-left: 0.2rem;
  padding-right: 0.2rem;

  &::after {
    content: "";
    position: absolute;
    bottom: -0.125rem;
    left: 0;
    width: 100% !important;
    height: 0.0125rem;
    background: #2a3f63;
  }
}

.marquee-item .algorithm-card {
  width: 100%;
}

.cus-bg {
  border-radius: 0.0625rem;
  background: $primary;
  border: 0.0125rem solid transparent;
  border-radius: 0.0625rem;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image: linear-gradient(to right, $primary, $primary),
    linear-gradient(to right, #64778a, #293f64);
}

.cusCard {
  padding-top: 0.25rem;
}

.textSize {
  font-size: 0.3rem;
}

.radar {
  height: 5rem;
}

@media screen and (max-width: 1920px) {
  .radar {
    height: 5.6rem !important;
  }

  .evaluation-content {
    min-height: 5rem !important;
  }
}

/* 新布局样式 */
.evaluation-content {
  display: flex;
  align-items: center;
  min-height: 4.5rem;
}

.radar-chart-container {
  width: 100%;
  height: 4.5rem;
  min-height: 4.5rem;
}

/* 独立开始训练按钮样式 */
.training-section {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
}

.training-btn {
  width: 100%;
  min-height: 60px;
}

.training-btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 10px;
}

.entry-icon {
  width: 20px;
  height: 20px;
}

/* 训练任务卡片样式 */
.task-cards-container {
  max-height: 9.8rem;
  // border: 1px solid tomato;
  overflow-y: auto;
  padding: 0;
}

.task-card {
  box-shadow: 0 0.0625rem 0.375rem -0.0625rem rgba(0, 0, 0, 0.15);
}

.task-card-inner {
  transition: all 0.3s ease;
}

.task-card-inner:hover {
  transform: translateY(-0.0625rem);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-name {
  font-weight: 600;
  font-size: 0.2rem;
}

.task-status {
  display: flex;
  align-items: center;
}

.status-label {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  background: rgba(255, 255, 255, 0.1);
  font-size: 0.175rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  font-size: 0.175rem;
}

.info-label {
  color: rgba(255, 255, 255, 0.7);
}

.info-value {
  color: #fff;
  font-weight: 500;
}

.task-actions {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.status-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-label-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.175rem;
}

.status-value {
  font-weight: 500;
  font-size: 0.175rem;
}

/* 滚动条样式 */
.task-cards-container::-webkit-scrollbar {
  width: 6px;
}

.task-cards-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.task-cards-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.task-cards-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
