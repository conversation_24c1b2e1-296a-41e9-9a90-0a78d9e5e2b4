import ray
from ray import tune
from ray.rllib.algorithms.ppo import PPOConfig
from ray.rllib.connectors.env_to_module import MeanStdFilter
from ray.rllib.core.rl_module.default_model_config import DefaultModelConfig
import argparse
import os
import subprocess
import time


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--checkpoint-path", type=str, default="",
                      help="检查点路径，如果不指定则使用随机动作")
    parser.add_argument("--num-env-runners", type=int, default=4,
                      help="环境运行器数量")
    parser.add_argument("--num-learners", type=int, default=1,
                      help="学习器数量")
    parser.add_argument("--num-gpus", type=int, default=1,
                      help="GPU数量")
    parser.add_argument("--local-dir", type=str, default="~/ray_results",
                      help="结果保存路径")
    parser.add_argument("--tensorboard-dir", type=str, default="~/tensorboard_logs",
                      help="TensorBoard日志路径")
    parser.add_argument("--training-iterations", type=int, default=2000,
                      help="训练迭代次数")

    return parser.parse_args()


def get_config(args):
    config = (
        PPOConfig()
        .environment("Pendulum-v1")
        .framework("torch")
        .resources(num_gpus=args.num_gpus)
        .env_runners(
            num_env_runners=args.num_env_runners,
            rollout_fragment_length='auto',
            num_envs_per_env_runner=1,
            num_cpus_per_env_runner=1,
            num_gpus_per_env_runner=0,
        )
        .learners(
            num_learners=1,
            num_cpus_per_learner=1,
            num_gpus_per_learner=1
        )
        .training(
            gamma=0.99,
            lr=5e-5,
            train_batch_size=16384,
            lambda_=0.95,
            clip_param=0.2,
            vf_clip_param=10.0,
            entropy_coeff=0.01,
            num_sgd_iter=10,
            minibatch_size=2048,
        )
        .evaluation(
            evaluation_interval=10,
            evaluation_duration=20,
            evaluation_num_workers=1,
            evaluation_config={
                "render_mode": "txt",
                "explore": False,
            },
        )
    )
    
    # 清空探索配置
    config.exploration_config = {}
    return config

if __name__ == "__main__":
    args = parse_args()

    timestamp = time.strftime("%Y%m%d-%H%M%S")
    experiment_name = "Pendulum-v1"
    
    local_dir = args.local_dir
    
    # 启动tensorboard
    subprocess.Popen(['tensorboard --logdir ~/ray_results/' + experiment_name + ' --bind_all'], shell=True)
    # subprocess.Popen([f'tensorboard --logdir {local_dir} {experiment_name} --bind_all'], shell=True)
    
    # 初始化Ray
    ray.init()
    
    # 获取配置
    # config = get_config(args)
    config = (
        PPOConfig()
        .environment("Pendulum-v1")
        .env_runners(
            num_env_runners=16,
            num_cpus_per_env_runner=4,
            num_gpus_per_env_runner=0,
            num_envs_per_env_runner=20,
            # env_to_module_connector=lambda env, spaces, device: MeanStdFilter(),
        )
        .learners(
            num_learners=1,
            num_cpus_per_learner=1,
            num_gpus_per_learner=1
        )
        .training(
            train_batch_size=16384,
            minibatch_size=256,
            lr=0.0002 * (args.num_learners or 1) ** 0.5,
            gamma=0.95,
            lambda_=0.5,
            # num_epochs=8,
        )
        # .rl_module(
        #     model_config=DefaultModelConfig(fcnet_activation="relu"),
        # )
    )
    
    # 运行训练
    tune.run(
        "PPO",
        config=config,
        stop={
            "training_iteration": args.training_iterations,
        },
        checkpoint_freq=50,
        checkpoint_at_end=True,
        # local_dir=os.path.expanduser(args.local_dir),
        storage_path=os.path.expanduser(local_dir),
        name=experiment_name,
        verbose=2,
    )
