# 统一训练工作流前端调用指南

## 概述

本文档说明前端如何正确调用后端的统一训练工作流API接口，包括各个场景下应该调用哪个接口以及如何处理响应。

## 核心接口列表

### 1. 创建训练工作流
**场景：** 点击"开始训练"按钮时
**接口：** `POST /backend/workflows/create/`
**前端调用：** `workflowApi.createWorkflow(data)`

### 2. 跳转到指定步骤
**场景：** 点击右侧任务列表中的任务时
**接口：** `POST /backend/workflows/jump/`
**前端调用：** `workflowApi.jumpToStep(data)`

### 3. 保存步骤数据
**场景：** 前两步点击"下一步"按钮时
**接口：** `POST /backend/workflows/step/save/`
**前端调用：** `workflowApi.saveStepData(data)`

### 4. 提交模型训练
**场景：** 第三步点击"开始训练"按钮时
**接口：** `POST /backend/workflows/training/submit/`
**前端调用：** `workflowApi.submitTraining(data)`

### 5. 训练控制
**场景：** 第三步的暂停/恢复/停止按钮时
**接口：** `POST /backend/workflows/training/control/`
**前端调用：** `workflowApi.controlTraining(data)`

### 6. 完成步骤
**场景：** 第三步点击"下一步"按钮时
**接口：** `POST /backend/workflows/step/complete/`
**前端调用：** `workflowApi.completeStep(data)`

### 7. 获取任务列表
**场景：** 加载右侧任务列表时
**接口：** `GET /backend/workflows/list/`
**前端调用：** `workflowApi.getWorkflowList(params)`

## 详细调用说明

### 1. 创建训练工作流

**使用场景：** 用户在首页点击"开始训练"按钮

```javascript
// 调用方式
const result = await workflowStore.createWorkflow({
  name: '训练任务名称',
  description: '任务描述',
  training_type: 'deep_learning', // deep_learning/reinforcement_learning/large_model
  task_type: 'object_detection_yolov8', // 具体任务类型
  model_name: '模型名称'
})

// 成功响应
{
  success: true,
  message: '训练任务创建成功',
  task_id: 'uuid-string',
  workflow_id: 123,
  model_info_id: 456,
  training_type: 'deep_learning',
  current_step: 1,
  status: 'draft'
}

// 后续操作：跳转到第一步
const firstStepRoute = getPageRoute(result.training_type, 1)
await router.push(firstStepRoute)
```

### 2. 跳转到指定步骤

**使用场景：** 用户点击右侧任务列表中的某个任务

```javascript
// 调用方式
const jumpResult = await workflowStore.jumpToStep(task.task_id)

// 成功响应
{
  success: true,
  data: {
    task_id: 'uuid-string',
    workflow_id: 123,
    name: '任务名称',
    training_type: 'deep_learning',
    task_type: 'object_detection_yolov8',
    current_step: 2,
    target_step: 2,
    target_page: 'step2',
    step_name: '训练参数配备',
    step_data: { /* 步骤配置数据 */ },
    status: 'step1_saved',
    training_status: 'not_started',
    progress_percentage: 40,
    can_proceed: true,
    next_step_number: 3,
    training_data: { /* 训练相关数据，仅第三步有 */ }
  }
}

// 后续操作：跳转到目标页面
const targetRoute = getPageRoute(jumpResult.training_type, jumpResult.target_step)
await router.push(targetRoute)
```

### 3. 保存步骤数据

**使用场景：** 第一步和第二步点击"下一步"按钮

```javascript
// 调用方式
const result = await workflowStore.saveStepData(stepNumber, stepData)

// 第一步示例
await workflowStore.saveStepData(1, {
  dataset_id: 'dataset-123',
  dataset_name: '数据集名称',
  train_percent: 70,
  validation_percent: 20,
  test_percent: 10,
  analysis_result: { /* 数据分析结果 */ }
})

// 第二步示例
await workflowStore.saveStepData(2, {
  epochs: 100,
  batch_size: 32,
  learning_rate: 0.001,
  optimizer: 'adam',
  model_config: { /* 模型配置 */ }
})

// 成功响应
{
  success: true,
  message: '步骤X数据保存成功',
  current_step: 2, // 自动进入下一步
  status: 'step1_saved',
  next_step_number: 3
}
```

### 4. 提交模型训练

**使用场景：** 第三步点击"开始训练"按钮

```javascript
// 调用方式
const result = await workflowStore.submitTraining(trainingConfig)

// 请求参数示例
const trainingConfig = {
  training_params: {
    epochs: 100,
    batch_size: 32,
    learning_rate: 0.001
  },
  hardware_config: {
    gpu_count: 1,
    memory_limit: '8GB'
  }
}

// 成功响应
{
  success: true,
  message: '训练任务已提交',
  training_task_id: 'training-uuid',
  status: 'training',
  training_status: 'running'
}
```

### 5. 训练控制

**使用场景：** 第三步的暂停/恢复/停止按钮

```javascript
// 暂停训练
await workflowStore.controlTraining('pause')

// 恢复训练
await workflowStore.controlTraining('resume')

// 停止训练
await workflowStore.controlTraining('stop')

// 成功响应
{
  success: true,
  message: '训练已暂停/恢复/停止',
  training_status: 'paused/running/stopped'
}
```

### 6. 完成步骤

**使用场景：** 第三步训练完成后点击"下一步"按钮

```javascript
// 调用方式
const result = await workflowStore.completeStep(3, finalMetrics)

// 请求参数示例
const finalMetrics = {
  final_accuracy: 0.95,
  final_loss: 0.05,
  training_time: 3600
}

// 成功响应
{
  success: true,
  message: '训练完成，进入评估阶段',
  current_step: 4,
  status: 'training_completed',
  training_status: 'completed'
}
```

### 7. 获取任务列表

**使用场景：** 加载右侧任务列表

```javascript
// 调用方式
const result = await workflowStore.fetchWorkflowList({
  page: 1,
  page_size: 10,
  training_type: 'deep_learning', // 可选筛选
  status: 'training' // 可选筛选
})

// 成功响应
{
  workflows: [
    {
      task_id: 'uuid-string',
      name: '任务名称',
      training_type: 'deep_learning',
      status: 'training',
      training_status: 'running',
      current_step: 3,
      step_name: '提交模型训练',
      progress_percentage: 60,
      created_at: '2024-01-01T00:00:00Z',
      status_display: '训练中',
      training_status_display: '运行中'
    }
  ],
  total: 50,
  page: 1,
  page_size: 10
}
```

## 常见错误处理

### 1. 任务不存在
```javascript
{
  success: false,
  error: '训练任务不存在'
}
```

### 2. 权限不足
```javascript
{
  success: false,
  error: '无权限访问该任务'
}
```

### 3. 状态不允许
```javascript
{
  success: false,
  error: '当前状态不允许此操作'
}
```

## 前端状态管理

### workflowStore 主要状态
```javascript
{
  currentWorkflow: {
    task_id: 'uuid-string',
    workflow_id: 123,
    name: '任务名称',
    training_type: 'deep_learning',
    current_step: 2,
    status: 'step1_saved',
    training_status: 'not_started'
  },
  trainingMetrics: { /* 训练指标 */ },
  trainingLogs: [ /* 训练日志 */ ],
  loading: false
}
```

### 计算属性
```javascript
const currentTaskId = computed(() => currentWorkflow.value?.task_id)
const currentStep = computed(() => currentWorkflow.value?.current_step || 1)
const trainingType = computed(() => currentWorkflow.value?.training_type)
```

## 路由跳转

### 获取目标路由
```javascript
import { getPageRoute } from 'src/services/workflowApi'

// 根据训练类型和步骤号获取路由
const route = getPageRoute('deep_learning', 2)
// 返回: '/training-workflow/deep-learning/step2'

await router.push(route)
```

## 注意事项

1. **右侧任务列表点击** → 必须调用 `jumpToStep` 接口，不能调用 `createWorkflow`
2. **前两步下一步** → 调用 `saveStepData` 接口保存数据并自动进入下一步
3. **第三步开始训练** → 调用 `submitTraining` 接口
4. **第三步下一步** → 调用 `completeStep` 接口
5. **所有接口都需要认证** → 确保请求头包含有效的 Authorization token
6. **错误处理** → 所有接口调用都应该包含 try-catch 错误处理
7. **状态同步** → 接口调用成功后要更新 workflowStore 中的状态
