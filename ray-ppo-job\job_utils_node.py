import yaml
from kubernetes import client,config
from kubernetes.client.rest import ApiException
import copy


def get_cluster_resources():

    cluster_resources = []
    v1 = client.CoreV1Api()

    nodes = v1.list_node().items
    for node in nodes:
        allocatable = node.status.allocatable
        cpu = int(allocatable['cpu'])  # Convert CPU cores to integer
        memory_kib = int(allocatable['memory'][:-2])  # Memory is provided in Ki, convert to integer
        memory_gib = memory_kib / (1024 ** 2)  # Convert memory from Ki to Gi
        gpu = int(allocatable.get('nvidia.com/gpu', 0))  # GPU might not be available
        
        cluster_resources.append({
            'cpu': int(cpu*0.85),
            'memory': int(memory_gib*0.85),  # Memory now in Gi
            'gpu': gpu,
            'name': node.metadata.name
        })
    return cluster_resources

def calculate_worker_configuration(
    cluster_resources, 
    actor_num, actor_cpu_num, actor_gpu_num, actor_mem_num, 
    learner_num, learner_cpu_num, learner_gpu_num, learner_mem_num):
    # 将节点按资源从大到小排序，优先处理 GPU 节点
    sorted_nodes = sorted(cluster_resources, key=lambda x: (x['gpu'], x['cpu'], x['memory']), reverse=True)
    worker_configurations = []
    remaining_learners_cpu=learner_num * learner_cpu_num
    remaining_learners_gpu=learner_num * learner_gpu_num
    remaining_learners_mem=learner_num * learner_mem_num
    remaining_actors_cpu=actor_num * actor_cpu_num
    remaining_actors_gpu=actor_num * actor_gpu_num
    remaining_actors_mem=actor_num * actor_mem_num
    print(f'actor nodes: {sorted_nodes}')
    for node in sorted_nodes:
        # 处理 Learners
        # 计算当前节点可以容纳的 Learners 数量
        max_learners = max(min(
            node['cpu'] // remaining_learners_cpu if remaining_learners_cpu > 0 else float('inf'),
            node['gpu'] // remaining_learners_gpu if remaining_learners_gpu > 0 else float('inf'),
            node['memory'] // remaining_learners_mem if remaining_learners_mem > 0 else float('inf')
        ),1)
        
        # 分配 Learners 到当前节点
        worker_configurations.append({
            'replicas': 1,
            'minReplicas': 1,
            'maxReplicas': max_learners,
            'groupName':'learner-group',
            'cpu': min(remaining_learners_cpu,node['cpu']),
            'memory': f"{min(remaining_learners_mem,node['memory'])}Gi",
            'gpu': min(remaining_learners_gpu,node['gpu']),
            'node': node['name'],
            'type': 'learner'
        })
        # 更新节点资源
        node['gpu'] -= min(remaining_learners_gpu,node['gpu'])
        node['cpu'] -= min(remaining_learners_cpu,node['cpu'])
        node['memory'] -= min(remaining_learners_mem,node['memory'])

        remaining_learners_cpu-=min(remaining_learners_cpu,node['cpu'])
        remaining_learners_gpu-=min(remaining_learners_gpu,node['gpu'])
        remaining_learners_mem-=min(remaining_learners_mem,node['memory'])
        
        if remaining_learners_cpu > 0 or remaining_learners_gpu>0 or remaining_learners_mem>0:
            continue
        else:
            break

    print(f'actor nodes: {sorted_nodes}')
    for node in sorted_nodes:
        
        # 处理 Actors
        # 计算当前节点可以容纳的 Actors 数量
        max_actors = max(min(
            node['cpu'] // remaining_actors_cpu if remaining_actors_cpu > 0 else float('inf'),
            node['gpu'] // remaining_actors_gpu if remaining_actors_gpu > 0 else float('inf'),
            node['memory'] // remaining_actors_mem if remaining_actors_mem > 0 else float('inf')
        ),1)
        
        # 分配 Actors 到当前节点
        worker_configurations.append({
            'replicas': 1,
            'minReplicas':1,
            'maxReplicas':max_actors,
            'groupName':'actor-group',
            'cpu': min(remaining_actors_cpu,node['cpu']),
            'memory': f"{min(remaining_actors_mem,node['memory'])}Gi",
            'gpu':  min(remaining_learners_gpu,node['gpu']),
            'node': node['name'],
            'type': 'actor'
        })
        # 更新节点资源
        node['gpu'] -= min(remaining_actors_gpu,node['gpu'])
        node['cpu'] -= min(remaining_actors_cpu,node['cpu'])
        node['memory'] -= min(remaining_actors_mem,node['memory'])

        remaining_actors_cpu-=min(remaining_actors_cpu,node['cpu'])
        remaining_actors_gpu-=min(remaining_actors_gpu,node['gpu'])
        remaining_actors_mem-=min(remaining_actors_mem,node['memory'])
        
        if remaining_actors_cpu > 0 or remaining_actors_gpu>0 or remaining_actors_mem>0:
            continue
        else:
            break
        
    return worker_configurations




def submit_ray_job(
                   entrypoint: str = 'python /root/ray-gym/train.py',
                   run_image: str = 'core.**************.nip.io:30670/public/ray-gym-cartpole:latest',
                   ray_version: str = '2.7.0',
                   actor_num: int = 64,
                   actor_cpu_num: int = 1,
                   actor_gpu_num: int = 0,
                   actor_mem_num: int = 2,
                   learner_num: int = 1,
                   learner_cpu_num: int = 4,
                   learner_gpu_num: int = 1,
                   learner_mem_num: int = 4,
                   namespace: str = 'rl-platform',
                   ray_job_name: str = 'ray-job'):
    
    config.load_kube_config()
    api_instance = client.CustomObjectsApi()
    


    # 获取集群资源信息
    cluster_resources = get_cluster_resources()

    # 动态计算 worker 节点的配置
    worker_configurations = calculate_worker_configuration(
        cluster_resources, 
        actor_num,actor_cpu_num, actor_gpu_num, actor_mem_num, 
        learner_num,learner_cpu_num, learner_gpu_num, learner_mem_num)

    # 根据计算的配置更新 YAML
    worker_specs = []
    for conf in worker_configurations:
        worker_spec = {
            'replicas': conf['replicas'],
            'minReplicas': conf['minReplicas'],
            'maxReplicas': conf['maxReplicas'],
            'groupName': conf['groupName'],
            'rayStartParams': {},
            'template': {
                'spec': {
                    'nodeName': conf['node'],  # 指定节点
                    'containers': [{
                        'name': 'ray-worker',
                        'image': run_image,
                        'imagePullPolicy': 'IfNotPresent',
                        'resources': {
                            'limits': {
                                'cpu': conf['cpu'],
                                'memory': conf['memory'],
                                'nvidia.com/gpu': conf['gpu'] if conf['gpu'] > 0 else None
                            },
                            'requests': {
                                'cpu': conf['cpu'],
                                'memory': conf['memory'],
                                'nvidia.com/gpu': conf['gpu'] if conf['gpu'] > 0 else None
                            }
                        },
                        'volumeMounts': [{
                            'name': 'shared-volume',
                            'mountPath': '/mnt/shared'  
                        }]
                       
                  
                        
                    }],
                    'volumes':[{
                       'name': 'shared-volume',
                       'persistentVolumeClaim':{
                           'claimName': 'common-pvc'
                        }
                    }]
                  
              
                }
            }
        }
        worker_specs.append(worker_spec)
    
   


    # Load the YAML template
    with open('job.yaml') as f:
        ray_job_yaml = yaml.safe_load(f)

    entrypoint += f' --num_actors {actor_num} --actor_cpu_num {actor_cpu_num} --actor_gpu_num {actor_gpu_num} --actor_mem_num {actor_mem_num} --learner_cpu_num {learner_cpu_num} --learner_gpu_num {learner_gpu_num} --learner_mem_num {learner_mem_num}'
    ray_job_yaml['spec']['rayClusterSpec']['workerGroupSpecs'] = worker_specs

    # Update YAML fields dynamically
    ray_job_yaml['metadata']['name'] = ray_job_name
    ray_job_yaml['metadata']['namespace'] = namespace
    ray_job_yaml['spec']['entrypoint']  =entrypoint
    ray_job_yaml['spec']['rayClusterSpec']['rayVersion'] = ray_version
    ray_job_yaml['spec']['rayClusterSpec']['headGroupSpec']['template']['spec']['containers'][0]['image'] = run_image
    
    # # Update worker group specs
    # gpu_group = ray_job_yaml['spec']['rayClusterSpec']['workerGroupSpecs'][0]
    # gpu_group['template']['spec']['containers'][0]['image'] = run_image

    # cpu_group = ray_job_yaml['spec']['rayClusterSpec']['workerGroupSpecs'][1]
    # cpu_group['template']['spec']['containers'][0]['image'] = run_image

    # Create RayJob in Kubernetes
    try:
        api_response = api_instance.create_namespaced_custom_object(
            group="ray.io",
            version="v1",
            namespace=namespace,
            plural="rayjobs",
            body=ray_job_yaml
        )
        print(api_response)
        print("RayCluster created successfully!")
    except ApiException as e:
        print(f"Exception when creating RayCluster: {e}")

if __name__ == '__main__':
    submit_ray_job()
