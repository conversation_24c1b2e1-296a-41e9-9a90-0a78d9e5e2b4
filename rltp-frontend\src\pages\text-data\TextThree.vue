<!--
 * @Author: Szc
 * @Date: 2025-08-20 14:31:37
 * @LastEditors: Szc
 * @LastEditTime: 2025-08-20 14:31:41
 * @Description: 
-->
<template>
  <div class="content">
    <div class="top">
      <div class="left">
        <div class="info-table">
          <div
            v-for="(row, index) in tableData"
            :key="index"
            class="table-row"
            :class="{ selected: selectedIndex === index }"
            @click="selectRow(index)"
          >
            <div class="cell-left">{{ row.label }}</div>
            <div class="cell-right">
              <span>{{ row.count }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="right">
        <div class="info-table">
          <div class="onetop">
            <img src="../../assets/images/icon_dz.png" />
            <h2>轨迹数据列表</h2>
          </div>
          <div class="twotiele">
            <div class="left-content">
              <p>共 {{ totalCount }} 条数据，已选择 {{ selectedCount }} 条数据</p>
            </div>
            <div class="right-content">
              <div class="bts">
                <q-btn
                  class="amendment-btn roundBox"
                  color="primary"
                  label="一键修正"
                  @click="batchFix"
                >
                  <img
                    src="../../assets/reinforcementImages/icon_drxd.png"
                    class="btn-icon"
                  />
                </q-btn>
                <q-btn
                  class="save-btn roundBox"
                  color="primary"
                  label="保存数据"
                  @click="saveData"
                >
                  <img
                    src="../../assets/reinforcementImages/icon_save.png"
                    class="btn-icon"
                  />
                </q-btn>
              </div>
            </div>
          </div>
          <div class="three">
            <div class="q-pa-md">
              <q-table
                :rows="trajectoryData"
                :columns="columns"
                row-key="ID"
                :selected-rows-label="getSelectedString"
                selection="multiple"
                v-model:selected="selected"
              >
                <template #body-cell-ProblemTypes="{ row }">
                  <div
                    :class="getProblemTypeClass(row.ProblemTypes)"
                    class="problem-type-cell"
                  >
                    {{ row.ProblemTypes }}
                  </div>
                </template>
                <template #body-cell-Status="{ row }">
                  <div class="status-cell">
                    <img src="" class="status-photo" />
                    <span :class="getStatusClass(row.Status)">{{ row.Status }}</span>
                  </div>
                </template>
              </q-table>
            </div>
          </div>
          <div class="bottom">
            <div class="next">
              <q-btn
                class="prevBtn roundBox"
                color="grey-7"
                label="上一步"
                @click="prevStep"
              />
              <q-btn
                class="nextBtn roundBox"
                color="primary"
                label="下一步"
                @click="nextStep"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
const emit = defineEmits(["next-step", "prev-step"]);
const saveResult = () => {
  console.log("保存结果");
};

const nextStep = () => {
  emit("next-step");
};
const tableData = ref([
  { label: "样本总数量", count: 17500 },
  { label: "合格样本数量", count: 13497 },
  { label: "已处理样本数量", count: 399 },
  { label: "样本数据缺失", count: 218 },
  { label: "异常样本", count: 399 },
  { label: "重复样本", count: 218 },
]);
const trajectoryData = ref([
  {
    ID: "TRJ_0001",
    SampleContent: "轨迹点序列：[(116.025396,39.029872),...",
    vehicleType: "公交车",
    ProblemTypes: "重复数据",
    Status: "待处理",
  },
  {
    ID: "TRJ_0002",
    SampleContent: "轨迹点序列：[(116.025396,39.029872),...",
    vehicleType: "公交车",
    ProblemTypes: "异常数据",
    Status: "待处理",
  },
  {
    ID: "TRJ_0003",
    SampleContent: "轨迹点序列：[(116.025396,39.029872),...",
    vehicleType: "公交车",
    ProblemTypes: "正常数据",
    Status: "已处理",
  },
  {
    ID: "TRJ_0004",
    SampleContent: "轨迹点序列：[(116.025396,39.029872),...",
    vehicleType: "公交车",
    ProblemTypes: "缺失数据",
    Status: "已处理",
  },
]);
const columns = ref([
  { name: "ID", label: "ID", field: "ID", align: "center", style: "width: 1fr" },
  {
    name: "SampleContent",
    label: "样本内容",
    field: "SampleContent",
    align: "center",
    style: "width: 2fr",
  },
  {
    name: "vehicleType",
    label: "车辆类型",
    field: "vehicleType",
    align: "center",
    style: "width: 1fr",
  },
  {
    name: "ProblemTypes",
    label: "问题类型",
    field: "ProblemTypes",
    align: "center",
    style: "width: 1fr",
  },
  {
    name: "Status",
    label: "状态",
    field: "Status",
    align: "center",
    style: "width: 1fr",
  },
]);
const selectedIndex = ref(0);
const selectRow = (index) => {
  selectedIndex.value = index;
};
const totalCount = ref(17500);
const selectedCount = ref(4);

const batchFix = () => {
  // 实现一键修正逻辑
  console.log("执行一键修正");
};

const saveData = () => {
  // 实现保存数据逻辑
  console.log("保存数据");
};
const selected = ref([]);
const getStatusClass = (status) => {
  return status === "待处理" ? "pending-status" : "processed-status";
};
const getProblemTypeClass = (type) => {
  switch (type) {
    case "重复数据":
      return "duplicate-data";
    case "异常数据":
      return "abnormal-data";
    case "缺失数据":
      return "missing-data";
    case "正常数据":
      return "normal-data";
    default:
      return "";
  }
};
</script>

<style lang="scss" scoped>
.content {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%; // 确保根容器有高度

  .top {
    display: flex;
    margin-bottom: 0.125rem;
    min-height: 9.5rem;
    flex: 1;
    height: 100%; // 确保 top 容器有高度

    .left {
      width: 15%;
      height: inherit;
      border: 0.025rem solid #707070;
      background-color: #181a24;
      padding: 0.125rem;
      display: flex;
      flex-direction: column;
      margin-right: 0.125rem;
      background-color: #181a24;
      background-image: repeating-linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.05) 0,
        rgba(255, 255, 255, 0.01) 0.05rem,
        transparent 0.0125rem,
        transparent 0.1875rem
      );
      .info-table {
        flex: 1;
        overflow-y: auto;

        .table-row {
          display: flex;
          height: 0.48rem;
          align-items: center;
          &.selected {
            background: url("../../assets/images/bq_bg.png");

            .cell-left {
              color: white;
            }
            .cell-right {
              span {
                background: rgba(206, 163, 69, 0.2);
                color: rgba(206, 163, 69, 1);
              }
            }
          }
        }
        .cell-left {
          flex: 2;
          padding: 0.125rem;
          color: #9cacc6;
          font-size: 0.225rem;
        }
        .cell-right {
          flex: 1;
          padding: 0.125rem;
          color: #ffffff;
          font-size: 0.225rem;
          span {
            background: rgba(255, 255, 255, 0.1);
            border: 0.0125rem solid #333;
            border-radius: 0.25rem;
            padding: 0 0.1rem;
          }
        }
      }
    }
  }

  .right {
    width: 85%;
    height: inherit;
    border: 0.025rem solid #707070;
    background-color: #181a24;
    padding: 0.0625rem 0.3375rem;
    display: flex;
    flex-direction: column;
    background-color: #181a24;
    background-image: repeating-linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.05) 0,
      rgba(255, 255, 255, 0.01) 0.05rem,
      transparent 0.0125rem,
      transparent 0.1875rem
    );
    .info-table {
      height: 100%;
      .onetop {
        display: flex;
        align-items: center;
        height: 5%;
        h2 {
          height: 0.1875rem;
          line-height: 0.1875rem;
          color: #ffffff;
          font-size: 0.275rem;
        }
        img {
          width: 0.2rem;
          height: 0.2rem;
          margin-right: 0.1875rem;
        }
      }
      .twotiele {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 0.125rem;
        height: 10%;

        .left-content {
          color: #ffffff;
          font-size: 0.225rem;
        }
        .right-content {
          .bts {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.125rem;
            .amendment-btn,
            .save-btn {
              width: 1.9rem;
              position: relative;
              font-size: 0.275rem;
              .btn-icon {
                position: absolute;
                margin-left: 0.2rem;
                left: 0;
              }
              :deep(.q-btn__content) {
                margin-left: 0.3rem;
              }
            }
            .save-btn {
              :deep(.q-field__inner) {
                background: rgba(112, 112, 112, 0.2);
                font-size: 0.25rem;
              }
            }
          }
        }
      }
      .three {
        height: 75%;
        width: 100%;
        display: flex;
        flex-direction: column;
        .three-table {
          flex: 1;
          margin-bottom: 0.125rem;
        }
        .status-cell {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.1rem;
          height: 100%;
        }

        .status-photo {
          width: 0.3rem;
          height: 0.3rem;
          vertical-align: middle;
        }

        .pending-status {
          color: yellow;
        }

        .processed-status {
          color: green;
        }
        .problem-type-cell {
          border: 0.025rem solid;
          border-radius: 0.05rem;
          display: flex;
          align-items: center;
          justify-content: center; // 添加居中对齐
          height: 0.75rem;
        }

        .duplicate-data {
          border-color: blue;
          background-color: rgba(0, 0, 255, 0.1);
        }

        .abnormal-data {
          border-color: red;
          background-color: rgba(255, 0, 0, 0.1);
        }

        .missing-data {
          border-color: yellow;
          background-color: rgba(255, 255, 0, 0.1);
        }

        .normal-data {
          border-color: green;
          background-color: rgba(0, 255, 0, 0.1);
        }
      }
      // 底部按钮
      .bottom {
        float: right;
        width: 2.25rem;
        height: 10%;
        display: flex;
        align-items: center;
        flex-shrink: 0;
        margin-top: 0.25rem;

        .next {
          display: flex;
          justify-content: space-between;
          width: 100%;
          padding: 0 0.25rem;
          gap: 0.25rem;

          .prevBtn {
            margin-right: auto;
          }

          .nextBtn {
            margin-left: auto;
          }
        }
      }
    }
  }
  .right {
    position: relative;
    &::before {
      position: absolute;
      z-index: 10000;
      content: "";
      left: -0.1rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198);
      border-radius: 0.125rem;
    }

    &::after {
      position: absolute;
      content: "";
      left: -0.075rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198, 0.5);
    }
  }

  .left {
    margin-right: 0.125rem;
    position: relative;
    &::before {
      position: absolute;
      z-index: 10000;
      content: "";
      left: -0.1rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198);
      border-radius: 0.125rem;
    }

    &::after {
      position: absolute;
      content: "";
      left: -0.075rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198, 0.5);
    }
  }
}
</style>
