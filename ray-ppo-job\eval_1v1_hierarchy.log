2025-06-30 18:36:09,898	WARNING utils.py:596 -- Detecting docker specified CPUs. In previous versions of Ray, CPU detection in containers was incorrect. Please ensure that <PERSON> has enough CPUs allocated. As a temporary workaround to revert to the prior behavior, set `RAY_USE_MULTIPROCESSING_CPU_COUNT=1` as an env var before starting Ray. Set the env var: `RAY_DISABLE_DOCKER_CPU_WARNING=1` to mute this warning.
2025-06-30 18:36:10,984	INFO worker.py:1852 -- Started a local Ray instance.
2025-06-30 18:36:11,302	WARNING deprecation.py:50 -- DeprecationWarning: `config.training(num_sgd_iter=..)` has been deprecated. Use `config.training(num_epochs=..)` instead. This will raise an error in the future!
2025-06-30 18:36:11,306	WARNING deprecation.py:50 -- DeprecationWarning: `AlgorithmConfig.evaluation(evaluation_num_workers=..)` has been deprecated. Use `AlgorithmConfig.evaluation(evaluation_num_env_runners=..)` instead. This will raise an error in the future!
2025-06-30 18:36:11,309	WARNING algorithm_config.py:4704 -- You are running PPO on the new API stack! This is the new default behavior for this algorithm. If you don't want to use the new API stack, set `config.api_stack(enable_rl_module_and_learner=False,enable_env_runner_and_connector_v2=False)`. For a detailed migration guide, see here: https://docs.ray.io/en/master/rllib/new-api-stack-migration-guide.html
/home/<USER>/.conda/envs/ray/lib/python3.11/site-packages/ray/rllib/algorithms/algorithm.py:512: RayDeprecationWarning: This API is deprecated and may be removed in future Ray releases. You could suppress this warning by setting env variable PYTHONWARNINGS="ignore::DeprecationWarning"
`UnifiedLogger` will be removed in Ray 2.7.
  return UnifiedLogger(config, logdir, loggers=None)
/home/<USER>/.conda/envs/ray/lib/python3.11/site-packages/ray/tune/logger/unified.py:53: RayDeprecationWarning: This API is deprecated and may be removed in future Ray releases. You could suppress this warning by setting env variable PYTHONWARNINGS="ignore::DeprecationWarning"
The `JsonLogger interface is deprecated in favor of the `ray.tune.json.JsonLoggerCallback` interface and will be removed in Ray 2.7.
  self._loggers.append(cls(self.config, self.logdir, self.trial))
/home/<USER>/.conda/envs/ray/lib/python3.11/site-packages/ray/tune/logger/unified.py:53: RayDeprecationWarning: This API is deprecated and may be removed in future Ray releases. You could suppress this warning by setting env variable PYTHONWARNINGS="ignore::DeprecationWarning"
The `CSVLogger interface is deprecated in favor of the `ray.tune.csv.CSVLoggerCallback` interface and will be removed in Ray 2.7.
  self._loggers.append(cls(self.config, self.logdir, self.trial))
/home/<USER>/.conda/envs/ray/lib/python3.11/site-packages/ray/tune/logger/unified.py:53: RayDeprecationWarning: This API is deprecated and may be removed in future Ray releases. You could suppress this warning by setting env variable PYTHONWARNINGS="ignore::DeprecationWarning"
The `TBXLogger interface is deprecated in favor of the `ray.tune.tensorboardx.TBXLoggerCallback` interface and will be removed in Ray 2.7.
  self._loggers.append(cls(self.config, self.logdir, self.trial))


     JSBSim Flight Dynamics Model v1.2.2 Mar 22 2025 11:56:49
[36m(MultiAgentEnvRunner pid=130057)[0m 2025-06-30 18:36:13,678	WARNING deprecation.py:50 -- DeprecationWarning: `RLModule(config=[RLModuleConfig object])` has been deprecated. Use `RLModule(observation_space=.., action_space=.., inference_only=.., model_config=.., catalog_class=..)` instead. This will raise an error in the future!
2025-06-30 18:36:13,891	WARNING deprecation.py:50 -- DeprecationWarning: `RLModule(config=[RLModuleConfig object])` has been deprecated. Use `RLModule(observation_space=.., action_space=.., inference_only=.., model_config=.., catalog_class=..)` instead. This will raise an error in the future!
/home/<USER>/.conda/envs/ray/lib/python3.11/site-packages/gymnasium/envs/registration.py:642: UserWarning: [33mWARN: Overriding environment rllib-multi-agent-env-v0 already in registry.[0m
  logger.warn(f"Overriding environment {new_spec.id} already in registry.")
[36m(_WrappedExecutable pid=130066)[0m Setting up process group for: env:// [rank=0, world_size=1]
[36m(_WrappedExecutable pid=130066)[0m 2025-06-30 18:36:19,875	WARNING deprecation.py:50 -- DeprecationWarning: `RLModule(config=[RLModuleConfig object])` has been deprecated. Use `RLModule(observation_space=.., action_space=.., inference_only=.., model_config=.., catalog_class=..)` instead. This will raise an error in the future![32m [repeated 3x across cluster] (Ray deduplicates logs by default. Set RAY_DEDUP_LOGS=0 to disable log deduplication, or see https://docs.ray.io/en/master/ray-observability/user-guides/configure-logging.html#log-deduplication for more options.)[0m
2025-06-30 18:36:19,894	WARNING util.py:61 -- Install gputil for GPU system monitoring.
2025-06-30 18:36:19,984	INFO trainable.py:577 -- Restored on 10.244.5.121 from checkpoint: Checkpoint(filesystem=local, path=/home/<USER>/xiangshuai/projects/xs/ray-ppo-job/ray_results/jsbsim-1v1-bsl-hierarchy_20250628-014652/PPO_JSBSim-Combat_b6885_00000_0_2025-06-28_01-46-54/checkpoint_000035)
使用检查点: /home/<USER>/xiangshuai/projects/xs/ray-ppo-job/ray_results/jsbsim-1v1-bsl-hierarchy_20250628-014652/PPO_JSBSim-Combat_b6885_00000_0_2025-06-28_01-46-54/checkpoint_000035
[36m(MultiAgentEnvRunner pid=130057)[0m 
[36m(MultiAgentEnvRunner pid=130057)[0m 
[36m(MultiAgentEnvRunner pid=130057)[0m      JSBSim Flight Dynamics Model v1.2.2 Mar 22 2025 11:56:49
[36m(MultiAgentEnvRunner pid=130057)[0m jsbsim agents:  {'A0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7efe67010d10>, 'B0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7efe68cc09d0>}
[36m(MultiAgentEnvRunner pid=130058)[0m 
[36m(MultiAgentEnvRunner pid=130058)[0m 
jsbsim agents:  {'A0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f45381bd950>, 'B0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f45381bd490>}
[36m(MultiAgentEnvRunner pid=130068)[0m 
[36m(MultiAgentEnvRunner pid=130068)[0m 
jsbsim agents:  {'A0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f451e59b510>, 'B0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f451e7b4b50>}
jsbsim agents:  {'A0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f4330500cd0>, 'B0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f4188391990>}

Episode 0 completed:
Episode Length: 987
Agent A0100 Reward: 716.9309696938353
Agent B0100 Reward: 616.8460311138186
Agent A0100 status: alive
Agent B0100 status: dead
------------------------
Average reward over 1 episodes: 0.0
回放文件已保存到: /home/<USER>/xiangshuai/projects/xs/ray-ppo-job/eval_results/jsbsim-1v1-bsl-hierarchy_20250630-183619.acmi
评估结果已保存到: /home/<USER>/xiangshuai/projects/xs/ray-ppo-job/eval_results/jsbsim-1v1-bsl-hierarchy_20250630-183619.txt
[36m(MultiAgentEnvRunner pid=130068)[0m      JSBSim Flight Dynamics Model v1.2.2 Mar 22 2025 11:56:49[32m [repeated 2x across cluster][0m
[36m(MultiAgentEnvRunner pid=130068)[0m jsbsim agents:  {'A0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f559e9c1e90>, 'B0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f559f1ba990>}[32m [repeated 2x across cluster][0m
            [JSBSim-ML v2.0]

JSBSim startup beginning ...

