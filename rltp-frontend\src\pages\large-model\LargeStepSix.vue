<!--
 * @Author: Szc
 * @Date: 2025-01-01 10:00:00
 * @LastEditors: Szc
 * @LastEditTime: 2025-08-19 10:49:49
 * @Description: 大模型-第六步：模型部署推理
-->
<template>
    <div class="content">
        <div class="top">
            <div class="left">
                <div class="left-decoration"></div>
                <div class="model-management-section">
                    <div class="section-header">
                        <img class="arrow-icon" src="../../assets/images/icon_dz.png" alt="">
                        <span class="section-title">模型管理中心</span>
                    </div>

                    <!-- 模型选择 -->
                    <div class="model-selection">
                        <div class="selection-row">
                            <div class="selection-label">选择模型:</div>
                            <q-select v-model="selectedModel" :options="modelOptions" class="model-select" outlined
                                dense color="primary" option-label="label" option-value="value"
                                placeholder="-- 请选择模型 --" @update:model-value="onModelChange"
                                :display-value="selectedModel ? selectedModel.label : '-- 请选择模型 --'" />
                        </div>
                    </div>

                    <!-- 部署/卸载按钮 -->
                    <div class="deploy-actions">
                        <q-btn class="deploy-btn roundBox" color="primary" @click="deployModel" :loading="isDeploying">
                            <img src="../../assets/images/btn_icon_bs.png" alt="部署" class="btn-icon">
                            <span style="font-size: .2rem;">部署模块</span>
                        </q-btn>
                        <q-btn class="undeploy-btn roundBox" color="secondary" @click="undeployModel"
                            :loading="isUndeploying">
                            <img src="../../assets/images/btn_icon_sc.png" alt="卸载" class="btn-icon">
                            <span style="font-size:.2rem;">卸载模块</span>
                        </q-btn>
                    </div>

                    <!-- 当前模型信息 -->
                    <div class="model-info">
                        <div class="info-title">当前模型信息</div>
                        <div class="info-item">
                            <span class="info-label">名称:</span>
                            <span class="info-value">{{ selectedModel ? selectedModel.label : '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">参数规模:</span>
                            <span class="info-value">{{ selectedModel ? selectedModel.params : '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">状态:</span>
                            <span class="info-value" :class="getStatusClass(currentStatus)">{{ getStatusText() }}</span>
                        </div>
                    </div>

                </div>
            </div>

            <div class="right">
                <div class="chat-section">
                    <!-- 对话区域 -->
                    <div class="chat-container">
                        <!-- 未部署状态 -->
                        <div v-if="!isDeployed" class="chat-placeholder">
                            <img src="../../assets/images/img_jqr.png" alt="机器人" class="robot-icon">
                            <div class="placeholder-text">请部署模型后开始对话</div>
                        </div>

                        <!-- 对话历史 -->
                        <div v-else class="chat-history" ref="chatHistoryRef">
                            <div v-for="(message, index) in chatHistory" :key="index" class="message-item"
                                :class="message.type">
                                <div class="message-avatar">
                                    <img v-if="message.type === 'bot'" src="../../assets/images/tx_a.png" alt="用户"
                                        class="avatar-icon">
                                    <img v-else class="user-avatar" src="../../assets/images/tx_b.png">
                                </div>
                                <div class="message-content">
                                    <div class="message-text">{{ message.content }}</div>
                                    <div class="message-time">{{ message.time }}</div>
                                </div>
                            </div>
                            <!-- 加载中提示 -->
                            <div v-if="isThinking" class="message-item bot thinking">
                                <div class="message-avatar">
                                    <img src="../../assets/images/tx_a.png" alt="机器人" class="avatar-icon">
                                </div>
                                <div class="message-content">
                                    <div class="message-text">
                                        <div class="thinking-dots">
                                            <span></span><span></span><span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 分割线 -->
                        <div class="chat-divider"></div>

                        <!-- 输入区域 -->
                        <div class="chat-input-area">
                            <!-- 快速提问 -->
                            <div v-if="isDeployed && chatHistory.length > 0" class="quick-questions">
                                <div class="quick-label">快速提问:</div>
                                <div class="question-buttons">
                                    <q-btn v-for="question in quickQuestions" :key="question" class="question-btn"
                                        outline @click="selectQuickQuestion(question)" :disable="isThinking">
                                        {{ question }}
                                    </q-btn>
                                </div>
                            </div>

                            <div class="input-container">
                                <q-input v-model="inputMessage" class="message-input" outlined dense
                                    placeholder="请输入您的问题" @keyup.enter="sendMessage"
                                    :disable="!isDeployed || isThinking" />
                                <q-btn class="send-btn" color="primary" @click="sendMessage"
                                    :disable="!isDeployed || !inputMessage.trim() || isThinking">
                                    <img src="../../assets/images/btn_icon_fs.png" alt="发送" class="send-icon">
                                </q-btn>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 提示弹窗 -->
        <q-dialog v-model="showTipDialog" persistent>
            <div class="tip-dialog">
                <div class="tip-header">提醒</div>
                <div class="tip-content">请选择模型后，在进行部署模型！</div>
                <div class="tip-actions">
                    <q-btn class="tip-confirm-btn" @click="closeTipDialog">确定</q-btn>
                </div>
            </div>
        </q-dialog>

        <!-- 卸载确认弹窗 -->
        <q-dialog v-model="showUndeployDialog" persistent>
            <div class="tip-dialog">
                <div class="tip-header">提醒</div>
                <div class="tip-content">确定要卸载{{ selectedModel ? selectedModel.label : '' }}吗？</div>
                <div class="tip-actions">
                    <q-btn class="tip-cancel-btn" @click="closeUndeployDialog">取消</q-btn>
                    <q-btn class="tip-confirm-btn" @click="confirmUndeploy">确定</q-btn>
                </div>
            </div>
        </q-dialog>

        <!-- 模型部署进度弹窗 -->
        <q-dialog v-model="showDeployProgressDialog" persistent>
            <div class="deploy-progress-dialog">
                <div class="deploy-header">
                    <div class="deploy-title">模型部署中</div>
                    <q-btn flat round dense icon="close" class="deploy-close-btn" @click="closeDeployProgressDialog" />
                </div>
                <div class="deploy-content">
                    <div class="deploy-message">请选择模型后,在进行部署模型!</div>
                    <div class="progress-section">
                        <el-progress :percentage="deployProgressPercentage" :stroke-width="12"
                            class="deploy-progress-bar" />
                        <div class="remaining-time">剩余时间:约{{ remainingTime }}秒</div>
                    </div>
                    <div class="deploy-warning">
                        <img class="warning-icon" src="../../assets/images/btn_icon_i.png" />
                        <span class="warning-text">部署过程中请不要关闭页面</span>
                    </div>
                </div>
            </div>
        </q-dialog>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, defineEmits, nextTick, computed } from 'vue'
import { usePlugin } from 'composables/plugin.js'
import { useLargeModelStore } from '../../stores/largeModelStore'

const emit = defineEmits(['next-step', 'prev-step'])
const { notify } = usePlugin()
const largeModelStore = useLargeModelStore()

const selectedModel = ref(null)
const modelOptions = ref([
    { label: 'Llama (7B)', value: 'llama-7b', params: '7B', status: 'ready', statusText: '就绪' },
    { label: 'ChatGLM (6B)', value: 'chatglm-6b', params: '6B', status: 'ready', statusText: '就绪' },
    { label: 'BLOOM (7.1B)', value: 'bloom-7.1b', params: '7.1B', status: 'ready', statusText: '就绪' },
    { label: 'GPT-Neo (2.7B)', value: 'gpt-neo-2.7b', params: '2.7B', status: 'ready', statusText: '就绪' }
])

const isDeploying = ref(false)
const isDeployed = ref(false)
const isUndeploying = ref(false)

const currentStatus = computed(() => {
    if (isDeploying.value) return 'deploying'
    if (isUndeploying.value) return 'undeploying'
    if (isDeployed.value) return 'deployed'
    if (selectedModel.value) return 'ready'
    return 'none'
})

const chatHistory = ref([])
const inputMessage = ref('')
const isThinking = ref(false)
const chatHistoryRef = ref(null)
const showTipDialog = ref(false)
const showUndeployDialog = ref(false)
const showDeployProgressDialog = ref(false)

const deployProgress = ref(0)
const remainingTime = ref(25)
const deployInterval = ref(null)

// 快速提问选项
const quickQuestions = ref([
    '解释量子计算机原理',
    '写一首关于春天的诗',
    'Python实现快速排序',
    '如何提高深度学习模型标准率'
])

// 计算进度百分比
const deployProgressPercentage = computed(() => {
    return Math.round(deployProgress.value * 100)
})

const getStatusClass = (status) => {
    switch (status) {
        case 'ready':
            return 'status-ready'
        case 'deploying':
            return 'status-deploying'
        case 'deployed':
            return 'status-deployed'
        case 'undeploying':
            return 'status-undeploying'
        case 'error':
            return 'status-error'
        default:
            return 'status-none'
    }
}

const getStatusText = () => {
    switch (currentStatus.value) {
        case 'ready':
            return '就绪'
        case 'deploying':
            return '部署中...'
        case 'deployed':
            return '已部署'
        case 'undeploying':
            return '卸载中...'
        case 'error':
            return '错误'
        default:
            return '未部署'
    }
}

const onModelChange = (model) => {
    console.log('选择模型:', model)
    if (isDeployed.value) {
        isDeployed.value = false
        chatHistory.value = []
        notify('模型已切换，请重新部署', 'info')
    }
}

const deployModel = async () => {
    if (!selectedModel.value) {
        showTipDialog.value = true
        return
    }

    if (isDeployed.value) {
        notify('模型已经部署！', 'info')
        return
    }

    // 显示部署进度弹窗
    showDeployProgressDialog.value = true
    deployProgress.value = 0
    remainingTime.value = 25

    // 开始部署进度
    startDeployProgress()
}

const startDeployProgress = () => {
    const totalDuration = 25000 // 25秒 = 25000毫秒
    const interval = 100 // 每100ms更新一次
    const progressIncrement = interval / totalDuration // 每次更新的进度增量

    deployInterval.value = setInterval(() => {
        if (deployProgress.value < 1) {
            deployProgress.value += progressIncrement
            // 确保进度不超过1
            if (deployProgress.value > 1) {
                deployProgress.value = 1
            }
            // 计算剩余时间，确保与进度同步
            remainingTime.value = Math.max(0, Math.ceil(25 * (1 - deployProgress.value)))
        } else {
            // 部署完成
            clearInterval(deployInterval.value)
            completeDeployment()
        }
    }, interval)
}

const completeDeployment = async () => {
    try {
        isDeploying.value = false
        isDeployed.value = true

        notify('模型部署成功！', 'positive')

        chatHistory.value.push({
            type: 'bot',
            content: `${selectedModel.value.label} 模型已成功部署完成，请问有什么可以帮助您的吗？`,
            time: getCurrentTime()
        })

        nextTick(() => {
            scrollToBottom()
        })

        // 关闭进度弹窗
        showDeployProgressDialog.value = false

    } catch (error) {
        isDeploying.value = false
        notify('模型部署失败！', 'negative')
        showDeployProgressDialog.value = false
    }
}

const closeDeployProgressDialog = () => {
    if (deployInterval.value) {
        clearInterval(deployInterval.value)
    }
    showDeployProgressDialog.value = false
    deployProgress.value = 0
    remainingTime.value = 25
}

const undeployModel = async () => {
    if (!isDeployed.value) {
        notify('当前没有已部署的模型！', 'warning')
        return
    }

    showUndeployDialog.value = true
}

const closeUndeployDialog = () => {
    showUndeployDialog.value = false
}

const confirmUndeploy = async () => {
    showUndeployDialog.value = false
    isUndeploying.value = true

    try {
        await new Promise(resolve => setTimeout(resolve, 2000))

        isUndeploying.value = false
        isDeployed.value = false
        chatHistory.value = []

        notify('模型卸载成功！', 'positive')

    } catch (error) {
        isUndeploying.value = false
        notify('模型卸载失败！', 'negative')
    }
}

const closeTipDialog = () => {
    showTipDialog.value = false
}

const sendMessage = async () => {
    if (!inputMessage.value.trim() || isThinking.value) return

    const userMessage = inputMessage.value.trim()
    inputMessage.value = ''

    chatHistory.value.push({
        type: 'user',
        content: userMessage,
        time: getCurrentTime()
    })

    nextTick(() => {
        scrollToBottom()
    })

    isThinking.value = true

    try {
        await new Promise(resolve => setTimeout(resolve, 2000))

        const botResponse = generateBotResponse(userMessage)

        chatHistory.value.push({
            type: 'bot',
            content: botResponse,
            time: getCurrentTime()
        })

        isThinking.value = false

        nextTick(() => {
            scrollToBottom()
        })

    } catch (error) {
        isThinking.value = false
        notify('消息发送失败！', 'negative')
    }
}

const generateBotResponse = (userMessage) => {
    const responses = [
        '这是一个很有趣的问题。根据我的理解，我认为...',
        '感谢您的提问。让我来为您详细解答一下...',
        '基于我的知识库，我可以为您提供以下信息...',
        '这个问题涉及多个方面，让我逐一为您分析...',
        '很高兴能够帮助您解决这个问题...'
    ]

    return responses[Math.floor(Math.random() * responses.length)]
}

const getCurrentTime = () => {
    const now = new Date()
    return now.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    })
}

const scrollToBottom = () => {
    if (chatHistoryRef.value) {
        chatHistoryRef.value.scrollTop = chatHistoryRef.value.scrollHeight
    }
}

const selectQuickQuestion = (question) => {
    if (isThinking.value) return
    inputMessage.value = question
    sendMessage()
}

const prevStep = () => {
    emit('prev-step')
}

const completeProcess = () => {
    largeModelStore.updateStepSixData({
        selectedModel: selectedModel.value,
        isDeployed: isDeployed.value,
        chatHistory: chatHistory.value
    })

    notify('模型部署推理流程完成！', 'positive')
    emit('next-step')
}

onMounted(() => {
    console.log('LargeStepSix mounted')
})

onUnmounted(() => {
    if (deployInterval.value) {
        clearInterval(deployInterval.value)
    }
})
</script>

<style lang="scss" scoped>
.content {
    display: flex;
    flex-direction: column;
    flex:1;
    .top {
        display: flex;
        margin-bottom: .125rem;
        flex:1;
        min-height: 9.5rem;

        .left {
            width: 20%;
            height: inherit;
            border: .025rem solid #707070;
            background-color: #181a24;
            padding: .25rem;
            display: flex;
            flex-direction: column;
            margin-right: .125rem;
            position: relative;

            &::before {
                position: absolute;
                content: '';
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-image:
                    repeating-linear-gradient(130deg,
                        rgba(255, 255, 255, 0.05) 0px,
                        rgba(255, 255, 255, 0.01) 4px,
                        transparent 1px,
                        transparent 15px);
                z-index: 10;
                pointer-events: none;
            }

            &::after {
                position: absolute;
                content: '';
                left: -0.1rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198);
                border-radius: .125rem;
                z-index: 3;
            }

            .left-decoration {
                position: absolute;
                left: -0.075rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198, .5);
                z-index: 3;
            }

            .model-management-section {
                position: relative;
                z-index: 11;
                height: 100%;
                display: flex;
                flex-direction: column;
            }
        }

        .right {
            width: 80%;
            height: inherit;
            border: .025rem solid #707070;
            background-color: #181a24;
            background-image:
                repeating-linear-gradient(130deg,
                    rgba(255, 255, 255, 0.05) 0px,
                    rgba(255, 255, 255, 0.01) 4px,
                    transparent 1px,
                    transparent 15px);
            padding: .3375rem;
            display: flex;
            flex-direction: column;
            position: relative;
            margin-left: .0625rem;

            &::before {
                position: absolute;
                content: '';
                left: -0.1rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198);
                border-radius: .125rem;
            }

            &::after {
                position: absolute;
                content: '';
                left: -0.075rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198, .5);
            }
        }
    }
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: .625rem;

    .arrow-icon {
        width: .2rem;
        height: .2rem;
        margin-right: .125rem;
    }

    .section-title {
        color: #fff;
        font-size: .2rem;
    }
}

.model-management-section {
    .model-selection {
        margin-bottom: .8rem;
        padding-left: .125rem;
        padding-right: .125rem;

        .selection-row {
            display: flex;
            align-items: center;
            gap: .125rem;

            .selection-label {
                color: white;
                font-size: .175rem;
                min-width: 1rem;
                flex-shrink: 0;
                text-align: right;
            }

            .model-select {
                flex: 1;

                :deep(.q-field__control) {
                    height: .45rem !important;
                    min-height: .45rem !important;
                }

                :deep(.q-field__native) {
                    color: white !important;
                    font-size: .15rem !important;
                }

                :deep(.q-field__label) {
                    color: #999 !important;
                    font-size: .15rem !important;
                }

                :deep(.q-field__control-container) {
                    .q-field__native {
                        color: white !important;
                    }
                }

                :deep(.q-field__marginal) {
                    color: #999 !important;
                }

                :deep(.q-field__marginal) {
                    height: 100% !important;
                }



            }
        }
    }



    .deploy-actions {
        margin-bottom: .8rem;
        display: flex;
        flex-direction: column;
        gap: .25rem;
        padding: 0 .125rem;

        .deploy-btn,
        .undeploy-btn {
            width: 100%;
            height: .6rem;
            font-size: .175rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: .0625rem;
            color: white !important;
            border-radius: .0625rem;

            .btn-icon {
                width: .3rem;
                height: .3rem;
                margin-right: .125rem;
            }
        }

        .deploy-btn {
            background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%) !important;

            &:hover:not(:disabled) {
                background: linear-gradient(135deg, #357abd 0%, #2a5a8a 100%) !important;
            }

            &:disabled {
                background: rgba(74, 144, 226, 0.5) !important;
                opacity: 0.6;
            }
        }

        .undeploy-btn {
            background: rgba(112, 112, 112, 0.8) !important;

            &:hover:not(:disabled) {
                background: rgba(112, 112, 112, 1) !important;
            }

            &:disabled {
                background: rgba(112, 112, 112, 0.3) !important;
                opacity: 0.6;
            }
        }
    }

    .model-info {
        background-color: rgba(112, 112, 112, .1);
        border-radius: .0625rem;
        padding: .1875rem;
        margin-top: auto;

        .info-title {
            color: #fff;
            font-size: .175rem;
            margin-bottom: .125rem;
            border-bottom: .0125rem solid #333;
            padding-bottom: .0625rem;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: .0625rem;
            font-size: .15rem;

            .info-label {
                color: #999;
            }

            .info-value {
                color: white;

                &.status-ready {
                    color: #4CAF50;
                }

                &.status-deploying {
                    color: #FF9800;
                }

                &.status-deployed {
                    color: #41bd73;
                }

                &.status-undeploying {
                    color: #FF5722;
                }

                &.status-error {
                    color: #F44336;
                }

                &.status-none {
                    color: #666;
                }
            }
        }
    }
}

.chat-section {
    height: 100%;
    display: flex;
    flex-direction: column;

    .chat-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        margin-bottom: .1875rem;
        border-radius: .25rem;
        background-color: rgba(37, 76, 111, .2);
    }

    .chat-placeholder {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        // justify-content: center;
        margin-top: 1.1875rem;
        color: #999;

        .robot-icon {
            width: .95rem;
            height: .625rem;
            margin-bottom: .25rem;
            opacity: 0.7;
        }

        .placeholder-text {
            font-size: .2rem;
            color: #9cacc6;
        }
    }

    .chat-history {
        height: 6.5rem;
        overflow-y: auto;
        padding: .375rem;
        border-radius: .0625rem;
        margin-bottom: .125rem;

        &::-webkit-scrollbar {
            width: .0625rem;
        }

        &::-webkit-scrollbar-track {
            background: rgb(0, 0, 0);
            border-radius: .03125rem;
        }

        &::-webkit-scrollbar-thumb {
            background: rgba(114, 114, 114, 0.6);
            border-radius: .03125rem;

            &:hover {
                background: rgba(74, 180, 255, 0.8);
            }
        }

        .message-item {
            display: flex;
            margin-bottom: .1875rem;
            align-items: center;

            &.user {
                flex-direction: row-reverse;

                .message-content {
                    background-color: #353c4c;
                    color: white;
                    margin-right: .125rem;
                }

                .message-avatar {
                    background: #353c4c !important;
                }
            }

            &.bot {
                .message-content {
                    background-color: #143558;
                    color: white;
                    margin-left: .125rem;
                }
            }

            .message-avatar {
                width: .825rem;
                height: .825rem;
                flex-shrink: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #163758;
                border-radius: .125rem;

                .avatar-icon {
                    width: .825rem;
                    height: .825rem;
                    border-radius: 50%;
                }

                .user-avatar {
                    width: .825rem;
                    height: .825rem;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: .15rem;
                    color: white;
                }
            }

            .user-avatarBox {
                background-color: #707070 !important;
            }

            .message-content {
                max-width: 70%;
                padding: .125rem .1875rem;
                border-radius: .125rem;
                position: relative;

                .message-text {
                    font-size: .15rem;
                    line-height: 1.4;
                }

                .message-time {
                    font-size: .125rem;
                    color: #999;
                    margin-top: .0625rem;
                    text-align: right;
                }
            }

            &.thinking .message-text {
                display: flex;
                align-items: center;
            }
        }
    }

    .chat-divider {
        height: .0125rem;
        background: rgba(141, 169, 195, .4);
        margin: .1875rem 0;
        border-radius: .0125rem;
    }

    .chat-input-area {
        padding: .1875rem;
        border-radius: .0625rem;
        margin: 0 .125rem .125rem .125rem;
        padding-top: 0 !important;

        .quick-questions {
            margin-bottom: .1875rem;
            display: flex;
            align-items: center;
            padding: 0 .25rem;

            .quick-label {
                color: #9cacc6;
                font-size: .175rem;
                margin-right: .25rem;
            }

            .question-buttons {
                display: flex;
                gap: .25rem;
                flex-wrap: wrap;

                .question-btn {
                    font-size: .15rem;
                    height: .5rem;
                    padding: 0 .1875rem;
                    border-radius: .5rem;
                    color: #fff !important;
                    border: .0125rem solid #fff !important;
                    background: #313541 !important;

                    &:hover:not(:disabled) {
                        color: #fff !important;
                        border-color: #4a90e2 !important;
                        background: rgba(74, 144, 226, 0.1) !important;
                    }

                    &:disabled {
                        opacity: 0.5;
                    }
                }
            }
        }

        .input-container {
            display: flex;
            gap: 0;

            .message-input {
                flex: 1;

                :deep(.q-field__control) {
                    height: .82rem !important;
                    min-height: .82rem !important;
                    background-color: #353c4c !important;
                    border: .0125rem solid rgba(255, 255, 255, 0.2) !important;
                    border-right: none !important;
                    border-radius: .0625rem 0 0 .0625rem !important;
                }

                :deep(.q-field__native) {
                    color: white !important;
                    font-size: .25rem !important;
                }

                :deep(.q-field__label) {
                    color: #999 !important;
                    font-size: .15rem !important;
                }
            }

            .send-btn {
                width: .825rem;
                height: .825rem;
                min-width: .825rem !important;
                padding: 0 !important;
                background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%) !important;
                border-radius: 0 .0625rem .0625rem 0 !important;
                position: relative;
                left: -.05rem;

                .send-icon {
                    width: .375rem;
                    height: .375rem;
                }

                &:hover:not(:disabled) {
                    background: linear-gradient(135deg, #357abd 0%, #2a5a8a 100%) !important;
                }

                &:disabled {
                    background: rgba(74, 144, 226, 1) !important;
                }
            }

            .disabled {
                opacity: unset !important;
            }

            .q-btn.disabled {
                opacity: unset !important;
            }
        }
    }

    .bottom {
        display: flex;
        align-items: center;
        flex-shrink: 0;
        margin-top: .1875rem;

        .next {
            display: flex;
            justify-content: space-between;
            width: 100%;
            padding: 0 .25rem;
            gap: .25rem;

            .prevBtn {
                margin-right: auto;
            }

            .nextBtn {
                margin-left: auto;
            }
        }
    }
}

.thinking-dots {
    display: flex;
    gap: .0625rem;

    span {
        width: .0625rem;
        height: .0625rem;
        background-color: #4ab4ff;
        border-radius: 50%;
        animation: thinking 1.4s infinite ease-in-out both;

        &:nth-child(1) {
            animation-delay: -0.32s;
        }

        &:nth-child(2) {
            animation-delay: -0.16s;
        }
    }
}

@keyframes thinking {

    0%,
    80%,
    100% {
        transform: scale(0);
    }

    40% {
        transform: scale(1);
    }
}

.tip-dialog {
    background: #102947;
    border: .05rem solid rgba(1, 74, 173, .8);
    border-radius: .125rem;
    padding: .25rem;
    min-width: 6rem;
    box-shadow: 0 .125rem .5rem rgba(0, 0, 0, 0.3);

    .tip-header {
        color: #fff;
        font-size: .25rem;
        font-weight: bold;
        margin-bottom: .175rem;
        text-align: left;
    }

    .tip-content {
        color: #fff;
        font-size: .2rem;
        line-height: 1.5;
        margin-bottom: .3rem;
        text-align: left;
    }

    .tip-actions {
        display: flex;
        justify-content: flex-end;

        .tip-confirm-btn {
            background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%) !important;
            color: white !important;
            border-radius: .0625rem;
            font-size: .175rem;
            min-width: 1.25rem;
            height: .5rem;

            &:hover {
                background: linear-gradient(135deg, #357abd 0%, #2a5a8a 100%) !important;
            }
        }

        .tip-cancel-btn {
            background: #273f5b !important;
            color: white !important;
            border-radius: .0625rem;
            font-size: .175rem;
            min-width: 1.25rem;
            height: .5rem;
            margin-left: .125rem;

            &:hover {
                background: #1e2f42 !important;
            }
        }
    }
}

.deploy-progress-dialog {
    background: #102947;
    border: .05rem solid rgba(1, 74, 173, .8);
    border-radius: .125rem;
    padding: .25rem;
    min-width: 8rem;
    box-shadow: 0 .125rem .5rem rgba(0, 0, 0, 0.3);

    .deploy-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: .25rem;

        .deploy-title {
            color: #fff;
            font-size: .25rem;
            font-weight: bold;
        }

        .deploy-close-btn {
            color: #fff !important;
            font-size: .2rem;

            &:hover {
                background: rgba(255, 255, 255, 0.1) !important;
            }
        }
    }

    .deploy-content {
        .deploy-message {
            color: #fff;
            font-size: .2rem;
            line-height: 1.5;
            margin-bottom: .25rem;
            text-align: left;
        }

        .progress-section {
            margin-bottom: .25rem;

            .deploy-progress-bar {
                margin-bottom: .125rem;

                :deep(.el-progress-bar__outer) {
                    background-color: rgba(255, 255, 255, 0.1) !important;
                    border-radius: .0625rem !important;
                }

                :deep(.el-progress-bar__inner) {
                    border-radius: .0625rem !important;
                    background: linear-gradient(90deg, #4ab4ff 0%, #4a90e2 100%) !important;
                    transition: width 0.3s ease !important;
                }

                :deep(.el-progress__text) {
                    color: #fff !important;
                    font-size: .15rem !important;
                }
            }

            .remaining-time {
                color: #4ab4ff;
                font-size: .175rem;
                text-align: right;
            }
        }

        .deploy-warning {
            display: flex;
            align-items: center;
            gap: .125rem;

            .warning-icon {
                width: .3375rem;
                height: .3375rem;
            }

            .warning-text {
                color: #ff9800;
                font-size: .175rem;
            }
        }
    }
}
</style>

<style lang="scss">

</style>