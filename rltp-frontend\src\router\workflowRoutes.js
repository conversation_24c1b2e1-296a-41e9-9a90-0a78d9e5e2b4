/**
 * 统一训练工作流路由配置
 */

const workflowRoutes = [
  {
    path: '/training-workflow',
    component: () => import('src/pages/TrainingWorkflowMain.vue'),
    children: [
      // 深度学习路由
      {
        path: 'deep-learning',
        children: [
          {
            path: 'step1',
            name: 'DeepLearningStep1',
            component: () => import('src/pages/ai-model/StepOne.vue'),
            meta: {
              title: '数据加载分割',
              trainingType: 'deep_learning',
              stepNumber: 1,
              needSave: true
            }
          },
          {
            path: 'step2',
            name: 'DeepLearningStep2',
            component: () => import('src/pages/ai-model/StepTwoExample.vue'),
            meta: {
              title: '训练参数配备',
              trainingType: 'deep_learning',
              stepNumber: 2,
              needSave: true
            }
          },
          {
            path: 'step3',
            name: 'DeepLearningStep3',
            component: () => import('src/pages/ai-model/StepThreeExample.vue'),
            meta: {
              title: '提交模型训练',
              trainingType: 'deep_learning',
              stepNumber: 3,
              needSave: true
            }
          },
          {
            path: 'step4',
            name: 'DeepLearningStep4',
            component: () => import('src/pages/ai-model/StepFour.vue'),
            meta: {
              title: '训练结果评估',
              trainingType: 'deep_learning',
              stepNumber: 4,
              needSave: false
            }
          },
          {
            path: 'step5',
            name: 'DeepLearningStep5',
            component: () => import('src/pages/ai-model/StepFive.vue'),
            meta: {
              title: '模型导出存储',
              trainingType: 'deep_learning',
              stepNumber: 5,
              needSave: false
            }
          }
        ]
      },

      // 强化学习路由
      {
        path: 'reinforcement-learning',
        children: [
          {
            path: 'step1',
            name: 'ReinforcementLearningStep1',
            component: () => import('src/pages/reinforcementLearing/StepOne.vue'),
            meta: {
              title: '交互环境接入',
              trainingType: 'reinforcement_learning',
              stepNumber: 1,
              needSave: true
            }
          },
          {
            path: 'step2',
            name: 'ReinforcementLearningStep2',
            component: () => import('src/pages/reinforcementLearing/StepTwo.vue'),
            meta: {
              title: '训练参数配置',
              trainingType: 'reinforcement_learning',
              stepNumber: 2,
              needSave: true
            }
          },
          {
            path: 'step3',
            name: 'ReinforcementLearningStep3',
            component: () => import('src/pages/reinforcementLearing/StepThree.vue'),
            meta: {
              title: '提交模型训练',
              trainingType: 'reinforcement_learning',
              stepNumber: 3,
              needSave: true
            }
          },
          {
            path: 'step4',
            name: 'ReinforcementLearningStep4',
            component: () => import('src/pages/reinforcementLearing/StepFour.vue'),
            meta: {
              title: '训练结果评估',
              trainingType: 'reinforcement_learning',
              stepNumber: 4,
              needSave: false
            }
          },
          {
            path: 'step5',
            name: 'ReinforcementLearningStep5',
            component: () => import('src/pages/reinforcementLearing/StepFive.vue'),
            meta: {
              title: '模型导出存储',
              trainingType: 'reinforcement_learning',
              stepNumber: 5,
              needSave: false
            }
          }
        ]
      },

      // 大模型路由
      {
        path: 'large-model',
        children: [
          {
            path: 'step1',
            name: 'LargeModelStep1',
            component: () => import('src/pages/large-model/StepOne.vue'),
            meta: {
              title: '数据加载分割',
              trainingType: 'large_model',
              stepNumber: 1,
              needSave: true
            }
          },
          {
            path: 'step2',
            name: 'LargeModelStep2',
            component: () => import('src/pages/large-model/StepTwo.vue'),
            meta: {
              title: '训练参数配备',
              trainingType: 'large_model',
              stepNumber: 2,
              needSave: true
            }
          },
          {
            path: 'step3',
            name: 'LargeModelStep3',
            component: () => import('src/pages/large-model/StepThree.vue'),
            meta: {
              title: '提交微调训练',
              trainingType: 'large_model',
              stepNumber: 3,
              needSave: true
            }
          },
          {
            path: 'step4',
            name: 'LargeModelStep4',
            component: () => import('src/pages/large-model/StepFour.vue'),
            meta: {
              title: '训练结果评估',
              trainingType: 'large_model',
              stepNumber: 4,
              needSave: false
            }
          },
          {
            path: 'step5',
            name: 'LargeModelStep5',
            component: () => import('src/pages/large-model/StepFive.vue'),
            meta: {
              title: '模型量化剪枝',
              trainingType: 'large_model',
              stepNumber: 5,
              needSave: false
            }
          },
          {
            path: 'step6',
            name: 'LargeModelStep6',
            component: () => import('src/pages/large-model/StepSix.vue'),
            meta: {
              title: '模型部署推理',
              trainingType: 'large_model',
              stepNumber: 6,
              needSave: false
            }
          }
        ]
      }
    ]
  }
]

export default workflowRoutes
