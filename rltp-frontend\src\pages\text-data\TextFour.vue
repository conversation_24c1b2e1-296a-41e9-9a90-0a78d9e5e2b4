<!--
 * @Author: Szc
 * @Date: 2025-08-20 14:32:02
 * @LastEditors: Szc
 * @LastEditTime: 2025-08-20 14:32:06
 * @Description: 👉数据增强
-->
<template>
  <div class="content">
    <div class="top">
      <div class="left">
        <div class="onetop">
          <img src="../../assets/images/icon_dz.png" />
          <h2>数据列表</h2>
        </div>
        <div class="result-tabs">
          <q-btn
            class="tab-btn"
            :class="{ active: resultType === 'recognition' }"
            @click="resultType = 'recognition'"
            no-caps
            flat
          >
            数据源（5）
          </q-btn>
          <q-btn
            class="tab-btn"
            :class="{ active: resultType === 'json' }"
            @click="resultType = 'json'"
            no-caps
            flat
          >
            增强数据 (3)
          </q-btn>
        </div>
        <div style="overflow-y: auto">
          <div class="table-header">
            <div class="header-cell">ID</div>
            <div class="header-cell">问题</div>
            <div class="header-cell">答案</div>
            <div class="header-cell">类别</div>
          </div>

          <div class="info-table">
            <div v-for="(row, index) in tableData" :key="index" class="table-row">
              <!-- 普通行 -->
              <template>
                <div class="cell">{{ row.label }}</div>
                <div class="cell blue-text">
                  {{ row.extra || "" }}
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>

      <div class="right">
        <div class="info-table">
          <div class="onetop">
            <img src="../../assets/images/icon_dz.png" />
            <h2>数据增强配置</h2>
          </div>
          <div class="twotiele"></div>
          <div class="bottom">
            <div class="next">
              <q-btn
                class="prevBtn roundBox"
                color="grey-7"
                label="上一步"
                @click="prevStep"
              />
              <q-btn
                class="nextBtn roundBox"
                color="primary"
                label="下一步"
                @click="nextStep"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
const emit = defineEmits(["next-step", "prev-step"]);
const saveResult = () => {
  console.log("保存结果");
};

const nextStep = () => {
  emit("next-step");
};
const resultType = ref("recognition");
</script>

<style scoped lang="scss">
.content {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%; // 确保根容器有高度

  .top {
    display: flex;
    margin-bottom: 0.125rem;
    min-height: 9.5rem;
    flex: 1;
    height: 100%; // 确保 top 容器有高度

    .left {
      width: 80%;
      height: inherit;
      border: 0.025rem solid #707070;
      background-color: #181a24;
      padding: 0.125rem;
      display: flex;
      flex-direction: column;
      margin-right: 0.125rem;
      background-color: #181a24;
      background-image: repeating-linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.05) 0,
        rgba(255, 255, 255, 0.01) 0.05rem,
        transparent 0.0125rem,
        transparent 0.1875rem
      );
      .result-tabs {
        display: flex;
        margin: 0.15rem 0.4rem;

        .tab-btn {
          width: 1.875rem;
          height: 0.625rem;
          color: #999;
          font-size: 0.225rem;
          background: #2a2d3a;
          border-radius: 0;
          &.active {
            color: #fff;
            background: url("../../assets/images/bq_bg.png") no-repeat center center;
            background-size: cover;
          }

          &:hover:not(.active) {
            color: #fff;
          }
        }
      }
    }
  }

  .right {
    width: 20%;
    height: inherit;
    border: 0.025rem solid #707070;
    background-color: #181a24;
    padding: 0.0625rem 0.3375rem;
    display: flex;
    flex-direction: column;
    background-color: #181a24;
    background-image: repeating-linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.05) 0,
      rgba(255, 255, 255, 0.01) 0.05rem,
      transparent 0.0125rem,
      transparent 0.1875rem
    );
    .info-table {
      height: 100%;

      .twotiele {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 0.125rem;
        height: 85%;
      }
      // 底部按钮
      .bottom {
        float: right;
        width: 2.25rem;
        height: 10%;
        display: flex;
        align-items: center;
        flex-shrink: 0;
        margin-top: 0.25rem;

        .next {
          display: flex;
          justify-content: space-between;
          width: 100%;
          padding: 0 0.25rem;
          gap: 0.25rem;

          .prevBtn {
            margin-right: auto;
          }

          .nextBtn {
            margin-left: auto;
          }
        }
      }
    }
  }
  .right {
    position: relative;
    &::before {
      position: absolute;
      z-index: 10000;
      content: "";
      left: -0.1rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198);
      border-radius: 0.125rem;
    }

    &::after {
      position: absolute;
      content: "";
      left: -0.075rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198, 0.5);
    }
  }

  .left {
    margin-right: 0.125rem;
    position: relative;
    &::before {
      position: absolute;
      z-index: 10000;
      content: "";
      left: -0.1rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198);
      border-radius: 0.125rem;
    }

    &::after {
      position: absolute;
      content: "";
      left: -0.075rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198, 0.5);
    }
  }
  .onetop {
    display: flex;
    align-items: center;
    height: 5%;
    h2 {
      height: 0.1875rem;
      line-height: 0.1875rem;
      color: #ffffff;
      font-size: 0.275rem;
    }
    img {
      width: 0.2rem;
      height: 0.2rem;
      margin-right: 0.1875rem;
    }
  }
}
</style>
