<!--
 * @Author: Szc
 * @Date: 2025-08-20 14:32:02
 * @LastEditors: Szc
 * @LastEditTime: 2025-08-20 14:32:06
 * @Description: 👉数据增强
-->
<template>
  <div class="content">
    <div class="top">
      <div class="left">
        <div class="onetop">
          <img src="../../assets/images/icon_dz.png" />
          <h2>数据列表</h2>
        </div>
        <div class="result-tabs">
          <q-btn class="tab-btn" :class="{ active: resultType === 'recognition' }" @click="resultType = 'recognition'"
            no-caps flat>
            数据源（5）
          </q-btn>
          <q-btn class="tab-btn" :class="{ active: resultType === 'json' }" @click="resultType = 'json'" no-caps flat>
            增强数据 (3)
          </q-btn>
        </div>
        <div class="tables">
          <div class="info-table">
            <template v-if="resultType === 'recognition'">
              <div class="table-header">
                <div class="header-cell">ID</div>
                <div class="header-cell">问题</div>
                <div class="header-cell">答案</div>
                <div class="header-cell">类别</div>
              </div>
              <div v-for="(row, index) in tableData" :key="index" class="table-row">
                <div class="cell">{{ row.id }}</div>
                <div class="cell">
                  <q-input v-if="editingIndex === index" v-model="row.question" borderless dense type="textarea"
                    class="cell-input" />
                  <span v-else>{{ row.question }}</span>
                </div>
                <div class="cell">
                  <q-input v-if="editingIndex === index" v-model="row.answer" borderless dense type="textarea"
                    class="cell-input" />
                  <span v-else>{{ row.answer }}</span>
                </div>
                <div class="cell">
                  <img v-if="editingIndex === index" src="../../assets/reinforcementImages/icon_save.png" alt="保存"
                    class="action-icon" @click="saveRow(index)" />
                  <img v-if="editingIndex === index" src="../../assets/images/close.png" alt="取消" class="action-icon"
                    @click="cancelEdit()" />
                  <template v-else>
                    <img src="../../assets/images/edit.png" alt="编辑" class="action-icon" @click="editRow(index)" />
                    <img src="../../assets/images/btn_icon_sc.png" alt="删除" class="action-icon"
                      @click="deleteRow(index)" />
                  </template>
                </div>
              </div>
            </template>
            <template v-else>
              <div class="header-two-cell">
                <div class="header-cell">ID</div>
                <div class="header-cell">问题</div>
                <div class="header-cell">答案</div>
              </div>
              <div v-if="!hasEnhancedData" class="no-data">
                <div class="no-data-text">
                  <span>暂无增强数据</span>
                  <span>请配置增强方式并点击“应用增强”</span>
                </div>
              </div>
              <div v-else>
                <div v-for="(row, index) in enhancedData" :key="index" class=" table-two-row">
                  <div class="cell">
                    <span>{{ row.id }}</span>
                    <span class="enhanced-tag">（增强）</span>
                  </div>
                  <div class="cell">
                    <q-input v-if="editingIndex === index" v-model="row.question" borderless dense type="textarea"
                      class="cell-input" />
                    <span v-else>{{ row.question }}</span>
                  </div>
                  <div class="cell">
                    <q-input v-if="editingIndex === index" v-model="row.answer" borderless dense type="textarea"
                      class="cell-input" />
                    <span v-else>{{ row.answer }}</span>
                  </div>
                  <!--   <div class="cell">
                    <img v-if="editingIndex === index" src="../../assets/reinforcementImages/icon_save.png" alt="保存"
                      class="action-icon" @click="saveRow(index)" />
                    <img v-if="editingIndex === index" src="../../assets/images/close.png" alt="取消" class="action-icon"
                      @click="cancelEdit()" />
                    <template v-else>
                      <img src="../../assets/images/edit.png" alt="编辑" class="action-icon" @click="editRow(index)" />
                      <img src="../../assets/images/btn_icon_sc.png" alt="删除" class="action-icon"
                        @click="deleteRow(index)" />
                    </template>
                  </div> -->
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>

      <div class="right">
        <div class="onetop">
          <img src="../../assets/images/icon_dz.png" />
          <h2>数据增强配置</h2>
        </div>
        <div class="twotiele">
          <div v-for="(config, index) in configList" :key="index" class="config-item">
            <div class="config-header">{{ config.title }}</div>
            <div class="config-content">
              <div class="config-label">{{ config.label }}</div>
              <q-input v-model="config.inputValue" class="config-input" outlined dense />
            </div>
            <el-slider v-model="config.sliderValue" :min="0" :max="1" :step="0.01" :format-tooltip="formatTooltip"
              show-tooltip @input="updateDisplay(index)" />
          </div>
          <div v-if="isApplyingEnhancement" class="enhancement-status">
            <div class="status-header">
              <div v-if="!enhancementCompleted" class="status-icon loading-spinner"></div>
              <img v-else src="../../assets/images/icon_cg.png" alt="成功" class="status-icon" />
              <span class="status-text">{{
                enhancementCompleted ? "应用增强完成" : "应用增强中"
              }}</span>
            </div>
            <div class="status-progress">
              <el-progress :percentage="enhancementProgress" :stroke-width="6" :show-text="true" />
            </div>
          </div>
          <div class="config-actions">
            <q-btn class="apply-btn roundBox" color="primary" label="应用增强" @click="applyEnhancement">
              <img src="../../assets/images/btn_icon_ksyh.png" class="btn-icon" />
            </q-btn>
            <q-btn class="reset-btn roundBox" label="重置配置" @click="showrestDialog = true">
              <img src="../../assets/images/refresh.png" class="btn-icon" />
            </q-btn>
          </div>
        </div>
        <div class="bottom">
          <div class="next">
            <q-btn class="nextBtn roundBox" color="primary" label="生成数据集" @click="nextStep" />
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 重置弹窗 -->
  <q-dialog v-model="showrestDialog" persistent>
    <div class="tip-dialog">
      <div class="tip-header">提醒</div>
      <div class="tip-content">确定要重置配置吗？</div>
      <div class="tip-actions">
        <q-btn class="tip-close-btn" @click="showrestDialog = false">取消</q-btn>
        <q-btn class="tip-confirm-btn" @click="ensureresetDialog">确定</q-btn>
      </div>
    </div>
  </q-dialog>
</template>

<script setup>
import { ref, computed } from "vue";
const emit = defineEmits(["next-step", "prev-step"]);
const nextStep = () => {
  emit("next-step");
};

const prevStep = () => {
  emit("prev-step");
};
const tableData = ref([
  {
    id: 1,
    question: "如何处置路由器密码？",
    answer: "通常可以通过路由器背面的重置按钮或访问管理界面进行密码重置",
  },
  {
    id: 2,
    question: "Python中如何读取CSX文件？",
    answer: "可以使用pandas库的read_csv模块来读取csv文件",
  },
  {
    id: 3,
    question: "如何申请退税？",
    answer: "可以通过税务APP或前往当地税务局办理退税手续，需要准备以下...",
  },
  {
    id: 4,
    question: "如何煮出完美的水煮蛋？",
    answer: "将鸡蛋放入冷水中，水开后煮6-7分钟可获得溏心蛋，9-10分...",
  },
]);

const resultType = ref("recognition");
const enhancedData = ref([]);
const hasEnhancedData = computed(() => enhancedData.value.length > 0);

const addRow = () => {
  const newId =
    tableData.value.length > 0
      ? Math.max(...tableData.value.map((item) => item.id)) + 1
      : 1;
  tableData.value.push({
    id: newId,
    question: "新问题",
    answer: "新答案",
  });
};

const editingIndex = ref(-1);

const editRow = (index) => {
  editingIndex.value = editingIndex.value === index ? -1 : index;
};

const deleteRow = (index) => {
  tableData.value.splice(index, 1);
};

// 添加 cancelEdit 方法
const cancelEdit = () => {
  editingIndex.value = -1;
};

// 确保 saveRow 方法存在，如果不存在需要添加
const saveRow = (index) => {
  // 这里可以添加保存逻辑
  editingIndex.value = -1;
};

const configList = ref([
  {
    title: "掩码遮盖:",
    label: "遮盖比例",
    inputValue: "28%",
    sliderValue: 0.28,
  },
  {
    title: "数据扰动:",
    label: "扰动比例",
    inputValue: "10%",
    sliderValue: 0.1,
  },
  {
    title: "同义词替换:",
    label: "替换比例",
    inputValue: "20%",
    sliderValue: 0.2,
  },
  {
    title: "随机插入:",
    label: "插入比例",
    inputValue: "15%",
    sliderValue: 0.15,
  },
]);

function formatTooltip(value) {
  return `${(value * 100).toFixed(1)}%`;
}

function updateDisplay(index) {
  configList.value[index].inputValue = `${(
    configList.value[index].sliderValue * 100
  ).toFixed(1)}%`;
}

const isApplyingEnhancement = ref(false);
const enhancementProgress = ref(0);
const enhancementCompleted = ref(false);

async function startEnhancement() {
  isApplyingEnhancement.value = true;
  enhancementCompleted.value = false;
  enhancementProgress.value = 0;

  try {
    for (let i = 0; i <= 100; i += 5) {
      await new Promise((resolve) => setTimeout(resolve, 200));
      enhancementProgress.value = i;
    }
    enhancementCompleted.value = true;
    // 可添加应用增强成功后的逻辑
  } catch (error) {
    // 可添加错误处理逻辑
  }
}

function resetConfig() {
  configList.value.forEach((config) => {
    config.sliderValue = 0.3;
    config.inputValue = "30.0%";
  });
}

// 添加应用增强方法
const applyEnhancement = async () => {
  isApplyingEnhancement.value = true;
  enhancementCompleted.value = false;
  enhancementProgress.value = 0;

  try {
    // 这里添加实际的增强逻辑，当前用模拟进度代替
    for (let i = 0; i <= 100; i += 5) {
      await new Promise((resolve) => setTimeout(resolve, 200));
      enhancementProgress.value = i;
    }
    enhancementCompleted.value = true;
    // 模拟增强数据
    enhancedData.value = tableData.value.map(item => ({
      ...item,
      id: `${item.id}`
    }));
  } catch (error) {
    console.error("应用增强失败:", error);
    // 可添加错误处理逻辑
  }
};
const showrestDialog = ref(false);
const ensureresetDialog = () => {
  isApplyingEnhancement.value = false;
  showrestDialog.value = false;
  // 清空增强数据
  enhancedData.value = [];
  resetConfig();
};
</script>

<style scoped lang="scss">
.content {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;

  .top {
    display: flex;
    margin-bottom: 0.125rem;
    min-height: 9.5rem;
    flex: 1;
    height: 100%;

    .left {
      width: 80%;
      height: inherit;
      border: 0.025rem solid #707070;
      background-color: #181a24;
      padding: 0.25rem;
      display: flex;
      flex-direction: column;
      margin-right: 0.125rem;
      background-color: #181a24;
      background-image: repeating-linear-gradient(135deg,
          rgba(255, 255, 255, 0.05) 0,
          rgba(255, 255, 255, 0.01) 0.05rem,
          transparent 0.0125rem,
          transparent 0.1875rem);

      .onetop {
        height: 5%;
      }

      .result-tabs {
        display: flex;
        margin: 0.15rem 0.4rem;
        height: 5%;

        .tab-btn {
          width: 1.875rem;
          height: 0.625rem;
          color: #999;
          font-size: 0.225rem;
          background: #2a2d3a;
          border-radius: 0;

          &.active {
            color: #fff;
            background: url("../../assets/images/bq_bg.png") no-repeat center center;
            background-size: cover;
          }

          &:hover:not(.active) {
            color: #fff;
          }
        }
      }

      .tables {
        height: 90%;
        margin: 0.15rem 0.4rem;

        .header-two-cell {
          display: flex;
          font-weight: bold;
          background: linear-gradient(to right, #216f6e 30%, #003366 70%);
          align-items: center;
          justify-content: center;
          height: 0.8rem;

          .header-cell {
            color: rgba(99, 212, 255, 1);
            height: 100%;
            line-height: 0.8rem;
            display: flex;
            align-items: center;

            &:nth-child(1){
              width: 15%;
            }

            &:nth-child(2) {
              width: 35%;
            }

            &:nth-child(3) {
              width: 50%;
            }

            padding: 0.125rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

          }


        }

        .table-header {
          display: flex;
          font-weight: bold;
          background: linear-gradient(to right, #216f6e 30%, #003366 70%);
          align-items: center;
          justify-content: center;
          height: 0.8rem;

          .header-cell {
            color: rgba(99, 212, 255, 1);
            height: 100%;
            line-height: 0.8rem;
            display: flex;
            align-items: center;

            &:nth-child(1),
            &:nth-child(4) {
              width: 15%;
            }

            &:nth-child(2) {
              width: 30%;
            }

            &:nth-child(3) {
              width: 40%;
            }

            padding: 0.125rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

          }


        }

        .info-table {
          height: 95%;
          overflow-y: auto;

          .table-row {
            display: flex;
            color: white;
            height: 0.8rem;
            line-height: 0.8rem;
            align-items: center;

            .cell {

              &:nth-child(1),
              &:nth-child(4) {
                width: 15%;
              }

              &:nth-child(2) {
                width: 30%;
              }

              &:nth-child(3) {
                width: 40%;
              }

              padding: 0.125rem;
              height: 100%;
              display: flex;
              align-items: center;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;

            }

            .action-icon {
              width: 0.2rem;
              height: 0.2rem;
              margin-right: 0.1rem;
              cursor: pointer;

            }
          }

          .table-two-row {
            display: flex;
            color: white;
            height: 0.8rem;
            line-height: 0.8rem;
            align-items: center;

            .cell {

              &:nth-child(1) {
                width: 15%;
              }

              &:nth-child(2) {
                width: 35%;
              }

              &:nth-child(3) {
                width: 50%;
              }

              padding: 0.125rem;
              height: 100%;
              display: flex;
              align-items: center;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;

            }

            .action-icon {
              width: 0.2rem;
              height: 0.2rem;
              margin-right: 0.1rem;
              cursor: pointer;

            }
          }
        }
      }
    }
  }

  .right {
    width: 20%;
    height: inherit;
    border: 0.025rem solid #707070;
    background-color: #181a24;
    padding: 0.25rem;
    display: flex;
    flex-direction: column;
    background-color: #181a24;
    background-image: repeating-linear-gradient(135deg,
        rgba(255, 255, 255, 0.05) 0,
        rgba(255, 255, 255, 0.01) 0.05rem,
        transparent 0.0125rem,
        transparent 0.1875rem);

    .onetop {
      height: 5%;
    }

    .twotiele {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      height: 90%;
      overflow-y: auto;

      .config-item {
        width: 100%;
        margin-top: 0.125rem;
        display: flex;
        flex-direction: column;

        .config-header {
          color: white;
        }

        .config-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .config-label {
            color: rgba(99, 212, 255, 1);
          }

          .config-input {
            width: 1.25rem;
            height: 0.45rem;
          }
        }

        .el-slider {
          width: 100%;
        }
      }

      .config-actions {
        display: flex;
        justify-content: space-between;
        width: 100%;

        .apply-btn {
          color: #ffffff;
        }

        .reset-btn {
          background: rgba(255, 255, 255, 0.1);
          color: #ffffff;
        }

        .apply-btn,
        .reset-btn {
          width: 49%;
          height: 0.6rem;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: row;
        }

        .btn-icon {
          position: absolute;
          margin-left: 0.2rem;
          left: 0;
        }
      }

      .enhancement-status {
        width: 100%;
        margin-bottom: 0.25rem;
        padding: 0.1875rem;
        background-color: #1a293e;
        border-radius: 0.0625rem;

        .status-header {
          display: flex;
          align-items: center;
          margin-bottom: 0.125rem;
        }

        .status-icon {
          width: 0.2rem;
          height: 0.2rem;
          margin-right: 0.1rem;
        }

        .loading-spinner {
          border: 0.025rem solid rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          border-top: 0.025rem solid #fff;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }

          100% {
            transform: rotate(360deg);
          }
        }

        .status-text {
          color: white;
        }

        .status-progress {
          width: 100%;
        }
      }
    }

    // 底部按钮
    .bottom {
      height: 5%;
      display: flex;
      align-items: center;
      flex-shrink: 0;
      margin-top: 0.25rem;

      .next {
        display: flex;
        justify-content: space-between;
        width: 100%;
        padding: 0 0.25rem;
        gap: 0.25rem;

        .prevBtn {
          margin-right: auto;
        }

        .nextBtn {
          margin-left: auto;
        }
      }
    }
  }

  .right {
    position: relative;

    &::before {
      position: absolute;
      z-index: 10000;
      content: "";
      left: -0.1rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198);
      border-radius: 0.125rem;
    }

    &::after {
      position: absolute;
      content: "";
      left: -0.075rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198, 0.5);
    }
  }

  .left {
    margin-right: 0.125rem;
    position: relative;

    &::before {
      position: absolute;
      z-index: 10000;
      content: "";
      left: -0.1rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198);
      border-radius: 0.125rem;
    }

    &::after {
      position: absolute;
      content: "";
      left: -0.075rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198, 1);
      display: flex;
      flex-direction: column;
    }
  }

  .onetop {
    display: flex;
    align-items: center;
    height: 5%;

    h2 {
      height: 0.1875rem;
      line-height: 0.1875rem;
      color: #ffffff;
      font-size: 0.225rem;
    }

    img {
      width: 0.2rem;
      height: 0.2rem;
      margin-right: 0.1875rem;
    }
  }
}

.tip-dialog {
  background: #102947;
  border: 0.05rem solid rgba(1, 74, 173, 0.8);
  border-radius: 0.125rem;
  padding: 0.25rem;
  min-width: 6rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.3);

  .tip-header {
    color: #fff;
    font-size: 0.25rem;
    font-weight: bold;
    margin-bottom: 0.175rem;
    text-align: left;
  }

  .tip-content {
    color: #fff;
    font-size: 0.2rem;
    line-height: 1.5;
    margin-bottom: 0.3rem;
    text-align: left;
  }

  .tip-actions {
    display: flex;
    justify-content: flex-end;

    .tip-close-btn {
      margin-right: .125rem;
      background: rgba(255, 255, 255, 0.1) !important;
      color: white !important;
      border-radius: 0.0625rem;
      font-size: 0.175rem;
      min-width: 1.25rem;
      height: 0.5rem;


    }

    .tip-confirm-btn {
      background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%) !important;
      color: white !important;
      border-radius: 0.0625rem;
      font-size: 0.175rem;
      min-width: 1.25rem;
      height: 0.5rem;


    }

    .tip-cancel-btn {
      background: #273f5b !important;
      color: white !important;
      border-radius: 0.0625rem;
      font-size: 0.175rem;
      min-width: 1.25rem;
      height: 0.5rem;
      margin-left: 0.125rem;


    }
  }
}

.cell .cell-input {
  padding: 0.125rem;
  height: 0.8rem;
  width: 100%;
  color: white;

  :deep(.q-field__control-container) {
    height: 0.55rem;
  }
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 85%;
  color: white;

  .no-data-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: rgba(156, 172, 198, 1);
    font-size: 0.2rem;
  }
}

.enhanced-tag {
  color: rgba(74, 180, 255, 1);
}
</style>


