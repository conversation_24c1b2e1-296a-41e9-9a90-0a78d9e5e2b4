# 统一训练工作流前端使用指南

## 概述

新的统一训练工作流系统提供了一套完整的前端解决方案，支持深度学习、强化学习和大模型三种类型的训练任务。

## 核心组件

### 1. WorkflowStepWrapper 组件

这是核心的步骤包装组件，负责处理工作流的状态管理和API调用。

**使用方式:**
```vue
<template>
  <WorkflowStepWrapper
    :step-number="1"
    :training-type="TRAINING_TYPES.DEEP_LEARNING"
    :need-save="true"
    @step-data-changed="onStepDataChanged"
    @step-completed="onStepCompleted"
  >
    <template #default="{ 
      currentWorkflow, 
      stepData, 
      loading, 
      onDataAnalysis, 
      onNextStep, 
      onPrevStep,
      onTrainingSubmit,
      onTrainingControl,
      onStepComplete
    }">
      <!-- 你的步骤内容 -->
    </template>
  </WorkflowStepWrapper>
</template>
```

**Props:**
- `step-number`: 当前步骤号 (1-6)
- `training-type`: 训练类型 (deep_learning/reinforcement_learning/large_model)
- `need-save`: 是否需要保存步骤数据 (前三步为true，后面步骤为false)

**插槽参数:**
- `currentWorkflow`: 当前工作流信息
- `stepData`: 当前步骤数据
- `loading`: 加载状态
- `onDataAnalysis`: 数据分析方法 (第一步使用)
- `onNextStep`: 下一步方法 (前两步使用)
- `onPrevStep`: 上一步方法
- `onTrainingSubmit`: 提交训练方法 (第三步使用)
- `onTrainingControl`: 训练控制方法 (第三步使用)
- `onStepComplete`: 完成步骤方法 (第三步使用)

### 2. WorkflowTaskList 组件

右侧任务列表组件，显示所有训练任务并支持跳转。

**使用方式:**
```vue
<WorkflowTaskList
  :selected-task-id="currentTaskId"
  @task-selected="onTaskSelected"
/>
```

### 3. workflowStore

Pinia状态管理store，管理工作流的全局状态。

**主要方法:**
- `createWorkflow()`: 创建新工作流
- `startDataAnalysis()`: 开始数据分析
- `saveStepData()`: 保存步骤数据
- `submitTraining()`: 提交训练
- `controlTraining()`: 训练控制
- `fetchWorkflowList()`: 获取任务列表
- `jumpToStep()`: 跳转到步骤

## 步骤实现指南

### 第一步：数据加载分割

**关键点:**
- 使用 `onDataAnalysis` 方法进行数据分析
- 使用 `onNextStep` 方法保存数据并跳转到下一步
- `need-save="true"` 确保数据会被保存

**示例代码:**
```vue
<script setup>
// 开始分析
const startAnalysis = async (onDataAnalysis) => {
  const datasetConfig = {
    dataset_id: selectedDataset.value,
    dataset_name: selectedDatasetName.value,
    total_samples: 1000,
    train_ratio: 0.8,
    val_ratio: 0.2
  }
  
  await onDataAnalysis(datasetConfig)
}

// 下一步
const handleNextStep = async (onNextStep) => {
  const stepData = {
    dataset_id: selectedDataset.value,
    train_samples: trainSamples.value,
    val_samples: valSamples.value,
    analysis_completed: true
  }
  
  await onNextStep(stepData)
}
</script>
```

### 第二步：训练参数配备

**关键点:**
- 配置训练参数
- 使用 `onNextStep` 方法保存参数并跳转到第三步
- `need-save="true"` 确保参数会被保存

**示例代码:**
```vue
<script setup>
const handleNextStep = async (onNextStep) => {
  const stepData = {
    epochs: formData.value.epochs,
    batch_size: formData.value.batch_size,
    learning_rate: formData.value.learning_rate,
    optimizer: formData.value.optimizer
  }
  
  await onNextStep(stepData)
}
</script>
```

### 第三步：提交模型训练

**关键点:**
- 使用 `onTrainingSubmit` 开始训练
- 使用 `onTrainingControl` 控制训练状态
- 使用 `onStepComplete` 完成训练步骤
- 实时更新训练指标和日志

**示例代码:**
```vue
<script setup>
// 开始训练
const startTraining = async (onTrainingSubmit) => {
  const trainingConfig = {
    algorithm_version: 'v8',
    model_path: 'yolov8n.pt',
    save_dir: './runs/train'
  }
  
  await onTrainingSubmit(trainingConfig)
}

// 训练控制
const controlTraining = async (action, onTrainingControl) => {
  await onTrainingControl(action) // 'pause', 'resume', 'stop'
}

// 完成步骤
const completeStep = async (onStepComplete) => {
  const finalMetrics = {
    final_train_loss: currentMetrics.value.train_loss,
    final_val_loss: currentMetrics.value.val_loss
  }
  
  await onStepComplete(finalMetrics)
}
</script>
```

### 第四步及以后：结果展示

**关键点:**
- `need-save="false"` 不需要保存数据
- 直接使用普通的下一步跳转
- 主要用于展示结果和导出模型

## 状态管理

### 工作流状态
- `draft`: 草稿
- `step1_saved`: 第一步已保存
- `step2_saved`: 第二步已保存
- `training`: 训练中
- `paused`: 已暂停
- `training_completed`: 训练完成
- `completed`: 全部完成

### 训练状态
- `not_started`: 未开始
- `running`: 运行中
- `paused`: 已暂停
- `stopped`: 已停止
- `completed`: 已完成
- `failed`: 训练失败

## 路由配置

使用提供的 `workflowRoutes.js` 配置文件，包含了所有三种训练类型的路由。

**路由结构:**
```
/training-workflow
  ├── deep-learning
  │   ├── step1
  │   ├── step2
  │   ├── step3
  │   ├── step4
  │   └── step5
  ├── reinforcement-learning
  │   ├── step1
  │   ├── step2
  │   ├── step3
  │   ├── step4
  │   └── step5
  └── large-model
      ├── step1
      ├── step2
      ├── step3
      ├── step4
      ├── step5
      └── step6
```

## 最佳实践

### 1. 错误处理
```vue
<script setup>
const handleNextStep = async (onNextStep) => {
  try {
    await onNextStep(stepData)
  } catch (error) {
    console.error('保存失败:', error)
    // 错误已经在store中处理并显示通知
  }
}
</script>
```

### 2. 数据验证
```vue
<script setup>
const handleNextStep = async (onNextStep) => {
  // 验证数据
  if (!validateForm()) {
    return
  }
  
  await onNextStep(stepData)
}
</script>
```

### 3. 加载状态
```vue
<template>
<q-btn
  :loading="loading"
  @click="handleNextStep(onNextStep)"
>
  下一步
</q-btn>
</template>
```

### 4. 实时数据更新
```vue
<script setup>
// 监听训练指标变化
watch(() => workflowStore.trainingMetrics, (newMetrics) => {
  // 更新图表或显示
}, { deep: true })
</script>
```

## 注意事项

1. **前三步的下一步按钮会进行保存操作**，后面页面的下一步不需要保存
2. **第三步是训练控制的核心**，需要处理开始/暂停/恢复/停止等操作
3. **右侧任务列表支持跳转**，会根据保存状态跳转到正确的步骤
4. **状态管理是响应式的**，页面刷新后状态会正确恢复
5. **错误处理已经统一**，不需要在每个组件中重复处理

## 迁移指南

如果要将现有页面迁移到新的工作流系统：

1. 用 `WorkflowStepWrapper` 包装现有内容
2. 修改下一步按钮的点击事件
3. 添加必要的事件处理函数
4. 更新路由配置
5. 测试跳转和状态保存功能
