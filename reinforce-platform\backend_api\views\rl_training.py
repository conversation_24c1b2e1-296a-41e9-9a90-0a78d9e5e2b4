import json
import logging
import time
import hashlib
import threading
from datetime import datetime, timedelta
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED, HTTP_400_BAD_REQUEST, HTTP_404_NOT_FOUND, HTTP_500_INTERNAL_SERVER_ERROR
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>, MultiPartParser
from django.utils.dateparse import parse_datetime
from django.utils import timezone as django_timezone
from django.db.models import Q
from django.core.paginator import Paginator
from django.core.exceptions import ValidationError
from django.http import StreamingHttpResponse, JsonResponse
from django.views import View
from django.shortcuts import get_object_or_404

from backend_api.models.rl_training import RLTrainingTask, RLTrainingMetrics, RLResourceMetrics, RLTrainingConfig, RLTrainingModel
from backend_api.serializers.rl_training import (
    RLTrainingStartSerializer, RLTrainingTaskSerializer, RLConfigSaveSerializer, 
    RLConfigImportSerializer, RLTrainingConfigSerializer, RLTrainingStatusSerializer,
    RLTrainingListResponseSerializer, RLConfigListResponseSerializer,
    RLTrainingMetricsResponseSerializer, RLResourceMetricsResponseSerializer
)
from utils.rl_trainer import RLDockerTrainer, create_rl_training_task

logger = logging.getLogger(__name__)

# 全局训练器存储
_active_trainers = {}


def start_rl_training_in_background(training_task, trainer):
    """在后台启动强化学习训练任务"""
    try:
        logger.info(f"在后台启动强化学习训练任务: {training_task.training_id}")
        success = trainer.start()

        if success:
            # 更新任务状态为运行中
            training_task.status = 'running'
            training_task.start_time = django_timezone.now()
            training_task.save()
            logger.info(f"强化学习训练任务 {training_task.training_id} 启动成功")
        else:
            # 启动失败，更新任务状态
            training_task.status = 'failed'
            training_task.save()
            logger.error(f"强化学习训练任务 {training_task.training_id} 启动失败")
    except Exception as e:
        logger.error(f"启动强化学习训练任务 {training_task.training_id} 时发生异常: {e}")
        training_task.status = 'failed'
        training_task.save()


def stop_rl_training_in_background(training_id, training_task, trainer):
    """在后台停止强化学习训练任务"""
    try:
        logger.info(f"在后台停止强化学习训练任务: {training_id}")
        success = trainer.stop()

        if success:
            # 停止成功，从活跃训练器列表中移除
            if training_id in _active_trainers:
                del _active_trainers[training_id]

            # 更新任务状态
            training_task.status = 'stopped'
            training_task.end_time = django_timezone.now()
            training_task.save()
            logger.info(f"强化学习训练任务 {training_id} 停止成功，状态已更新为stopped")
        else:
            logger.warning(f"停止强化学习训练任务 {training_id} 失败，但仍标记为停止状态")
            # 即使停止操作失败，也标记为停止状态
            training_task.status = 'stopped'
            training_task.end_time = django_timezone.now()
            training_task.save()
    except Exception as e:
        logger.error(f"停止强化学习训练任务 {training_id} 时发生异常: {e}")
        training_task.status = 'stopped'
        training_task.end_time = django_timezone.now()
        training_task.save()


class RLTrainingStartView(APIView):
    """开始强化学习训练"""
    
    parser_classes = [JSONParser]
    # 暂时移除权限要求用于调试
    # permission_classes = [IsAuthenticated]
    
    def post(self, request):
        try:
            # 添加更详细的调试信息
            logger.info("="*50)
            logger.info("RL Training Start Request Received")
            logger.info("="*50)
            logger.info(f"User: {getattr(request.user, 'id', 'Anonymous')}")
            logger.info(f"Method: {request.method}")
            logger.info(f"Content Type: {request.content_type}")
            logger.info(f"Headers: {dict(request.headers)}")
            logger.info(f"Request data: {request.data}")
            logger.info(f"Request data type: {type(request.data)}")
            logger.info("="*50)
            
            # 检查请求数据是否为空
            if not request.data:
                logger.warning("Empty request data received")
                return Response({
                    "success": False,
                    "error": {
                        "code": "EMPTY_DATA",
                        "message": "请求数据为空，请确保发送了有效的JSON数据"
                    }
                }, status=HTTP_400_BAD_REQUEST)
            
            serializer = RLTrainingStartSerializer(data=request.data)
            if not serializer.is_valid():
                logger.warning(f"Invalid training request: {serializer.errors}")
                
                # 提供更详细的错误信息
                error_details = {}
                help_info = {}
                
                for field, errors in serializer.errors.items():
                    error_details[field] = errors
                    if field == 'agent':
                        help_info[field] = {
                            "description": "智能体配置",
                            "required_fields": ["sampleTime", "actionSpace", "observationSpace", "rewardFunction"],
                            "actionSpace_example": {
                                "type": "Box",
                                "low": [-1, -1, -1],
                                "high": [1, 1, 1]
                            },
                            "observationSpace_example": {
                                "type": "Box",
                                "shape": [20]
                            }
                        }
                    elif field == 'algorithm':
                        help_info[field] = {
                            "description": "算法配置",
                            "required_fields": ["version", "rlType"],
                            "valid_rlTypes": ["PPO", "SAC", "TD3", "DQN", "A3C"]
                        }
                    elif field == 'simulation':
                        help_info[field] = {
                            "description": "仿真配置",
                            "required_fields": ["dataSource", "useExternalEnv"]
                        }
                    elif field == 'hyperParams':
                        help_info[field] = {
                            "description": "超参数配置",
                            "required_fields": ["learningRate", "epochs", "batchSize"],
                            "ranges": {
                                "learningRate": "0 < value <= 1",
                                "epochs": "positive integer",
                                "batchSize": "positive integer"
                            }
                        }
                    elif field == 'cluster':
                        help_info[field] = {
                            "description": "集群资源配置",
                            "required_fields": ["cpuCount", "gpuCount"]
                        }
                    elif field == 'output':
                        help_info[field] = {
                            "description": "输出配置",
                            "required_fields": ["savePath", "saveName"]
                        }
                
                return Response({
                    "success": False,
                    "error": {
                        "code": "VALIDATION_ERROR",
                        "message": "请求参数验证失败",
                        "details": error_details,
                        "help": help_info
                    }
                }, status=HTTP_400_BAD_REQUEST)
            
            # 检查用户是否已有运行中的训练任务（仅对认证用户）
            # if hasattr(request.user, 'id') and request.user.id:
            #     running_tasks = RLTrainingTask.objects.filter(
            #         created_by=request.user,
            #         status__in=['pending', 'running']
            #     ).count()
                
            #     if running_tasks >= 3:  # 限制每个用户最多3个并发任务
            #         logger.warning(f"User {request.user.id} has too many running tasks: {running_tasks}")
            #         return Response({
            #             "success": False,
            #             "error": {
            #                 "code": "TOO_MANY_TASKS",
            #                 "message": "您已有太多运行中的训练任务，请等待其完成后再启动新任务"
            #             }
            #         }, status=HTTP_400_BAD_REQUEST)
            
            # 创建训练任务
            training_task = create_rl_training_task(serializer.validated_data, request.user)
            logger.info(f"Created training task: {training_task.training_id}")

            # 创建训练器
            trainer = RLDockerTrainer(training_task)
            _active_trainers[training_task.training_id] = trainer

            # 设置初始状态为pending
            training_task.status = 'pending'
            training_task.save()

            # 在后台线程中启动训练
            thread = threading.Thread(
                target=start_rl_training_in_background,
                args=(training_task, trainer)
            )
            thread.daemon = True  # 守护线程，主程序退出时会自动结束
            thread.start()

            # 立即返回响应（与深度学习保持一致的响应结构）
            logger.info(f"强化学习训练任务 {training_task.training_id} 已提交")
            return Response({
                "success": True,
                "message": "强化学习训练任务已提交",
                "status": "pending",
                "trainingId": training_task.training_id,
                "estimatedDuration": training_task.estimated_duration
            }, status=HTTP_201_CREATED)
                
        except Exception as e:
            logger.error(f"Failed to start RL training: {e}", exc_info=True)
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=HTTP_500_INTERNAL_SERVER_ERROR)


class RLTrainingStopView(APIView):
    """停止强化学习训练"""
    
    def post(self, request, training_id):
        try:
            logger.info(f"Stopping RL training: {training_id}")
            
            # 查找训练任务
            try:
                training_task = RLTrainingTask.objects.get(training_id=training_id)
            except RLTrainingTask.DoesNotExist:
                logger.warning(f"Training task not found: {training_id}")
                return Response({
                    "success": False,
                    "error": {
                        "code": "TASK_NOT_FOUND",
                        "message": f"训练任务 {training_id} 不存在"
                    }
                }, status=HTTP_404_NOT_FOUND)
            
            # 检查权限
            if training_task.created_by != request.user and not request.user.is_staff:
                logger.warning(f"User {request.user.id} has no permission to stop task {training_id}")
                return Response({
                    "success": False,
                    "error": {
                        "code": "PERMISSION_DENIED",
                        "message": "您没有权限停止此训练任务"
                    }
                }, status=HTTP_400_BAD_REQUEST)
            
            # 检查任务状态
            if training_task.status not in ['pending', 'running']:
                logger.warning(f"Cannot stop task {training_id} with status: {training_task.status}")
                return Response({
                    "success": False,
                    "error": {
                        "code": "INVALID_STATUS",
                        "message": f"任务状态为 {training_task.status}，无法停止"
                    }
                }, status=HTTP_400_BAD_REQUEST)
            
            # 获取训练器
            trainer = _active_trainers.get(training_id)
            if trainer:
                logger.info(f"找到活跃的强化学习训练器，任务ID: {training_id}")
                try:
                    # 在后台线程中停止训练
                    thread = threading.Thread(
                        target=stop_rl_training_in_background,
                        args=(training_id, training_task, trainer)
                    )
                    thread.daemon = True  # 守护线程，主程序退出时会自动结束
                    thread.start()

                    # 立即更新状态为"停止中"
                    training_task.status = 'stopping'
                    training_task.save()

                    # 立即返回响应
                    logger.info(f"强化学习训练任务 {training_id} 停止请求已提交")
                    return Response({
                        "success": True,
                        "data": {
                            "trainingId": training_id,
                            "message": "强化学习训练任务停止请求已提交",
                            "status": "stopping"
                        }
                    }, status=HTTP_200_OK)

                except Exception as e:
                    logger.error(f"提交停止请求失败: {e}")
                    return Response({
                        "success": False,
                        "error": {
                            "code": "STOP_REQUEST_FAILED",
                            "message": f"提交停止请求失败: {str(e)}"
                        }
                    }, status=HTTP_500_INTERNAL_SERVER_ERROR)
            else:
                # 没有找到活跃的训练器，直接更新状态
                logger.warning(f"未找到活跃的训练器，任务ID: {training_id}")
                training_task.status = 'stopped'
                training_task.end_time = django_timezone.now()
                training_task.save()

                return Response({
                    "success": True,
                    "data": {
                        "trainingId": training_id,
                        "message": "训练任务已停止",
                        "status": "stopped"
                    }
                }, status=HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Failed to stop RL training: {e}", exc_info=True)
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=HTTP_500_INTERNAL_SERVER_ERROR)


class RLTrainingPauseView(APIView):
    """暂停强化学习训练"""

    def post(self, request, training_id):
        try:
            # 查找训练任务
            try:
                training_task = RLTrainingTask.objects.get(training_id=training_id)
            except RLTrainingTask.DoesNotExist:
                return Response({
                    "success": False,
                    "error": {
                        "code": "TASK_NOT_FOUND",
                        "message": f"训练任务 {training_id} 不存在"
                    }
                }, status=HTTP_404_NOT_FOUND)

            # 检查任务状态
            if training_task.status not in ['running']:
                return Response({
                    "success": False,
                    "error": {
                        "code": "INVALID_STATUS",
                        "message": f"任务状态为 {training_task.status}，无法暂停"
                    }
                }, status=HTTP_400_BAD_REQUEST)

            # 获取训练器
            trainer = _active_trainers.get(training_id)
            if trainer:
                logger.info(f"找到活跃的强化学习训练器，任务ID: {training_id}")
                try:
                    # 在后台线程中暂停训练
                    thread = threading.Thread(
                        target=pause_rl_training_in_background,
                        args=(training_id, training_task, trainer)
                    )
                    thread.daemon = True
                    thread.start()

                    # 立即更新状态为"暂停中"
                    training_task.status = 'pausing'
                    training_task.save()

                    # 立即返回响应
                    logger.info(f"强化学习训练任务 {training_id} 暂停请求已提交")
                    return Response({
                        'success': True,
                        'message': '强化学习训练任务暂停请求已提交',
                        'status': "pausing"
                    }, status=HTTP_200_OK)

                except Exception as e:
                    logger.error(f"提交暂停请求失败: {e}")
                    return Response({
                        "success": False,
                        "error": {
                            "code": "PAUSE_REQUEST_FAILED",
                            "message": f"提交暂停请求失败: {str(e)}"
                        }
                    }, status=HTTP_500_INTERNAL_SERVER_ERROR)
            else:
                return Response({
                    "success": False,
                    "error": {
                        "code": "TRAINER_NOT_FOUND",
                        "message": "未找到活跃的训练器"
                    }
                }, status=HTTP_404_NOT_FOUND)

        except Exception as e:
            logger.error(f"Failed to pause RL training: {e}")
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=HTTP_500_INTERNAL_SERVER_ERROR)


class RLTrainingResumeView(APIView):
    """继续强化学习训练"""

    def post(self, request, training_id):
        try:
            # 查找训练任务
            try:
                training_task = RLTrainingTask.objects.get(training_id=training_id)
            except RLTrainingTask.DoesNotExist:
                return Response({
                    "success": False,
                    "error": {
                        "code": "TASK_NOT_FOUND",
                        "message": f"训练任务 {training_id} 不存在"
                    }
                }, status=HTTP_404_NOT_FOUND)

            # 检查任务状态
            if training_task.status not in ['paused']:
                return Response({
                    "success": False,
                    "error": {
                        "code": "INVALID_STATUS",
                        "message": f"任务状态为 {training_task.status}，无法继续训练"
                    }
                }, status=HTTP_400_BAD_REQUEST)

            # 获取训练器
            trainer = _active_trainers.get(training_id)
            if trainer:
                logger.info(f"找到活跃的强化学习训练器，任务ID: {training_id}")
                try:
                    # 在后台线程中继续训练
                    thread = threading.Thread(
                        target=resume_rl_training_in_background,
                        args=(training_id, training_task, trainer)
                    )
                    thread.daemon = True
                    thread.start()

                    # 立即更新状态为"继续中"
                    training_task.status = 'resuming'
                    training_task.save()

                    # 立即返回响应
                    logger.info(f"强化学习训练任务 {training_id} 继续请求已提交")
                    return Response({
                        'success': True,
                        'message': '强化学习训练任务继续请求已提交',
                        'status': "resuming"
                    }, status=HTTP_200_OK)

                except Exception as e:
                    logger.error(f"提交继续请求失败: {e}")
                    return Response({
                        "success": False,
                        "error": {
                            "code": "RESUME_REQUEST_FAILED",
                            "message": f"提交继续请求失败: {str(e)}"
                        }
                    }, status=HTTP_500_INTERNAL_SERVER_ERROR)
            else:
                return Response({
                    "success": False,
                    "error": {
                        "code": "TRAINER_NOT_FOUND",
                        "message": "未找到活跃的训练器"
                    }
                }, status=HTTP_404_NOT_FOUND)

        except Exception as e:
            logger.error(f"Failed to resume RL training: {e}")
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=HTTP_500_INTERNAL_SERVER_ERROR)


class RLTrainingModelInfoView(APIView):
    """获取强化学习训练生成的模型信息"""

    def get(self, request, training_id):
        try:
            # 查找训练任务
            try:
                training_task = RLTrainingTask.objects.get(training_id=training_id)
            except RLTrainingTask.DoesNotExist:
                return Response({
                    "success": False,
                    "error": {
                        "code": "TASK_NOT_FOUND",
                        "message": f"训练任务 {training_id} 不存在"
                    }
                }, status=HTTP_404_NOT_FOUND)

            # 获取训练器
            trainer = _active_trainers.get(training_id)
            if not trainer:
                return Response({
                    "success": False,
                    "error": {
                        "code": "TRAINER_NOT_FOUND",
                        "message": "未找到活跃的训练器"
                    }
                }, status=HTTP_404_NOT_FOUND)

            # 获取模型信息
            model_info = trainer.get_model_info()
            if model_info:
                return Response({
                    "success": True,
                    "data": {
                        "modelInfo": model_info,
                        "trainingId": training_id
                    }
                }, status=HTTP_200_OK)
            else:
                return Response({
                    "success": False,
                    "error": {
                        "code": "MODEL_NOT_FOUND",
                        "message": "模型信息不存在，可能训练尚未完成"
                    }
                }, status=HTTP_404_NOT_FOUND)

        except Exception as e:
            logger.error(f"Failed to get RL model info: {e}")
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=HTTP_500_INTERNAL_SERVER_ERROR)


class RLTrainingModelDownloadView(APIView):
    """下载强化学习训练生成的模型"""

    def post(self, request, training_id):
        try:
            # 查找训练任务
            try:
                training_task = RLTrainingTask.objects.get(training_id=training_id)
            except RLTrainingTask.DoesNotExist:
                return Response({
                    "success": False,
                    "error": {
                        "code": "TASK_NOT_FOUND",
                        "message": f"训练任务 {training_id} 不存在"
                    }
                }, status=HTTP_404_NOT_FOUND)

            # 获取下载路径参数
            local_path = request.data.get('localPath')
            if not local_path:
                return Response({
                    "success": False,
                    "error": {
                        "code": "MISSING_PARAMETER",
                        "message": "缺少localPath参数"
                    }
                }, status=HTTP_400_BAD_REQUEST)

            # 获取训练器
            trainer = _active_trainers.get(training_id)
            if not trainer:
                return Response({
                    "success": False,
                    "error": {
                        "code": "TRAINER_NOT_FOUND",
                        "message": "未找到活跃的训练器"
                    }
                }, status=HTTP_404_NOT_FOUND)

            # 下载模型
            success = trainer.download_model(local_path)
            if success:
                return Response({
                    "success": True,
                    "data": {
                        "message": "模型下载成功",
                        "localPath": local_path,
                        "trainingId": training_id
                    }
                }, status=HTTP_200_OK)
            else:
                return Response({
                    "success": False,
                    "error": {
                        "code": "DOWNLOAD_FAILED",
                        "message": "模型下载失败"
                    }
                }, status=HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            logger.error(f"Failed to download RL model: {e}")
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=HTTP_500_INTERNAL_SERVER_ERROR)



class RLTrainingLogStreamView(View):
    """强化学习训练日志实时流式传输 (Server-Sent Events)"""

    def get(self, request, training_id):
        """GET方式建立SSE连接（token通过URL参数传递）"""
        return self._handle_sse_connection(request, training_id)

    def post(self, request, training_id):
        """POST方式建立SSE连接（token通过请求体传递，适用于长token）"""
        return self._handle_sse_connection(request, training_id)

    def _handle_sse_connection(self, request, training_id):
        """建立SSE连接，实时推送强化学习训练日志"""

        # 记录请求信息用于调试
        accept_header = request.META.get('HTTP_ACCEPT', '')
        logger.info(f"强化学习日志流SSE请求Accept头: {accept_header}")
        logger.info(f"强化学习日志流SSE请求方法: {request.method}")
        logger.info(f"强化学习日志流SSE请求路径: {request.path}")

        # 支持通过URL参数传递token（用于EventSource）
        token = request.GET.get('token')
        if not token:
            logger.error("强化学习日志流SSE连接缺少token参数")
            return JsonResponse({
                'error': 'Missing token',
                'detail': 'Token parameter is required for SSE connection'
            }, status=401)

        if token:
            from rest_framework_simplejwt.authentication import JWTAuthentication
            from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
            import urllib.parse

            try:
                # 处理可能的URL编码
                decoded_token = urllib.parse.unquote(token)

                # 去掉可能的 "Bearer " 前缀
                if decoded_token.startswith('Bearer '):
                    decoded_token = decoded_token[7:]

                # 去掉首尾空格
                decoded_token = decoded_token.strip()

                # 验证token长度（JWT token通常很长）
                if len(decoded_token) < 50:
                    logger.error(f"强化学习日志流Token长度不足: {len(decoded_token)}, token: {decoded_token[:20]}...")
                    return JsonResponse({
                        'error': 'Invalid token format',
                        'detail': f'Token too short: {len(decoded_token)} characters'
                    }, status=401)

                # 记录token信息用于调试
                logger.info(f"尝试验证强化学习日志流token，长度: {len(decoded_token)}, 前20字符: {decoded_token[:20]}...")

                jwt_auth = JWTAuthentication()
                validated_token = jwt_auth.get_validated_token(decoded_token)
                user = jwt_auth.get_user(validated_token)

                logger.info(f"强化学习日志流Token验证成功，用户: {user.username}")

            except (InvalidToken, TokenError) as e:
                logger.error(f"强化学习日志流Token验证失败: {e}")
                return JsonResponse({
                    'error': 'Invalid token',
                    'detail': str(e)
                }, status=401)
            except Exception as e:
                logger.error(f"强化学习日志流Token处理异常: {e}")
                return JsonResponse({
                    'error': 'Token processing error',
                    'detail': str(e)
                }, status=500)

        # 查找训练任务
        try:
            training_task = RLTrainingTask.objects.get(training_id=training_id)
        except RLTrainingTask.DoesNotExist:
            return JsonResponse({
                "success": False,
                "error": {
                    "code": "TASK_NOT_FOUND",
                    "message": f"强化学习训练任务 {training_id} 不存在"
                }
            }, status=404)

        def log_event_stream():
            """SSE日志事件流生成器"""
            import time
            import json
            import hashlib

            try:
                training_task = get_object_or_404(RLTrainingTask, training_id=training_id)
                trainer = _active_trainers.get(training_id)

                last_log_hash = None
                heartbeat_counter = 0
                final_sent = False  # 标记是否已发送最终数据

                logger.info(f"开始SSE推送强化学习训练日志，任务ID: {training_id}")

                # 发送初始连接确认
                yield f"event: connected\ndata: {json.dumps({'message': 'Connected to RL training log stream', 'training_id': training_id, 'timestamp': int(time.time())})}\n\n"

                while True:
                    try:
                        # 重新获取训练任务状态（可能在其他地方被更新）
                        training_task.refresh_from_db()

                        # 检查训练器是否存在
                        if not trainer:
                            trainer = _active_trainers.get(training_id)
                            if not trainer:
                                yield f"event: error\ndata: {json.dumps({'error': 'RL training task not found or not active', 'training_id': training_id, 'timestamp': int(time.time())})}\n\n"
                                time.sleep(10)
                                continue

                        # 获取当前日志
                        try:
                            current_logs = trainer.get_training_logs(lines=50, mode='tail')
                            if current_logs and current_logs != "暂无日志":
                                # 计算日志内容的哈希值，避免重复发送相同内容
                                current_hash = hashlib.md5(current_logs.encode()).hexdigest()

                                if current_hash != last_log_hash:
                                    yield f"event: logs\ndata: {json.dumps({'type': 'update', 'logs': current_logs, 'timestamp': int(time.time()), 'training_id': training_id})}\n\n"
                                    last_log_hash = current_hash
                                    logger.debug(f"推送新的强化学习训练日志，任务ID: {training_id}")
                        except Exception as log_error:
                            logger.error(f"获取强化学习训练日志失败: {log_error}")
                            yield f"event: error\ndata: {json.dumps({'error': f'Failed to get RL logs: {str(log_error)}', 'timestamp': int(time.time())})}\n\n"

                        # 检查训练状态
                        if training_task.status in ['completed', 'failed', 'cancelled', 'stopped'] and not final_sent:
                            try:
                                # 发送最终日志
                                final_logs = trainer.get_training_logs(lines=100, mode='tail') if trainer else "强化学习训练已结束，无法获取更多日志"
                                yield f"event: final\ndata: {json.dumps({'type': 'final', 'status': training_task.status, 'logs': final_logs, 'timestamp': int(time.time()), 'training_id': training_id})}\n\n"

                                # 发送关闭事件
                                yield f"event: close\ndata: {json.dumps({'message': 'RL training finished', 'status': training_task.status, 'timestamp': int(time.time())})}\n\n"
                                logger.info(f"强化学习训练任务 {training_id} 已完成，关闭日志流连接")

                                final_sent = True
                                break
                            except Exception as final_error:
                                logger.error(f"发送最终日志失败: {final_error}")
                                yield f"event: error\ndata: {json.dumps({'error': f'Failed to send final logs: {str(final_error)}', 'timestamp': int(time.time())})}\n\n"

                        # 每30秒发送一次心跳
                        heartbeat_counter += 1
                        if heartbeat_counter >= 10:  # 3秒 * 10 = 30秒
                            yield f"event: heartbeat\ndata: {json.dumps({'timestamp': int(time.time()), 'training_id': training_id})}\n\n"
                            heartbeat_counter = 0

                        time.sleep(3)  # 3秒检查一次

                    except Exception as e:
                        logger.error(f"SSE日志推送过程中出错: {e}")
                        yield f"event: error\ndata: {json.dumps({'error': str(e), 'timestamp': int(time.time())})}\n\n"
                        time.sleep(3)

            except Exception as e:
                logger.error(f"强化学习日志流初始化失败: {e}")
                yield f"event: error\ndata: {json.dumps({'error': f'Log stream initialization failed: {str(e)}', 'timestamp': int(time.time())})}\n\n"

        # 返回SSE响应
        try:
            response = StreamingHttpResponse(log_event_stream(), content_type='text/event-stream')
            response['Cache-Control'] = 'no-cache'
            # response['Connection'] = 'keep-alive'
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Headers'] = 'Cache-Control'

            return response

        except Exception as e:
            logger.error(f"建立强化学习日志流失败: {e}")
            return JsonResponse({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"建立日志流失败: {str(e)}"
                }
            }, status=500)


class RLTrainingModelListView(APIView):
    """获取强化学习训练模型列表"""

    def get(self, request, training_id):
        try:
            # 查找训练任务
            try:
                training_task = RLTrainingTask.objects.get(training_id=training_id)
            except RLTrainingTask.DoesNotExist:
                return Response({
                    "success": False,
                    "error": {
                        "code": "TASK_NOT_FOUND",
                        "message": f"训练任务 {training_id} 不存在"
                    }
                }, status=HTTP_404_NOT_FOUND)

            # 获取模型列表
            models = RLTrainingModel.objects.filter(task=training_task).order_by('-created_time')

            # 构建响应数据
            models_data = []
            for model in models:
                models_data.append({
                    'id': model.id,
                    'modelName': model.model_name,
                    'modelPath': model.model_path,
                    'checkpointPath': model.checkpoint_path,
                    'algorithmType': model.algorithm_type,
                    'framework': model.framework,
                    'status': model.status,
                    'isBest': model.is_best,
                    'bestEpisodeReward': model.best_episode_reward,
                    'averageEpisodeReward': model.average_episode_reward,
                    'trainingIterations': model.training_iterations,
                    'totalEpisodes': model.total_episodes,
                    'modelSizeMb': model.model_size_mb,
                    'exportedFormats': model.exported_formats,
                    'exportPaths': model.export_paths,
                    'createdTime': model.created_time.isoformat(),
                    'completedTime': model.completed_time.isoformat() if model.completed_time else None,
                    'performanceSummary': model.performance_summary,
                    'metadata': model.metadata
                })

            return Response({
                "success": True,
                "data": {
                    "models": models_data,
                    "total": len(models_data),
                    "trainingId": training_id
                }
            }, status=HTTP_200_OK)

        except Exception as e:
            logger.error(f"Failed to get RL model list: {e}")
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=HTTP_500_INTERNAL_SERVER_ERROR)


class RLTrainingStatusView(APIView):
    """获取训练状态"""
    
    def get(self, request, training_id):
        try:
            # 查找训练任务
            try:
                training_task = RLTrainingTask.objects.get(training_id=training_id)
            except RLTrainingTask.DoesNotExist:
                return Response({
                    "success": False,
                    "error": {
                        "code": "TASK_NOT_FOUND",
                        "message": f"训练任务 {training_id} 不存在"
                    }
                }, status=HTTP_404_NOT_FOUND)
            
            # 获取最新指标
            latest_metrics = RLTrainingMetrics.objects.filter(
                task=training_task
            ).order_by('-timestamp').first()
            
            # 估算结束时间
            estimated_end_time = None
            if training_task.status == 'running' and training_task.started_time:
                if training_task.current_episode and training_task.total_episodes:
                    progress = training_task.current_episode / training_task.total_episodes
                    if progress > 0:
                        elapsed = django_timezone.now() - training_task.started_time
                        total_estimated = elapsed / progress
                        estimated_end_time = training_task.started_time + total_estimated
            
            # 构建响应数据
            status_data = {
                "trainingId": training_task.training_id,
                "status": training_task.status,
                "currentEpisode": training_task.current_episode or 0,
                "totalEpisodes": training_task.total_episodes or 0,
                "progress": min(100, (training_task.current_episode or 0) / max(1, training_task.total_episodes or 1) * 100),
                "startedTime": training_task.started_time,
                "estimatedEndTime": estimated_end_time,
                "lastMetrics": {
                    "cumulativeReward": latest_metrics.cumulative_reward if latest_metrics else 0,
                    "policyLoss": latest_metrics.policy_loss if latest_metrics else 0,
                    "timestamp": latest_metrics.timestamp if latest_metrics else None
                } if latest_metrics else None
            }
            
            return Response({
                "success": True,
                "data": status_data
            }, status=HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Failed to get training status: {e}")
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=HTTP_500_INTERNAL_SERVER_ERROR)


class RLTrainingMetricsView(APIView):
    """获取训练过程数据"""
    
    def get(self, request, training_id):
        try:
            # 查找训练任务
            try:
                training_task = RLTrainingTask.objects.get(training_id=training_id)
            except RLTrainingTask.DoesNotExist:
                return Response({
                    "success": False,
                    "error": {
                        "code": "TASK_NOT_FOUND",
                        "message": f"训练任务 {training_id} 不存在"
                    }
                }, status=HTTP_404_NOT_FOUND)
            
            # 获取查询参数
            limit = int(request.GET.get('limit', 1000))
            start_time = request.GET.get('startTime')
            end_time = request.GET.get('endTime')
            metric_type = request.GET.get('type', 'all')  # all, reward, loss, episode
            
            # 构建查询
            queryset = RLTrainingMetrics.objects.filter(task=training_task)
            
            if start_time:
                start_dt = parse_datetime(start_time)
                if start_dt:
                    queryset = queryset.filter(timestamp__gte=start_dt)
            
            if end_time:
                end_dt = parse_datetime(end_time)
                if end_dt:
                    queryset = queryset.filter(timestamp__lte=end_dt)
            
            # 限制数量并排序
            metrics = queryset.order_by('episode')[:limit]
            
            # 构建响应数据
            response_data = {
                "episodes": [],
                "cumulativeRewards": [],
                "policyLosses": [],
                "valueLosses": [],  # 添加价值损失数据
                "averageRewards": [],
                "episodeLengths": [],
                "explorationRates": []
            }

            for metric in metrics:
                response_data["episodes"].append(metric.episode)
                response_data["cumulativeRewards"].append(metric.cumulative_reward)
                response_data["policyLosses"].append(metric.policy_loss or 0.0)
                response_data["valueLosses"].append(metric.value_loss or 0.0)  # 添加价值损失数据
                response_data["averageRewards"].append(metric.average_reward or 0.0)
                response_data["episodeLengths"].append(metric.episode_length or 0)
                response_data["explorationRates"].append(metric.exploration_rate or 0.0)
            
            # 根据请求类型过滤数据
            if metric_type != 'all':
                filtered_data = {"episodes": response_data["episodes"]}
                if metric_type == 'reward':
                    filtered_data.update({
                        "cumulativeRewards": response_data["cumulativeRewards"],
                        "averageRewards": response_data["averageRewards"]
                    })
                elif metric_type == 'loss':
                    filtered_data.update({
                        "policyLosses": response_data["policyLosses"],
                        "valueLosses": response_data["valueLosses"]
                    })
                elif metric_type == 'episode':
                    filtered_data.update({
                        "episodeLengths": response_data["episodeLengths"],
                        "explorationRates": response_data["explorationRates"]
                    })
                response_data = filtered_data
            
            return Response({
                "success": True,
                "data": response_data
            }, status=HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Failed to get training metrics: {e}")
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=HTTP_500_INTERNAL_SERVER_ERROR)


class RLTrainingResourcesView(APIView):
    """获取资源利用率数据"""
    
    def get(self, request, training_id):
        try:
            # 查找训练任务
            try:
                training_task = RLTrainingTask.objects.get(training_id=training_id)
            except RLTrainingTask.DoesNotExist:
                return Response({
                    "success": False,
                    "error": {
                        "code": "TASK_NOT_FOUND",
                        "message": f"训练任务 {training_id} 不存在"
                    }
                }, status=HTTP_404_NOT_FOUND)
            
            # 获取查询参数
            limit = int(request.GET.get('limit', 1000))
            start_time = request.GET.get('startTime')
            end_time = request.GET.get('endTime')
            
            # 构建查询
            queryset = RLResourceMetrics.objects.filter(task=training_task)
            
            if start_time:
                start_dt = parse_datetime(start_time)
                if start_dt:
                    queryset = queryset.filter(timestamp__gte=start_dt)
            
            if end_time:
                end_dt = parse_datetime(end_time)
                if end_dt:
                    queryset = queryset.filter(timestamp__lte=end_dt)
            
            # 限制数量并排序
            resources = queryset.order_by('timestamp')[:limit]
            
            # 构建响应数据
            response_data = {
                "timestamps": [],
                "cpuUtilization": [],
                "npuUtilization": [],
                "memoryUtilization": [],
                "networkIO": [],
                "diskIO": []
            }
            
            for resource in resources:
                response_data["timestamps"].append(resource.timestamp)
                response_data["cpuUtilization"].append(resource.cpu_utilization)
                response_data["npuUtilization"].append(resource.npu_utilization)
                response_data["memoryUtilization"].append(resource.memory_utilization)
                response_data["networkIO"].append(resource.network_io or 0.0)
                response_data["diskIO"].append(resource.disk_io or 0.0)
            
            return Response({
                "success": True,
                "data": response_data
            }, status=HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Failed to get resource metrics: {e}")
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=HTTP_500_INTERNAL_SERVER_ERROR)


class RLTrainingConfigSaveView(APIView):
    """保存训练配置"""
    
    parser_classes = [JSONParser]
    
    def post(self, request):
        try:
            logger.info(f"Saving RL config from user: {request.user.id}")
            logger.info(f"Request data: {request.data}")
            logger.info(f"Request content type: {request.content_type}")
            
            # 检查请求数据是否为空
            if not request.data:
                logger.warning("Empty request data received")
                return Response({
                    "success": False,
                    "error": {
                        "code": "EMPTY_DATA",
                        "message": "请求数据为空，请确保发送了有效的JSON数据"
                    }
                }, status=HTTP_400_BAD_REQUEST)
            
            # 检查配置名称字段（支持多种字段名）
            config_name = None
            for field_name in ['configName', 'config_name', 'name']:
                if field_name in request.data:
                    config_name = request.data[field_name]
                    break
            
            if not config_name:
                # 如果没有找到配置名称，尝试从算法配置中生成一个默认名称
                algorithm_config = request.data.get('algorithm', {})
                if algorithm_config:
                    from datetime import datetime
                    default_name = f"RL配置_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    logger.info(f"No config name provided, using default: {default_name}")
                    request.data['configName'] = default_name
                else:
                    logger.warning(f"Missing configName in request data: {list(request.data.keys())}")
                    return Response({
                        "success": False,
                        "error": {
                            "code": "MISSING_CONFIG_NAME",
                            "message": "缺少必需字段 'configName'，或者请求数据格式不正确",
                            "details": {
                                "received_fields": list(request.data.keys()),
                                "expected_fields": ["configName", "algorithm", "simulation", "agent", "hyperParams", "cluster", "output"],
                                "solutions": [
                                    "在请求JSON中添加 'configName' 字段",
                                    "确保Content-Type为application/json",
                                    "检查请求数据格式是否正确"
                                ],
                                "example": {
                                    "configName": "我的RL配置",
                                    "description": "配置描述",
                                    "algorithm": {"version": "1.0", "rlType": "PPO"},
                                    "simulation": {"dataSource": "JSBSim", "useExternalEnv": True},
                                    "agent": {"sampleTime": 0.1, "actionSpace": {"type": "Box"}, "observationSpace": {"type": "Box"}, "rewardFunction": "custom"},
                                    "hyperParams": {"learningRate": 0.0003, "epochs": 1000, "batchSize": 32},
                                    "cluster": {"cpuCount": "4", "gpuCount": "1"},
                                    "output": {"savePath": "/models", "saveName": "model"}
                                }
                            }
                        }
                    }, status=HTTP_400_BAD_REQUEST)
            else:
                # 统一使用 configName 字段
                request.data['configName'] = config_name
            
            serializer = RLConfigSaveSerializer(data=request.data)
            if not serializer.is_valid():
                logger.warning(f"Invalid config save request: {serializer.errors}")
                return Response({
                    "success": False,
                    "error": {
                        "code": "VALIDATION_ERROR",
                        "message": "请求参数验证失败",
                        "details": serializer.errors,
                        "help": {
                            "configName": "配置名称（必需字段）",
                            "description": "配置描述（可选）",
                            "algorithm": "算法配置（必需）- 包含version和rlType",
                            "simulation": "仿真配置（必需）- 包含dataSource和useExternalEnv",
                            "agent": "智能体配置（必需）- 包含sampleTime, actionSpace, observationSpace, rewardFunction",
                            "hyperParams": "超参数配置（必需）- 包含learningRate, epochs, batchSize",
                            "cluster": "集群配置（必需）- 包含cpuCount和gpuCount",
                            "output": "输出配置（必需）- 包含savePath和saveName"
                        }
                    }
                }, status=HTTP_400_BAD_REQUEST)
            
            data = serializer.validated_data
            
            # 检查配置名称是否已存在
            if RLTrainingConfig.objects.filter(
                config_name=data['configName'],
                created_by=request.user
            ).exists():
                return Response({
                    "success": False,
                    "error": {
                        "code": "CONFIG_EXISTS",
                        "message": "配置名称已存在，请使用其他名称"
                    }
                }, status=HTTP_400_BAD_REQUEST)
            
            # 创建配置记录
            config = RLTrainingConfig.objects.create(
                config_name=data['configName'],
                description=data.get('description', ''),
                algorithm_config=data['algorithm'],
                simulation_config=data['simulation'],
                agent_config=data['agent'],
                hyper_params_config=data['hyperParams'],
                cluster_config=data['cluster'],
                output_config=data['output'],
                created_by=request.user
            )
            
            logger.info(f"Successfully saved config: {config.config_id}")
            return Response({
                "success": True,
                "data": {
                    "configId": config.config_id,
                    "configName": config.config_name,
                    "message": "配置保存成功"
                }
            }, status=HTTP_201_CREATED)
            
        except Exception as e:
            logger.error(f"Failed to save RL config: {e}", exc_info=True)
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=HTTP_500_INTERNAL_SERVER_ERROR)


class RLTrainingConfigImportView(APIView):
    """批量导入配置"""
    
    parser_classes = [MultiPartParser]
    
    def post(self, request):
        try:
            logger.info(f"Importing RL config from user: {request.user.id}")
            
            serializer = RLConfigImportSerializer(data=request.data)
            if not serializer.is_valid():
                logger.warning(f"Invalid config import request: {serializer.errors}")
                return Response({
                    "success": False,
                    "error": {
                        "code": "VALIDATION_ERROR",
                        "message": "请求参数验证失败",
                        "details": serializer.errors
                    }
                }, status=HTTP_400_BAD_REQUEST)
            
            config_file = serializer.validated_data['config']
            
            try:
                # 读取并解析JSON配置
                config_content = config_file.read().decode('utf-8')
                config_data = json.loads(config_content)
                
                # 验证配置格式
                required_fields = ['configName', 'algorithm', 'simulation', 'agent', 'hyperParams', 'cluster', 'output']
                for field in required_fields:
                    if field not in config_data:
                        return Response({
                            "success": False,
                            "error": {
                                "code": "INVALID_CONFIG",
                                "message": f"配置文件缺少必需字段: {field}"
                            }
                        }, status=HTTP_400_BAD_REQUEST)
                
                # 检查配置名称是否已存在
                if RLTrainingConfig.objects.filter(
                    config_name=config_data['configName'],
                    created_by=request.user
                ).exists():
                    return Response({
                        "success": False,
                        "error": {
                            "code": "CONFIG_EXISTS",
                            "message": "配置名称已存在，请修改配置名称后重新导入"
                        }
                    }, status=HTTP_400_BAD_REQUEST)
                
                # 创建配置记录
                config = RLTrainingConfig.objects.create(
                    config_name=config_data['configName'],
                    description=config_data.get('description', ''),
                    algorithm_config=config_data['algorithm'],
                    simulation_config=config_data['simulation'],
                    agent_config=config_data['agent'],
                    hyper_params_config=config_data['hyperParams'],
                    cluster_config=config_data['cluster'],
                    output_config=config_data['output'],
                    created_by=request.user
                )
                
                logger.info(f"Successfully imported config: {config.config_id}")
                return Response({
                    "success": True,
                    "data": {
                        "configId": config.config_id,
                        "configName": config.config_name,
                        "message": "配置导入成功"
                    }
                }, status=HTTP_201_CREATED)
                
            except json.JSONDecodeError:
                return Response({
                    "success": False,
                    "error": {
                        "code": "INVALID_JSON",
                        "message": "配置文件格式错误，请确保是有效的JSON格式"
                    }
                }, status=HTTP_400_BAD_REQUEST)
            except UnicodeDecodeError:
                return Response({
                    "success": False,
                    "error": {
                        "code": "INVALID_ENCODING",
                        "message": "配置文件编码错误，请使用UTF-8编码"
                    }
                }, status=HTTP_400_BAD_REQUEST)
            
        except Exception as e:
            logger.error(f"Failed to import RL config: {e}")
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=HTTP_500_INTERNAL_SERVER_ERROR)


class RLTrainingConfigListView(APIView):
    """获取配置列表"""
    
    def get(self, request):
        try:
            # 获取查询参数
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('pageSize', 20))
            search = request.GET.get('search', '')
            
            # 构建查询
            queryset = RLTrainingConfig.objects.filter(created_by=request.user)
            
            if search:
                queryset = queryset.filter(
                    Q(config_name__icontains=search) |
                    Q(description__icontains=search)
                )
            
            # 分页
            paginator = Paginator(queryset.order_by('-created_time'), page_size)
            page_obj = paginator.get_page(page)
            
            # 序列化数据
            serializer = RLTrainingConfigSerializer(page_obj.object_list, many=True)
            
            return Response({
                "success": True,
                "data": serializer.data,
                "total": paginator.count,
                "page": page,
                "pageSize": page_size,
                "totalPages": paginator.num_pages
            }, status=HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Failed to get config list: {e}")
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=HTTP_500_INTERNAL_SERVER_ERROR)


class RLTrainingTaskListView(APIView):
    """获取训练任务列表"""
    
    def get(self, request):
        try:
            # 获取查询参数
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('pageSize', 20))
            status_filter = request.GET.get('status', '')
            search = request.GET.get('search', '')
            
            # 构建查询
            queryset = RLTrainingTask.objects.filter(created_by=request.user)
            
            if status_filter:
                queryset = queryset.filter(status=status_filter)
            
            if search:
                queryset = queryset.filter(
                    Q(training_id__icontains=search) |
                    Q(algorithm_name__icontains=search)
                )
            
            # 分页
            paginator = Paginator(queryset.order_by('-created_time'), page_size)
            page_obj = paginator.get_page(page)
            
            # 序列化数据
            serializer = RLTrainingTaskSerializer(page_obj.object_list, many=True)
            
            return Response({
                "success": True,
                "data": serializer.data,
                "total": paginator.count,
                "page": page,
                "pageSize": page_size,
                "totalPages": paginator.num_pages
            }, status=HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Failed to get task list: {e}")
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=HTTP_500_INTERNAL_SERVER_ERROR)


class RLTrainingConfigDeleteView(APIView):
    """删除训练配置"""
    
    def delete(self, request, config_id):
        try:
            # 查找配置
            try:
                config = RLTrainingConfig.objects.get(
                    config_id=config_id,
                    created_by=request.user
                )
            except RLTrainingConfig.DoesNotExist:
                return Response({
                    "success": False,
                    "error": {
                        "code": "CONFIG_NOT_FOUND",
                        "message": "配置不存在或您没有权限删除"
                    }
                }, status=HTTP_404_NOT_FOUND)
            
            config_name = config.config_name
            config.delete()
            
            logger.info(f"Successfully deleted config: {config_id}")
            return Response({
                "success": True,
                "message": f"配置 '{config_name}' 已删除"
            }, status=HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Failed to delete config: {e}")
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=HTTP_500_INTERNAL_SERVER_ERROR)


class RLTrainingConfigExportView(APIView):
    """导出训练配置"""
    
    def get(self, request, config_id):
        try:
            # 查找配置
            try:
                config = RLTrainingConfig.objects.get(
                    config_id=config_id,
                    created_by=request.user
                )
            except RLTrainingConfig.DoesNotExist:
                return Response({
                    "success": False,
                    "error": {
                        "code": "CONFIG_NOT_FOUND",
                        "message": "配置不存在或您没有权限导出"
                    }
                }, status=HTTP_404_NOT_FOUND)
            
            # 构建导出数据
            export_data = {
                "configName": config.config_name,
                "description": config.description,
                "algorithm": config.algorithm_config,
                "simulation": config.simulation_config,
                "agent": config.agent_config,
                "hyperParams": config.hyper_params_config,
                "cluster": config.cluster_config,
                "output": config.output_config,
                "exportTime": django_timezone.now().isoformat(),
                "version": "1.0"
            }
            
            from django.http import JsonResponse
            response = JsonResponse(export_data, json_dumps_params={'ensure_ascii': False, 'indent': 2})
            response['Content-Disposition'] = f'attachment; filename="{config.config_name}_config.json"'
            return response
            
        except Exception as e:
            logger.error(f"Failed to export config: {e}")
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=HTTP_500_INTERNAL_SERVER_ERROR)


class RLTrainingMetricsStreamView(View):
    """强化学习训练指标实时推送视图 (SSE)"""

    def get(self, request, training_id):
        """GET方式建立SSE连接（token通过URL参数传递）"""
        return self._handle_sse_connection(request, training_id)

    def post(self, request, training_id):
        """POST方式建立SSE连接（token通过请求体传递，适用于长token）"""
        return self._handle_sse_connection(request, training_id)

    def _handle_sse_connection(self, request, training_id):
        """处理SSE连接的通用方法"""

        # 记录请求信息用于调试
        accept_header = request.META.get('HTTP_ACCEPT', '')
        logger.info(f"强化学习指标流SSE请求Accept头: {accept_header}")
        logger.info(f"强化学习指标流SSE请求方法: {request.method}")
        logger.info(f"强化学习指标流SSE请求路径: {request.path}")

        # 支持通过URL参数传递token（用于EventSource）
        token = request.GET.get('token')
        if not token:
            logger.error("强化学习指标流SSE连接缺少token参数")
            return JsonResponse({
                'error': 'Missing token',
                'detail': 'Token parameter is required for SSE connection'
            }, status=401)

        if token:
            from rest_framework_simplejwt.authentication import JWTAuthentication
            from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
            import urllib.parse

            try:
                # 处理可能的URL编码
                decoded_token = urllib.parse.unquote(token)

                # 去掉可能的 "Bearer " 前缀
                if decoded_token.startswith('Bearer '):
                    decoded_token = decoded_token[7:]

                # 去掉首尾空格
                decoded_token = decoded_token.strip()

                # 验证token长度（JWT token通常很长）
                if len(decoded_token) < 50:
                    logger.error(f"强化学习指标流Token长度不足: {len(decoded_token)}, token: {decoded_token[:20]}...")
                    return JsonResponse({
                        'error': 'Invalid token format',
                        'detail': f'Token too short: {len(decoded_token)} characters'
                    }, status=401)

                # 记录token信息用于调试
                logger.info(f"尝试验证强化学习指标流token，长度: {len(decoded_token)}, 前20字符: {decoded_token[:20]}...")

                jwt_auth = JWTAuthentication()
                validated_token = jwt_auth.get_validated_token(decoded_token)
                user = jwt_auth.get_user(validated_token)
                request.user = user

                logger.info(f"强化学习指标流SSE认证成功，用户: {user.username}, 训练任务: {training_id}")

            except (InvalidToken, TokenError) as e:
                logger.error(f"强化学习指标流SSE token验证失败: {str(e)}")
                return JsonResponse({
                    'error': 'Invalid token',
                    'detail': str(e)
                }, status=401)
            except Exception as e:
                logger.error(f"强化学习指标流SSE认证过程中发生错误: {str(e)}")
                return JsonResponse({
                    'error': 'Authentication error',
                    'detail': str(e)
                }, status=401)

        try:
            # 验证训练任务是否存在
            training_task = get_object_or_404(RLTrainingTask, training_id=training_id)
            logger.info(f"建立强化学习训练指标SSE连接，任务ID: {training_id}")

            # 设置SSE响应头
            response = StreamingHttpResponse(
                self._event_stream(training_id),
                content_type='text/event-stream'
            )
            response['Cache-Control'] = 'no-cache'
            # response['Connection'] = 'keep-alive'
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Headers'] = 'Cache-Control'

            return response

        except Exception as e:
            logger.error(f"建立强化学习训练指标SSE连接失败: {e}")
            return StreamingHttpResponse(
                iter([f"event: error\ndata: {json.dumps({'error': str(e)})}\n\n"]),
                content_type='text/event-stream'
            )

    def _event_stream(self, training_id):
        """SSE事件流生成器"""
        try:
            training_task = get_object_or_404(RLTrainingTask, training_id=training_id)
            trainer = _active_trainers.get(training_id)

            last_metrics_hash = None
            heartbeat_counter = 0
            final_sent = False  # 标记是否已发送最终数据

            logger.info(f"开始SSE推送强化学习训练指标，任务ID: {training_id}")

            # 发送初始连接确认
            yield f"event: connected\ndata: {json.dumps({'message': 'Connected to RL training metrics stream', 'training_id': training_id})}\n\n"

            while True:
                try:
                    # 重新获取训练任务状态（可能在其他地方被更新）
                    training_task.refresh_from_db()

                    # 检查训练是否已完成或失败
                    if training_task.status in ['completed', 'failed', 'stopped'] and not final_sent:
                        logger.info(f"强化学习训练任务 {training_id} 已结束，状态: {training_task.status}")

                        # 发送最终状态
                        final_data = {
                            'status': training_task.status,
                            'message': f'训练已{training_task.status}',
                            'timestamp': int(time.time()),
                            'final': True
                        }

                        # 如果有最终指标，也一并发送
                        if trainer:
                            try:
                                final_metrics = trainer.get_training_metrics()
                                if final_metrics:
                                    final_data.update(final_metrics)
                            except Exception as e:
                                logger.warning(f"获取最终指标失败: {e}")

                        yield f"event: final\ndata: {json.dumps(final_data)}\n\n"
                        final_sent = True

                        # 等待一段时间后关闭连接
                        time.sleep(10)
                        break

                    # 获取当前训练指标
                    current_data = {
                        'status': training_task.status,
                        'timestamp': int(time.time()),
                        'training_id': training_id
                    }

                    # 如果有训练器，获取详细指标
                    if trainer:
                        try:
                            # 刷新数据库状态，确保使用最新状态
                            training_task.refresh_from_db()

                            # 主动更新训练任务状态（与深度学习保持一致）
                            task_status = trainer.get_task_status()
                            if task_status and 'status' in task_status:
                                old_status = training_task.status
                                new_status = task_status['status']

                                # 只有状态发生变化时才更新数据库
                                if old_status != new_status:
                                    logger.info(f"强化学习SSE检测到状态变化: {old_status} -> {new_status}")
                                    training_task.status = new_status

                                    # 如果是完成状态，设置结束时间
                                    if new_status in ['completed', 'failed', 'stopped']:
                                        training_task.end_time = django_timezone.now()

                                    training_task.save()

                            # 更新当前数据的状态（使用最新更新的状态）
                            current_data['status'] = training_task.status

                            metrics = trainer.get_training_metrics()
                            if metrics:
                                current_data.update(metrics)
                        except Exception as e:
                            logger.warning(f"获取强化学习训练指标失败: {e}")
                            current_data['metrics_error'] = str(e)

                    # 从数据库获取最新的训练指标
                    try:
                        latest_metrics = RLTrainingMetrics.objects.filter(
                            task=training_task
                        ).order_by('-timestamp').first()

                        if latest_metrics:
                            current_data.update({
                                'episode': latest_metrics.episode,
                                'cumulative_reward': latest_metrics.cumulative_reward,
                                'average_reward': latest_metrics.average_reward,
                                'episode_length': latest_metrics.episode_length,
                                'exploration_rate': latest_metrics.exploration_rate,
                                'policy_loss': latest_metrics.policy_loss,
                                'value_loss': latest_metrics.value_loss,
                                'entropy': latest_metrics.entropy,
                                'timestamp': latest_metrics.timestamp.isoformat()
                            })
                    except Exception as e:
                        logger.warning(f"从数据库获取强化学习指标失败: {e}")

                    # 只有在未发送最终数据时才推送常规指标数据
                    if not final_sent:
                        # 计算数据哈希，只在数据变化时推送
                        current_hash = hashlib.md5(json.dumps(current_data, sort_keys=True).encode()).hexdigest()

                        if current_hash != last_metrics_hash:
                            yield f"event: metrics\ndata: {json.dumps(current_data)}\n\n"
                            last_metrics_hash = current_hash
                            logger.debug(f"推送新的强化学习训练指标数据，任务ID: {training_id}")

                    # 每30秒发送一次心跳
                    heartbeat_counter += 1
                    if heartbeat_counter >= 6:  # 5秒 * 6 = 30秒
                        yield f"event: heartbeat\ndata: {json.dumps({'timestamp': int(time.time()), 'training_id': training_id})}\n\n"
                        heartbeat_counter = 0

                    time.sleep(5)  # 5秒检查一次

                except Exception as e:
                    logger.error(f"SSE推送过程中出错: {e}")
                    yield f"event: error\ndata: {json.dumps({'error': str(e), 'timestamp': int(time.time())})}\n\n"
                    time.sleep(5)

        except Exception as e:
            logger.error(f"SSE事件流生成器出错: {e}")
            yield f"event: error\ndata: {json.dumps({'error': str(e)})}\n\n"


def pause_rl_training_in_background(training_id, training_task, trainer):
    """在后台暂停强化学习训练任务"""
    try:
        logger.info(f"在后台暂停强化学习训练任务: {training_id}")
        success = trainer.pause()

        if success:
            # 暂停成功，更新任务状态
            training_task.status = 'paused'
            training_task.save()
            logger.info(f"强化学习训练任务 {training_id} 暂停成功")
        else:
            logger.warning(f"暂停强化学习训练任务 {training_id} 失败，但仍标记为暂停状态")
            training_task.status = 'paused'
            training_task.save()

    except Exception as e:
        logger.error(f"暂停强化学习训练任务 {training_id} 时出现异常: {str(e)}")
        training_task.status = 'paused'
        training_task.save()


def resume_rl_training_in_background(training_id, training_task, trainer):
    """在后台继续强化学习训练任务"""
    try:
        logger.info(f"在后台继续强化学习训练任务: {training_id}")
        success = trainer.resume()

        if success:
            # 继续训练成功，先设置为resuming状态
            training_task.status = 'resuming'
            training_task.save()
            logger.info(f"强化学习训练任务 {training_id} 继续训练命令执行成功，等待训练进程启动")

            # 等待一段时间让训练进程完全启动
            time.sleep(5)

            # 重新检查训练状态
            training_task.refresh_from_db()
            if training_task.status == 'resuming':
                training_task.status = 'running'
                training_task.save()
                logger.info(f"强化学习训练任务 {training_id} 状态更新为running")
        else:
            logger.warning(f"无法继续强化学习训练任务 {training_id}")
            training_task.status = 'paused'
            training_task.save()

    except Exception as e:
        logger.error(f"继续强化学习训练任务 {training_id} 时出现异常: {str(e)}")
        training_task.status = 'paused'
        training_task.save()