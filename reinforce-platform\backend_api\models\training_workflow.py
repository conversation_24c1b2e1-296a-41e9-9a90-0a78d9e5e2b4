from django.db import models
from django.conf import settings
from django.utils import timezone
import uuid


class TrainingWorkflow(models.Model):
    """统一训练工作流模型 - 支持深度学习、强化学习、大模型三种类型"""

    # 训练类型选择
    TRAINING_TYPE_CHOICES = [
        ('deep_learning', '深度学习'),
        ('reinforcement_learning', '强化学习'),
        ('large_model', '大模型'),
    ]

    # 任务状态
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('step1_saved', '步骤1已保存'),
        ('step2_saved', '步骤2已保存'),
        ('training_ready', '准备训练'),
        ('training', '训练中'),
        ('paused', '暂停训练'),
        ('training_completed', '训练完成'),
        ('evaluation_completed', '评估完成'),
        ('export_completed', '导出完成'),
        ('completed', '全部完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]

    # 训练状态（用于第三步训练页面的具体状态）
    TRAINING_STATUS_CHOICES = [
        ('not_started', '未开始'),
        ('running', '训练中'),
        ('paused', '已暂停'),
        ('stopped', '已停止'),
        ('completed', '已完成'),
        ('failed', '训练失败'),
    ]
    
    # 基本信息
    id = models.AutoField(primary_key=True)
    task_id = models.CharField('任务ID', max_length=100, unique=True, help_text='唯一任务标识符')
    name = models.CharField('任务名称', max_length=200)
    description = models.TextField('任务描述', blank=True)

    # 训练类型和任务类型
    training_type = models.CharField('训练类型', max_length=30, choices=TRAINING_TYPE_CHOICES, default='deep_learning')
    task_type = models.CharField('任务类型', max_length=50, blank=True, help_text='具体的任务类型')

    # 状态管理
    status = models.CharField('任务状态', max_length=20, choices=STATUS_CHOICES, default='draft')
    training_status = models.CharField('训练状态', max_length=20, choices=TRAINING_STATUS_CHOICES, default='not_started')
    current_step = models.IntegerField('当前步骤', default=1, help_text='当前所在步骤：1-6')
    
    # 时间信息
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    training_started_at = models.DateTimeField('开始训练时间', null=True, blank=True)
    training_completed_at = models.DateTimeField('训练完成时间', null=True, blank=True)
    completed_at = models.DateTimeField('全部完成时间', null=True, blank=True)

    # 步骤配置数据 - 统一存储各步骤的配置
    step1_config = models.JSONField('步骤1配置', default=dict, blank=True,
                                   help_text='数据加载分割/交互环境接入配置')
    step2_config = models.JSONField('步骤2配置', default=dict, blank=True,
                                   help_text='训练参数配备/配置')
    step3_config = models.JSONField('步骤3配置', default=dict, blank=True,
                                   help_text='训练基础信息配置')
    step4_config = models.JSONField('步骤4配置', default=dict, blank=True,
                                   help_text='训练结果评估配置')
    step5_config = models.JSONField('步骤5配置', default=dict, blank=True,
                                   help_text='模型导出存储/量化剪枝配置')
    step6_config = models.JSONField('步骤6配置', default=dict, blank=True,
                                   help_text='模型部署推理配置(仅大模型)')

    # 训练过程数据
    training_metrics = models.JSONField('训练过程指标', default=dict, blank=True,
                                       help_text='实时训练指标数据：损失值、准确率等')
    training_logs = models.JSONField('训练日志', default=list, blank=True,
                                    help_text='训练过程详细日志')

    # 关联信息
    training_task_id = models.IntegerField('关联的训练任务ID', null=True, blank=True,
                                          help_text='第三步开始训练时创建的TrainingTask ID')
    model_info_id = models.IntegerField('关联的模型信息ID', null=True, blank=True,
                                       help_text='创建任务时保存的TrainingModel ID')

    # 用户关联
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name='创建者')
    
    class Meta:
        verbose_name = '训练工作流'
        verbose_name_plural = '训练工作流'
        db_table = 'training_workflow'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.get_training_type_display()})"

    def save(self, *args, **kwargs):
        """保存时自动生成task_id"""
        if not self.task_id:
            import uuid
            self.task_id = str(uuid.uuid4())
        super().save(*args, **kwargs)

    def get_step_name(self):
        """根据训练类型和当前步骤获取步骤名称"""
        step_names = {
            'deep_learning': {
                1: '数据加载分割',
                2: '训练参数配备',
                3: '提交模型训练',
                4: '训练结果评估',
                5: '模型导出存储'
            },
            'reinforcement_learning': {
                1: '交互环境接入',
                2: '训练参数配置',
                3: '提交模型训练',
                4: '训练结果评估',
                5: '模型导出存储'
            },
            'large_model': {
                1: '数据加载分割',
                2: '训练参数配备',
                3: '提交微调训练',
                4: '训练结果评估',
                5: '模型量化剪枝',
                6: '模型部署推理'
            }
        }
        return step_names.get(self.training_type, {}).get(self.current_step, f'步骤{self.current_step}')

    def get_max_steps(self):
        """获取当前训练类型的最大步骤数"""
        max_steps = {
            'deep_learning': 5,
            'reinforcement_learning': 5,
            'large_model': 6
        }
        return max_steps.get(self.training_type, 5)

    def get_progress_percentage(self):
        """获取进度百分比"""
        max_steps = self.get_max_steps()
        if self.status == 'completed':
            return 100
        elif self.status == 'failed':
            return 0
        else:
            return int((self.current_step / max_steps) * 100)

    def can_proceed_to_next_step(self):
        """检查是否可以进入下一步"""
        if self.current_step >= self.get_max_steps():
            return False

        # 前两步需要保存配置才能进入下一步
        if self.current_step == 1:
            return self.status in ['step1_saved', 'step2_saved', 'training_ready', 'training', 'paused', 'training_completed']
        elif self.current_step == 2:
            return self.status in ['step2_saved', 'training_ready', 'training', 'paused', 'training_completed']
        else:
            return True

    def get_next_step_number(self):
        """获取下一步骤号"""
        if self.current_step < self.get_max_steps():
            return self.current_step + 1
        return None

    def update_step_config(self, step_number, config_data):
        """更新指定步骤的配置"""
        field_name = f'step{step_number}_config'
        if hasattr(self, field_name):
            current_config = getattr(self, field_name)
            current_config.update(config_data)
            setattr(self, field_name, current_config)
            self.save()

    def get_step_config(self, step_number):
        """获取指定步骤的配置"""
        field_name = f'step{step_number}_config'
        if hasattr(self, field_name):
            return getattr(self, field_name)
        return {}

    def update_training_metrics(self, metrics_data):
        """更新训练指标"""
        self.training_metrics.update(metrics_data)
        self.save()

    def add_training_log(self, log_entry):
        """添加训练日志"""
        if not isinstance(self.training_logs, list):
            self.training_logs = []
        self.training_logs.append({
            'timestamp': timezone.now().isoformat(),
            'content': log_entry
        })
        self.save()

    def get_target_page_for_jump(self):
        """获取从右侧列表跳转时应该进入的页面"""
        # 根据当前状态和步骤确定目标页面
        if self.status == 'draft':
            return 'step1', 1
        elif self.status == 'step1_saved':
            return 'step2', 2  # 第一步已保存，应该进入第二步
        elif self.status == 'step2_saved':
            return 'step3', 3  # 第二步已保存，应该进入第三步
        elif self.status in ['training_ready', 'training', 'paused', 'training_completed']:
            return 'step3', 3  # 训练相关状态，进入第三步
        elif self.status == 'evaluation_completed':
            return 'step4', 4
        elif self.status == 'export_completed':
            return 'step5', 5
        else:
            # 默认根据当前步骤
            step_pages = {
                1: 'step1',
                2: 'step2',
                3: 'step3',
                4: 'step4',
                5: 'step5',
                6: 'step6'
            }
            return step_pages.get(self.current_step, 'step1'), self.current_step




class TrainingMetrics(models.Model):
    """训练指标记录模型"""
    
    workflow = models.ForeignKey(TrainingWorkflow, on_delete=models.CASCADE, related_name='metrics_records')
    
    # 训练基本信息
    epoch = models.IntegerField('训练轮次')
    step = models.IntegerField('训练步数', default=0)
    
    # 损失指标
    train_loss = models.FloatField('训练损失', null=True, blank=True)
    val_loss = models.FloatField('验证损失', null=True, blank=True)
    
    # 准确率指标
    train_accuracy = models.FloatField('训练准确率', null=True, blank=True)
    val_accuracy = models.FloatField('验证准确率', null=True, blank=True)
    
    # 目标检测特有指标
    precision = models.FloatField('精确率', null=True, blank=True)
    recall = models.FloatField('召回率', null=True, blank=True)
    map50 = models.FloatField('mAP@0.5', null=True, blank=True)
    map50_95 = models.FloatField('mAP@0.5:0.95', null=True, blank=True)
    
    # 训练环境指标
    learning_rate = models.FloatField('学习率', null=True, blank=True)
    gpu_memory = models.FloatField('GPU内存使用', null=True, blank=True)
    training_time = models.FloatField('训练时间(秒)', null=True, blank=True)
    
    # 其他指标
    additional_metrics = models.JSONField('其他指标', default=dict, blank=True)
    
    # 记录时间
    recorded_at = models.DateTimeField('记录时间', auto_now_add=True)
    
    class Meta:
        verbose_name = '训练指标'
        verbose_name_plural = '训练指标'
        db_table = 'training_metrics'
        ordering = ['-recorded_at']
        indexes = [
            models.Index(fields=['workflow', 'epoch']),
            models.Index(fields=['recorded_at']),
        ]
    
    def __str__(self):
        return f"{self.workflow.name} - Epoch {self.epoch}"


class ModelEvaluation(models.Model):
    """模型评估结果模型"""
    
    workflow = models.ForeignKey(TrainingWorkflow, on_delete=models.CASCADE, related_name='evaluations')
    
    # 评估基本信息
    evaluation_name = models.CharField('评估名称', max_length=200)
    model_path = models.CharField('模型路径', max_length=500)
    test_dataset_path = models.CharField('测试数据集路径', max_length=500)
    
    # 评估结果
    overall_accuracy = models.FloatField('总体准确率', null=True, blank=True)
    precision = models.FloatField('精确率', null=True, blank=True)
    recall = models.FloatField('召回率', null=True, blank=True)
    f1_score = models.FloatField('F1分数', null=True, blank=True)
    
    # 目标检测特有指标
    map50 = models.FloatField('mAP@0.5', null=True, blank=True)
    map50_95 = models.FloatField('mAP@0.5:0.95', null=True, blank=True)
    
    # 性能指标
    inference_time_ms = models.FloatField('推理时间(毫秒)', null=True, blank=True)
    fps = models.FloatField('FPS', null=True, blank=True)
    model_size_mb = models.FloatField('模型大小(MB)', null=True, blank=True)
    
    # 详细结果
    class_metrics = models.JSONField('各类别指标', default=dict, blank=True)
    confusion_matrix = models.JSONField('混淆矩阵', default=dict, blank=True)
    evaluation_report = models.JSONField('评估报告', default=dict, blank=True)
    
    # 评估状态
    status = models.CharField('评估状态', max_length=20, choices=[
        ('pending', '等待中'),
        ('running', '评估中'),
        ('completed', '已完成'),
        ('failed', '失败'),
    ], default='pending')
    
    # 时间信息
    started_at = models.DateTimeField('开始时间', null=True, blank=True)
    completed_at = models.DateTimeField('完成时间', null=True, blank=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        verbose_name = '模型评估'
        verbose_name_plural = '模型评估'
        db_table = 'model_evaluation'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.workflow.name} - {self.evaluation_name}"


class InferenceModel(models.Model):
    """推理模型模型"""
    
    workflow = models.ForeignKey(TrainingWorkflow, on_delete=models.CASCADE, related_name='inference_models')
    
    # 模型基本信息
    model_name = models.CharField('模型名称', max_length=200)
    model_version = models.CharField('模型版本', max_length=50, default='1.0')
    model_path = models.CharField('模型路径', max_length=500)
    
    # 部署配置
    deployment_config = models.JSONField('部署配置', default=dict, blank=True)
    
    # 模型状态
    status = models.CharField('状态', max_length=20, choices=[
        ('preparing', '准备中'),
        ('ready', '就绪'),
        ('deployed', '已部署'),
        ('failed', '失败'),
        ('archived', '已归档'),
    ], default='preparing')
    
    # 性能信息
    inference_time_ms = models.FloatField('推理时间(毫秒)', null=True, blank=True)
    throughput_qps = models.FloatField('吞吐量(QPS)', null=True, blank=True)
    model_size_mb = models.FloatField('模型大小(MB)', null=True, blank=True)
    
    # 使用统计
    total_requests = models.IntegerField('总请求数', default=0)
    successful_requests = models.IntegerField('成功请求数', default=0)
    
    # 时间信息
    deployed_at = models.DateTimeField('部署时间', null=True, blank=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '推理模型'
        verbose_name_plural = '推理模型'
        db_table = 'inference_model'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.model_name} v{self.model_version}"
