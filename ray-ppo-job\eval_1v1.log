2025-06-27 10:42:57,928	WARNING utils.py:596 -- Detecting docker specified CPUs. In previous versions of Ray, CPU detection in containers was incorrect. Please ensure that <PERSON> has enough CPUs allocated. As a temporary workaround to revert to the prior behavior, set `RAY_USE_MULTIPROCESSING_CPU_COUNT=1` as an env var before starting Ray. Set the env var: `RAY_DISABLE_DOCKER_CPU_WARNING=1` to mute this warning.
2025-06-27 10:42:58,022	INFO worker.py:1852 -- Started a local Ray instance.
2025-06-27 10:42:58,374	WARNING deprecation.py:50 -- DeprecationWarning: `config.training(num_sgd_iter=..)` has been deprecated. Use `config.training(num_epochs=..)` instead. This will raise an error in the future!
2025-06-27 10:42:58,377	WARNING deprecation.py:50 -- DeprecationWarning: `AlgorithmConfig.evaluation(evaluation_num_workers=..)` has been deprecated. Use `AlgorithmConfig.evaluation(evaluation_num_env_runners=..)` instead. This will raise an error in the future!
2025-06-27 10:42:58,380	WARNING algorithm_config.py:4704 -- You are running PPO on the new API stack! This is the new default behavior for this algorithm. If you don't want to use the new API stack, set `config.api_stack(enable_rl_module_and_learner=False,enable_env_runner_and_connector_v2=False)`. For a detailed migration guide, see here: https://docs.ray.io/en/master/rllib/new-api-stack-migration-guide.html
/home/<USER>/.conda/envs/ray/lib/python3.11/site-packages/ray/rllib/algorithms/algorithm.py:512: RayDeprecationWarning: This API is deprecated and may be removed in future Ray releases. You could suppress this warning by setting env variable PYTHONWARNINGS="ignore::DeprecationWarning"
`UnifiedLogger` will be removed in Ray 2.7.
  return UnifiedLogger(config, logdir, loggers=None)
/home/<USER>/.conda/envs/ray/lib/python3.11/site-packages/ray/tune/logger/unified.py:53: RayDeprecationWarning: This API is deprecated and may be removed in future Ray releases. You could suppress this warning by setting env variable PYTHONWARNINGS="ignore::DeprecationWarning"
The `JsonLogger interface is deprecated in favor of the `ray.tune.json.JsonLoggerCallback` interface and will be removed in Ray 2.7.
  self._loggers.append(cls(self.config, self.logdir, self.trial))
/home/<USER>/.conda/envs/ray/lib/python3.11/site-packages/ray/tune/logger/unified.py:53: RayDeprecationWarning: This API is deprecated and may be removed in future Ray releases. You could suppress this warning by setting env variable PYTHONWARNINGS="ignore::DeprecationWarning"
The `CSVLogger interface is deprecated in favor of the `ray.tune.csv.CSVLoggerCallback` interface and will be removed in Ray 2.7.
  self._loggers.append(cls(self.config, self.logdir, self.trial))
/home/<USER>/.conda/envs/ray/lib/python3.11/site-packages/ray/tune/logger/unified.py:53: RayDeprecationWarning: This API is deprecated and may be removed in future Ray releases. You could suppress this warning by setting env variable PYTHONWARNINGS="ignore::DeprecationWarning"
The `TBXLogger interface is deprecated in favor of the `ray.tune.tensorboardx.TBXLoggerCallback` interface and will be removed in Ray 2.7.
  self._loggers.append(cls(self.config, self.logdir, self.trial))
[36m(MultiAgentEnvRunner pid=60099)[0m 2025-06-27 10:43:00,772	WARNING deprecation.py:50 -- DeprecationWarning: `RLModule(config=[RLModuleConfig object])` has been deprecated. Use `RLModule(observation_space=.., action_space=.., inference_only=.., model_config=.., catalog_class=..)` instead. This will raise an error in the future!


     JSBSim Flight Dynamics Model v1.2.2 Mar 22 2025 11:56:49
2025-06-27 10:43:00,919	WARNING deprecation.py:50 -- DeprecationWarning: `RLModule(config=[RLModuleConfig object])` has been deprecated. Use `RLModule(observation_space=.., action_space=.., inference_only=.., model_config=.., catalog_class=..)` instead. This will raise an error in the future!
/home/<USER>/.conda/envs/ray/lib/python3.11/site-packages/gymnasium/envs/registration.py:642: UserWarning: [33mWARN: Overriding environment rllib-multi-agent-env-v0 already in registry.[0m
  logger.warn(f"Overriding environment {new_spec.id} already in registry.")
[36m(_WrappedExecutable pid=60101)[0m Setting up process group for: env:// [rank=0, world_size=1]
2025-06-27 10:43:07,311	WARNING util.py:61 -- Install gputil for GPU system monitoring.
[36m(_WrappedExecutable pid=60101)[0m 2025-06-27 10:43:07,233	WARNING deprecation.py:50 -- DeprecationWarning: `RLModule(config=[RLModuleConfig object])` has been deprecated. Use `RLModule(observation_space=.., action_space=.., inference_only=.., model_config=.., catalog_class=..)` instead. This will raise an error in the future![32m [repeated 3x across cluster] (Ray deduplicates logs by default. Set RAY_DEDUP_LOGS=0 to disable log deduplication, or see https://docs.ray.io/en/master/ray-observability/user-guides/configure-logging.html#log-deduplication for more options.)[0m
2025-06-27 10:43:07,420	INFO trainable.py:577 -- Restored on 10.244.5.121 from checkpoint: Checkpoint(filesystem=local, path=/home/<USER>/xiangshuai/projects/xs/ray-ppo-job/ray_results/jsbsim-1v1-bsl_20250626-184843/PPO_JSBSim-Combat_227d2_00000_0_2025-06-26_18-48-46/checkpoint_000095)
使用检查点: /home/<USER>/xiangshuai/projects/xs/ray-ppo-job/ray_results/jsbsim-1v1-bsl_20250626-184843/PPO_JSBSim-Combat_227d2_00000_0_2025-06-26_18-48-46/checkpoint_000095
[36m(MultiAgentEnvRunner pid=60099)[0m 
[36m(MultiAgentEnvRunner pid=60099)[0m 
[36m(MultiAgentEnvRunner pid=60099)[0m      JSBSim Flight Dynamics Model v1.2.2 Mar 22 2025 11:56:49
[36m(MultiAgentEnvRunner pid=60099)[0m jsbsim agents:  {'A0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7fb8cd7a49d0>, 'B0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7fb8cb439c90>}
[36m(MultiAgentEnvRunner pid=60107)[0m 
[36m(MultiAgentEnvRunner pid=60107)[0m 
jsbsim agents:  {'A0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f53e8486f10>, 'B0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f54c7dd6b50>}
[36m(MultiAgentEnvRunner pid=60100)[0m 
[36m(MultiAgentEnvRunner pid=60100)[0m 
jsbsim agents:  {'A0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f54c7a2fa90>, 'B0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f54c7a2f950>}
jsbsim agents:  {'A0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f5150747c10>, 'B0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f52e0355c90>}

Episode 0 completed:
Episode Length: 961
Agent A0100 Reward: 724.4031495208231
Agent B0100 Reward: 429.4555539788221
------------------------
Average reward over 1 episodes: 0.0
回放文件已保存到: /home/<USER>/xiangshuai/projects/xs/ray-ppo-job/eval_results/jsbsim-1v1-bsl_20250627-104307.acmi
评估结果已保存到: /home/<USER>/xiangshuai/projects/xs/ray-ppo-job/eval_results/jsbsim-1v1-bsl_20250627-104307.txt
[36m(MultiAgentEnvRunner pid=60100)[0m      JSBSim Flight Dynamics Model v1.2.2 Mar 22 2025 11:56:49[32m [repeated 2x across cluster][0m
[36m(MultiAgentEnvRunner pid=60100)[0m jsbsim agents:  {'A0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f962be51c90>, 'B0100': <envs.JSBSim.core.simulatior.AircraftSimulator object at 0x7f962a90c490>}[32m [repeated 2x across cluster][0m
            [JSBSim-ML v2.0]

JSBSim startup beginning ...

