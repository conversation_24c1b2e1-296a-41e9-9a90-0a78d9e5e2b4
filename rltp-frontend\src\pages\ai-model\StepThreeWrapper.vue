<template>
  <WorkflowStepWrapper
    :training-type="TRAINING_TYPES.DEEP_LEARNING"
    :step-number="3"
    :need-save="false"
  >
    <template #default="{ loading, onTrainingSubmit, onTrainingControl, onStepComplete }">
      <StepThree
        @training-submit="(config) => handleTrainingSubmit(config, onTrainingSubmit)"
        @training-control="onTrainingControl"
        @step-complete="onStepComplete"
        @next-step="onStepComplete"
        :loading="loading"
      />
    </template>
  </WorkflowStepWrapper>
</template>

<script setup>
import { ref } from 'vue'
import WorkflowStepWrapper from 'src/components/WorkflowStepWrapper.vue'
import StepThree from './StepThree.vue'
import { TRAINING_TYPES } from 'src/services/workflowApi.js'
import { useModelFormStore } from 'src/stores/modelFormStore.js'

const modelFormStore = useModelFormStore()

/**
 * 处理训练提交事件
 * 将原来的页面传递的配置转换为新的工作流格式
 */
const handleTrainingSubmit = async (config, onTrainingSubmit) => {
  try {
    // config 是从 StepThree.vue 传递过来的训练配置
    // 直接使用这个配置调用工作流的训练提交方法
    await onTrainingSubmit(config)

  } catch (error) {
    console.error('训练提交失败:', error)
  }
}
</script>
