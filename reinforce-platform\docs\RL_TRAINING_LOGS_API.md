# 强化学习训练日志API接口文档

## 📋 概述

此接口用于实时监控强化学习训练过程中的日志信息，采用Server-Sent Events (SSE) 技术提供实时日志流。

## 🔗 接口详情

### 强化学习训练日志实时流 (SSE)

**接口路径**: `/backend/training/rl/{training_id}/log-stream`
**请求方法**: `GET`
**权限要求**: 需要登录认证
**响应类型**: `text/event-stream`

#### 调用示例

```javascript
// 建立SSE连接获取实时训练日志
const eventSource = new EventSource('/backend/training/rl/rl_train_20231201_143022_1234/log-stream');

eventSource.onmessage = function(event) {
    const data = JSON.parse(event.data);

    switch(data.type) {
        case 'connected':
            console.log('日志流连接已建立');
            break;
        case 'log':
            console.log(`${data.content}`);
            // 将日志添加到页面显示
            appendLogToDisplay(data.content);
            break;
        case 'status_change':
            console.log(`训练状态变更: ${data.status}`);
            updateTrainingStatus(data.status);
            break;
        case 'heartbeat':
            console.log('心跳信号');
            break;
        case 'error':
            console.error('日志流错误:', data.message);
            break;
        case 'disconnected':
            console.log('日志流连接已断开');
            break;
    }
};

eventSource.onerror = function(event) {
    console.error('SSE连接错误:', event);
};

// 关闭连接
// eventSource.close();
```

#### SSE事件类型

| 事件类型 | 说明 | 数据格式 |
|----------|------|----------|
| `connected` | 连接建立 | `{"type": "connected", "training_id": "...", "timestamp": 1234567890}` |
| `log` | 新日志内容 | `{"type": "log", "content": "...", "timestamp": 1234567890}` |
| `status_change` | 训练状态变更 | `{"type": "status_change", "status": "completed", "timestamp": 1234567890}` |
| `heartbeat` | 心跳信号 | `{"type": "heartbeat", "timestamp": 1234567890}` |
| `error` | 错误信息 | `{"type": "error", "message": "...", "timestamp": 1234567890}` |
| `disconnected` | 连接断开 | `{"type": "disconnected", "timestamp": 1234567890}` |

## 🚨 错误处理

### 常见错误码

| 错误码 | HTTP状态码 | 说明 | 解决方案 |
|--------|------------|------|----------|
| `TASK_NOT_FOUND` | 404 | 训练任务不存在 | 检查training_id是否正确 |
| `INTERNAL_ERROR` | 500 | 内部服务器错误 | 联系管理员 |

### 错误响应格式

```json
{
    "success": false,
    "error": {
        "code": "TASK_NOT_FOUND",
        "message": "强化学习训练任务 rl_train_20231201_143022_1234 不存在"
    }
}
```

## 💡 使用建议

1. **实时监控**: 使用SSE接口进行实时日志监控
2. **连接管理**: 及时关闭不需要的SSE连接以节省资源
3. **错误处理**: 监听onerror事件处理连接异常

## 🔧 集成示例

### React组件示例

```jsx
import React, { useState, useEffect } from 'react';

const RLTrainingLogs = ({ trainingId }) => {
    const [logs, setLogs] = useState([]);
    const [isConnected, setIsConnected] = useState(false);

    useEffect(() => {
        // 建立SSE连接
        const eventSource = new EventSource(`/backend/training/rl/${trainingId}/log-stream`);

        eventSource.onmessage = (event) => {
            const data = JSON.parse(event.data);

            if (data.type === 'log') {
                setLogs(prev => [...prev, data.content]);
            } else if (data.type === 'connected') {
                setIsConnected(true);
            } else if (data.type === 'disconnected') {
                setIsConnected(false);
            }
        };

        eventSource.onerror = () => {
            setIsConnected(false);
        };

        return () => {
            eventSource.close();
            setIsConnected(false);
        };
    }, [trainingId]);

    return (
        <div>
            <div>连接状态: {isConnected ? '已连接' : '未连接'}</div>
            <div className="log-container">
                {logs.map((log, index) => (
                    <div key={index} className="log-line">{log}</div>
                ))}
            </div>
        </div>
    );
};
```

这个简化的强化学习日志API提供了实时日志监控功能，便于开发者监控强化学习训练过程！
