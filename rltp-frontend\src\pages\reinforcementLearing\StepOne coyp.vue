<template>
  <div class="scenario-container">
    <div class="left-panel">
      <div class="control-section">
        <div class="onetop">
          <img src="../../assets/images/icon_dz.png" />
          <h2>想定选择与控制</h2>
        </div>

        <p class="instruction">请选择一个规定进行投入</p>

        <div class="import-section">
          <div class="bts">
            <q-btn class="import-btn" label="导入想定">
              <img
                src="../../assets/reinforcementImages/icon_drxd.png"
                class="btn-icon"
              />
            </q-btn>
            <q-btn class="connect-btn" label="连接引擎">
              <img
                src="../../assets/reinforcementImages/icon_ljyq.png"
                class="btn-icon"
              />
            </q-btn>
          </div>
        </div>
      </div>

      <div class="available-scenarios">
        <div class="onetop">
          <img src="../../assets/images/icon_dz.png" />
          <h2 class="available-title">可用想定（5）</h2>
        </div>
        <div class="container">
          <div class="scenario-card" v-for="(scenario, index) in scenarios" :key="index">
            <h4 class="scenario-title">{{ scenario.title }}</h4>
            <div class="scenario-items">
              <div class="basic-info">
                <span class="info-left"> {{ scenario.status }}</span>
                <span class="info-right"> {{ scenario.environmenttype }}</span>
              </div>
              <span class="info-text">{{ scenario.info }}</span>
              <div class="detail-info">
                <div class="top">
                  <div class="top-item">
                    <img src="../../assets/reinforcementImages/kp_icon_dz.png" />
                    <p>{{ scenario.detailinfo.detaillocation }}</p>
                  </div>
                  <div class="top-item">
                    <img src="../../assets/reinforcementImages/kp_icon_sj.png" />
                    <p>{{ scenario.detailinfo.detailtime }}</p>
                  </div>
                </div>
                <div class="bottom">
                  <div class="bottom-item">
                    <img src="../../assets/reinforcementImages/kp_icon_wf.png" />
                    <p>{{ scenario.detailinfo.detailour }}</p>
                  </div>
                  <div class="bottom-item">
                    <img src="../../assets/reinforcementImages/kp_icon_df.png" />
                    <p>{{ scenario.detailinfo.detailenemy }}</p>
                  </div>
                </div>
              </div>
              <div class="add-time">{{ scenario.addtime }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="right-panel">
      <div class="scenario-detail">
        <div class="detail-left">
          <div class="top-title">
            <div class="title-left">
              <div class="onetop">
                <img src="../../assets/images/icon_dz.png" />
                <h2>{{ scenariodetail.title }}</h2>
              </div>

              <div class="center-content">
                <span class="one-content">{{ scenariodetail.type }}</span>
                <span class="two-content">{{ scenariodetail.info }}</span>
              </div>
            </div>
            <div class="right-content">
              <span>{{ scenariodetail.status }}</span>
            </div>
          </div>
          <div class="bottom-content">
            <div
              v-for="(item, index) in scenariodetail.detailinfo"
              :key="index"
              class="info-item"
            >
              <img :src="getImageByType(item.type)" />
              <div class="info-text">
                <div class="info-main">{{ item.info }}</div>
                <div class="info-type">{{ item.type }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="detail-right">
          <img src="" class="detail-image" />
        </div>
      </div>

      <div class="bottom-sections">
        <div class="section force-comparison">
          <div class="onetop">
            <img src="../../assets/images/icon_dz.png" />
            <h2>兵力对比</h2>
          </div>
          <div class="force-container">
            <div
              v-for="(force, index) in forceComparison"
              :key="index"
              class="force-info"
            >
              <div class="onetop">
                <img :src="getIconByType(force.type)" />
                <h5
                  :style="{
                    color:
                      force.type === 1 ? 'rgba(232, 38, 38, 1)' : 'rgba(40, 124, 208, 1)',
                  }"
                >
                  {{ getTitleByType(force.type) }}
                </h5>
              </div>
              <div
                v-for="(fleet, fleetIndex) in force.fleets"
                :key="fleetIndex"
                class="fleet-item"
              >
                <div class="fleet-header">
                  <span class="fleet-name">{{ fleet.name }}</span>
                  <div class="fleettypeborder">
                    <span class="fleet-type">{{ fleet.type }}</span>
                  </div>
                </div>
                <p>数量: {{ fleet.count }}</p>
                <p>装备: {{ fleet.equipment }}</p>
              </div>
            </div>
          </div>
        </div>

        <div class="section mission-objective">
          <div class="onetop">
            <img src="../../assets/images/icon_dz.png" />
            <h2>目标任务</h2>
          </div>
          <div>
            <el-steps direction="vertical" align-center class="steps">
              <el-step
                v-for="(mission, index) in missionObjectives"
                :key="index"
                finish-status="success"
              >
                <template #description>
                  <div class="steptitle">{{ mission }}</div>
                </template>
              </el-step>
            </el-steps>
          </div>
        </div>

        <div class="section">
          <div class="right-section">
            <div class="onetop">
              <img src="../../assets/images/icon_dz.png" />
              <h2>详细信息</h2>
            </div>
            <div class="stats-container">
              <h5 class="stats-title">场景统计</h5>
              <div class="stats-info">
                <p>我方总兵力:</p>
                <q-input
                  class="stats-input"
                  v-model.trim="detailedInfo.Ourforces"
                ></q-input>
              </div>
              <div class="stats-info">
                <p>敌方总兵力:</p>
                <q-input
                  class="stats-input"
                  v-model.trim="detailedInfo.Enemyforces"
                ></q-input>
              </div>
              <div class="stats-info">
                <p>兵力比:</p>
                <q-input
                  class="stats-input"
                  v-model.trim="detailedInfo.TroopRatio"
                ></q-input>
              </div>
            </div>
            <div class="stats-container">
              <h5 class="stats-title">场景统计</h5>
              <div class="stats-info">
                <p>创建时间:</p>
                <q-input
                  class="stats-input"
                  v-model.trim="detailedInfo.AddTime"
                ></q-input>
              </div>
              <div class="stats-info">
                <p>创建ID:</p>
                <q-input
                  class="stats-input"
                  v-model.trim="detailedInfo.SceneID"
                ></q-input>
              </div>
            </div>
          </div>
          <div class="next">
            <q-btn
              v-if="false"
              class="prevBtn roundBox"
              color="grey-7"
              label="上一步"
              @click="prevStep"
            />
            <q-btn
              class="nextBtn roundBox"
              color="primary"
              label="下一步"
              @click="nextStep"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineEmits, watch } from "vue";
import { useStepOneStore } from "../../stores/reinforcementLearingStore";
import { api } from 'boot/axios'

// 定义事件
const emit = defineEmits(["next-step", "prev-step"]);
const stepOneStore = useStepOneStore();

const scenarios = ref([
  {
    title: "城市反恐作战",
    status: "困难",
    environmenttype: "城市环境",
    info: "在复杂城市环境中执行反恐任务，需要精确打击并保护平民。",
    detailinfo: {
      detaillocation: "高楼密集区",
      detailtime: "2小时",
      detailour: "我方：14人",
      detailenemy: "敌方：200人",
    },
    addtime: "2025 /01 /01",
  },
  {
    title: "空中拦截作战",
    status: "困难",
    environmenttype: "空中环境",
    info: "执行空中拦截任务，阻止敌机侵入领空",
    detailinfo: {
      detaillocation: "高楼密集区",
      detailtime: "2小时",
      detailour: "我方：14人",
      detailenemy: "敌方：200人",
    },
    addtime: "2025 /01 /01",
  },
  {
    title: "空中拦截作战",
    status: "困难",
    environmenttype: "空中环境",
    info: "执行空中拦截任务，阻止敌机侵入领空",
    detailinfo: {
      detaillocation: "高楼密集区",
      detailtime: "2小时",
      detailour: "我方：14人",
      detailenemy: "敌方：200人",
    },
    addtime: "2025 /01 /01",
  },
]);
const scenariodetail = ref({
  title: "想定详情",
  status: "困难",
  type: "空中拦截作战",
  info: "执行空中拦截任务，阻止敌机侵入领空。",
  detailinfo: [
    {
      type: "环境",
      info: "空中环境",
    },
    {
      type: "地形",
      info: "高空地形",
    },
    {
      type: "天气",
      info: "晴天",
    },
    {
      type: "持续时间",
      info: "1 小时",
    },
  ],
});
const forceComparison = ref([
  {
    type: 0,
    fleets: [
      {
        name: "护卫舰队",
        type: "海军舰队",
        count: 3,
        equipment: "舰炮/导弹系统/ 雷达",
      },
      {
        name: "直升机分队",
        type: "航空兵",
        count: 3,
        equipment: "武装直升机/侦擦 设备",
      },
    ],
  },
  {
    type: 1,
    fleets: [
      {
        name: "海盗快艇",
        type: "快速攻击艇",
        count: 8,
        equipment: "轻武器/快艇/通 讯设备",
      },
    ],
  },
]);

const missionObjectives = ref(["保护南路", "距离海盗", "维护航道安全"]);

const detailedInfo = ref({
  Ourforces: "4",
  Enemyforces: "6",
  TroopRatio: "0.7:1",
  AddTime: "2025/01/20",
  SceneID: "92.0%",
});
// 下一步按钮点击事件
function nextStep() {
  // 保存数据到store
  stepOneStore.updateStepOneData({
    scenarios: scenarios.value,
    scenariodetail: scenariodetail.value,
    forceComparison: forceComparison.value,
    missionObjectives: missionObjectives.value,
    detailedInfo: detailedInfo.value,
  });
  console.log("stepOneData", stepOneStore.stepOneData);

  emit("next-step");
}

// 上一步按钮点击事件
function prevStep() {
  emit("prev-step");
}

function getImageByType(type) {
  switch (type) {
    case "环境":
      return "../../src/assets/reinforcementImages/xq_icon_hj.png";
    case "地形":
      return "../../src/assets/reinforcementImages/xq_icon_dx.png";
    case "天气":
      return "../../src/assets/reinforcementImages/xq_icon_tq.png";
    case "持续时间":
      return "../../src/assets/reinforcementImages/xq_icon_cxsj.png";
    default:
      return "";
  }
}

function getIconByType(type) {
  if (type === 0) {
    return "../../src/assets/reinforcementImages/icon_wfbl.png";
  } else if (type === 1) {
    return "../../src/assets/reinforcementImages/icon_dfbl.png";
  }
  return "";
}

function getTitleByType(type) {
  if (type === 0) {
    return "我方兵力";
  } else if (type === 1) {
    return "敌方兵力";
  }
  return "";
}
</script>

<style lang="scss" scoped>
.scenario-container {
  display: flex;
  color: #333;
  // height: 100vh;

  .left-panel {
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    padding-right: 0.125rem;
    height: 9.875rem;

    .control-section {
      width: 5rem;
      margin-bottom: 0.25rem;
      border: 0.0125rem solid rgba(112, 112, 112, 1);
      padding: 0 0.25rem;
      background-color: #181a24;
      background-image: repeating-linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.05) 0px,
        rgba(255, 255, 255, 0.01) 4px,
        transparent 1px,
        transparent 15px
      );

      .instruction {
        color: rgba(206, 163, 69, 1);
        font-size: 0.25rem;
      }

      .import-section {
        .bts {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 0.25rem;
          gap: 0.125rem;
          .import-btn {
            width: 2rem;
            height: 0.125rem;
            background: #164c82;
            border-radius: 0.075rem;
            color: #ffffff;
            font-size: 0.275rem;
            text-align: center;
            position: relative;
            .btn-icon {
              position: absolute;
              margin-left: 0.125rem;
              left: 0;
            }
          }

          .connect-btn {
            width: 2rem;
            height: 0.125rem;
            background: rgba(225, 225, 225, 0.1);
            border-radius: 0.075rem;
            color: #ffffff;
            font-size: 0.275rem;
            text-align: center;
            .btn-icon {
              position: absolute;
              margin-left: 0.125rem;
              left: 0;
            }
          }
        }
      }
    }
    .control-section {
      margin-left: 0.125rem;
      position: relative;

      &::before {
        position: absolute;
        content: "";
        left: -0.1rem;
        top: 0;
        width: 0.025rem;
        height: 100%;
        background: rgba(156, 172, 198);
        border-radius: 0.125rem;
      }

      &::after {
        position: absolute;
        content: "";
        left: -0.075rem;
        top: 0;
        width: 0.025rem;
        height: 100%;
        background: rgba(156, 172, 198, 0.5);
      }
    }
    .available-scenarios {
      width: 5rem;
      flex-grow: 1;
      height: 4.625rem;
      border: 0.0125rem solid rgba(112, 112, 112, 1);
      padding: 0 0.25rem;
      background-color: #181a24;
      background-image: repeating-linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.05) 0px,
        rgba(255, 255, 255, 0.01) 4px,
        transparent 1px,
        transparent 15px
      );
      .container {
        height: calc(100% - 0.65rem);
        overflow-y: auto;
        scrollbar-width: none;
        &::-webkit-scrollbar {
          display: none;
        }
        .scenario-card {
          border: 0.0125rem solid rgba(112, 112, 112, 1);
          border-radius: 0.0625rem;
          transition: all 0.3s ease;
          margin-bottom: .125rem;
          padding: 0.125rem;
          .scenario-title {
            margin: 0.125rem 0.0625rem 0.1875rem;
            height: 0.1875rem;
            line-height: 0.1875rem;
            color: #ffffff;
            font-size: 0.275rem;
          }

          .scenario-items {
            .basic-info {
              margin-bottom: 0.125rem;

              .info-left {
                border: 0.0125rem solid #c02c2c;
                border-radius: 0.2rem;
                width: 0.875rem;
                height: 0.4rem;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                padding: 0 0.125rem;
                color: #e82626;
                // opacity: 60%;
                margin-right: 0.125rem;
                font-size: 0.225rem;
                background: rgba(192, 44, 44, 0.3);
              }

              .info-right {
                width: 1.25rem;
                height: 0.4rem;
                border: 0.0125rem solid;
                border-color: #ffffff;
                border-radius: 0.2rem;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                padding: 0 0.125rem;
                // opacity: 80%;
                color: #ffffff;
                font-size: 0.225rem;
                background: rgba(225, 225, 225, 0.2);
              }
            }

            .info-text {
              display: block;
              flex: wrap;
              margin-bottom: 0.125rem;
              color: #9cacc6;
              font-size: 0.225rem;
            }

            .detail-info {
              background-color: rgba(225, 225, 225, 0.1);
              margin-bottom: 0.125rem;
              border-radius: 0.1rem;
              padding: 0.125rem;
              width: 4.2rem;
              .top,
              .bottom {
                display: flex;
                justify-content: flex-start;
                .top-item,
                .bottom-item {
                  display: flex;
                  align-items: center;
                  margin-left: 0.3rem;
                }
              }
              p {
                height: 0.25rem;
                line-height: 0.4375rem;
                color: #ffffff;
                font-size: 0.225rem;
              }
            }

            .add-time {
              color: #9cacc6;
              font-size: 0.1625rem;
            }
          }
        }
      }
    }
    .available-scenarios {
      margin-left: 0.125rem;
      position: relative;

      &::before {
        position: absolute;
        content: "";
        left: -0.1rem;
        top: 0;
        width: 0.025rem;
        height: 100%;
        background: rgba(156, 172, 198);
        border-radius: 0.125rem;
      }

      &::after {
        position: absolute;
        content: "";
        left: -0.075rem;
        top: 0;
        width: 0.025rem;
        height: 100%;
        background: rgba(156, 172, 198, 0.5);
      }
    }
  }

  .right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-left: 0.0625rem;
    height: 9.875rem;

    .scenario-detail {
      border: 0.0125rem solid rgba(112, 112, 112, 1);
      margin-bottom: 0.25rem;
      height: 3.25rem;
      padding: 0 0.25rem 0 0.25rem;
      display: flex;
      align-items: stretch;
      background-color: #181a24;
      background-image: repeating-linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.05) 0px,
        rgba(255, 255, 255, 0.01) 4px,
        transparent 1px,
        transparent 15px
      );
      .detail-left {
        flex: 1;
      }

      .top-title {
        display: flex;
        height: 2.125rem;
        .title-left {
          .center-content {
            display: flex;
            flex-direction: column;
            margin-left: 0.375rem;

            .one-content {
              height: 0.4625rem;
              line-height: 0.6125rem;
              color: #ffffff;
              font-size: 0.35rem;
              margin-bottom: 0.25rem;
            }

            .two-content {
              height: 1.25rem;
              line-height: 0.325rem;
              color: #cea345;
              font-size: 0.3rem;
            }
          }
        }

        .right-content {
          margin: auto;
          width: 1.25rem;
          height: 0.55rem;
          border: 0.0125rem solid;
          border-color: #c02c2c;
          border-radius: 0.275rem;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(192, 44, 44, 0.3);

          span {
            font-size: 0.225rem;
            height: 0.55rem;
            line-height: 0.55rem;
            text-align: center;
            color: #c02c2c;
          }
        }
      }

      .bottom-content {
        display: flex;
        gap: 0.25rem;
        .info-item {
          display: flex;
          align-items: center;
          flex: 1;
          img {
            margin-right: 0.125rem;
          }

          .info-text {
            text-align: center;
            .info-main {
              height: 0.3875rem;
              line-height: 0.525rem;
              color: #ffffff;
              font-size: 0.3rem;
            }

            .info-type {
              height: 0.325rem;
              line-height: 0.4375rem;
              color: #9cacc6;
              font-size: 0.25rem;
            }
          }
          .info-text {
            position: relative;

            &::before {
              position: absolute;
              content: "";
              top: 0.125rem;
              right: -0.4225rem;
              width: 0.0125rem;
              height: 100%;
              background: rgba(255, 255, 255, 1);
            }
          }
        }
      }
      .detail-right {
        margin: auto;
        width: 4.375rem;
        height: 2.5rem;

        .detail-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      .detail-right {
        margin-left: 0.125rem;
        position: relative;

        &::before {
          position: absolute;
          content: "";
          left: -0.25rem;
          top: 0;
          width: 0.0125rem;
          height: 100%;
          background: rgba(255, 255, 255, 1);
        }
      }
    }
    .bottom-sections {
      display: flex;
      height: 6.375rem;
      gap: 0.25rem;
      background-color: #181a24;
      background-image: repeating-linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.05) 0px,
        rgba(255, 255, 255, 0.01) 4px,
        transparent 1px,
        transparent 15px
      );
      .section {
        border: 0.0125rem solid rgba(112, 112, 112, 1);
        border-radius: 0.0625rem;
        padding: 0 0.1875rem;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        flex: 1;

        .steps {
          padding: 0.125rem;
          margin-bottom: 1.25rem;
          :deep(.el-step__icon) {
            background: rgba(51, 106, 150, 1);
            width: 0.45rem;
            height: 0.45rem;
            margin-left: -0.125rem;
          }
          :deep(.el-step__icon-inner) {
            color: rgba(74, 180, 255, 1);
            font-size: 0.225rem;
          }
          .steptitle {
            color: #4ab4ff;
            font-size: 0.225rem;
            margin: 0.125rem 0 0 0.1875rem;
          }
        }

        .right-section {
          height: 5.5rem;
          .stats-container {
            height: 1.25rem;
            line-height: 0.325rem;
            color: #cea345;
            font-size: 0.3rem;
            margin-bottom: 1rem;
            .stats-info {
              margin-bottom: 0.125rem;
              display: flex;
              p {
                margin-right: 0.125rem;
                height: 0.325rem;
                line-height: 0.4375rem;
                color: #ffffff;
                font-size: 0.2rem;
                text-align: right;
                width: 1.25rem;
              }

              .stats-input {
                width: 2rem;
                height: 0.45rem;
              }
            }
          }

          .stats-title {
            margin: 0 0.1875rem;
            height: 0.325rem;
            line-height: 0.325rem;
            color: #cea345;
            font-size: 0.225rem;
          }
        }
        .next {
          display: flex;
          gap: 0.25rem;
          // margin-bottom: .375rem;
          .prevBtn {
            margin-right: auto;
          }

          .nextBtn {
            margin-left: auto;
          }
        }
      }

      .force-comparison {
        flex: 2;
        display: flex;
        flex-direction: column;
        p {
          height: 0.325rem;
          line-height: 0.4375rem;
          color: #ffffff;
          font-size: 0.25rem;
          margin-bottom: 0.0625rem;
        }
        .force-container {
          flex: 1;
          display: flex;
          gap: 0.25rem;
          padding: 0.125rem;

          .force-info {
            border: 0.0125rem solid #ccc;
            padding: 0.125rem;
            display: flex;
            flex-direction: column;
            flex: 1;
            border-radius: 0.125rem;
            background: rgba(0, 252, 255, 0.1);
            .onetop {
              img {
                width: 0.2476rem;
                height: 0.3011rem;
                margin: 0;
              }
              h5 {
                height: 0.3625rem;
                line-height: 0.3625rem;
                font-size: 0.275rem;
                margin: 0.125rem;
              }
            }

            .fleet-item {
              border-top: 0.0125rem solid rgba(0, 252, 255, 1);
              display: flex;
              flex-direction: column;
              margin-bottom: 0.0625rem;
              padding: 0.125rem;
              .fleet-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 0.0625rem;
                .fleet-name {
                  height: 0.325rem;
                  line-height: 0.4375rem;
                  color: #cea345;
                  font-size: 0.25rem;
                }
                .fleettypeborder {
                  background: rgba(225, 225, 225, 0.1);
                  border-radius: 0.175rem;
                  width: 1.25rem;
                  height: 0.35rem;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  .fleet-type {
                    height: 0.35rem;
                    line-height: 0.35rem;
                    font-size: 0.225rem;
                    color: white;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  .right-panel {
    position: relative;

    &::before {
      position: absolute;
      content: "";
      left: -0.1rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198);
      border-radius: 0.125rem;
    }

    &::after {
      position: absolute;
      content: "";
      left: -0.075rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198, 0.5);
    }
  }
  .onetop {
    display: flex;
    align-items: center;
    h2 {
      height: 0.1875rem;
      line-height: 0.1875rem;
      color: #ffffff;
      font-size: 0.275rem;
    }
    img {
      width: 0.2rem;
      height: 0.2rem;
      margin-right: 0.1875rem;
    }
  }
}
</style>
