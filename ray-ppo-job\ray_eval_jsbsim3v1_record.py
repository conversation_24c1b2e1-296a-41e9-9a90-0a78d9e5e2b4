import argparse
import ray
from ray.rllib.algorithms.ppo import PPO
from ray.rllib.algorithms.ppo import PPOConfig
from ray.tune.registry import register_env
import torch
from envs.JSBSim.envs.multiplecombat_rllib_env import MultipleCombatEnv
from envs.JSBSim.envs.singlecombat_env import SingleCombatEnv
import os
import argparse
from ray import tune
import time

def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--checkpoint-path", type=str, default="",
                      help="检查点路径，如果不指定则使用随机动作")
    parser.add_argument("--env-name", type=str, default="3v1/ShootMissile/HierarchyVsBaseline",
                      help="环境名称")
    parser.add_argument("--num-episodes", type=int, default=1,
                      help="评估轮数")
    parser.add_argument("--num-env-runners", type=int, default=1,
                      help="训练时的环境运行器数量")
    parser.add_argument("--num-gpus", type=int, default=1,
                      help="GPU数量")
    parser.add_argument("--output-dir", type=str, default="./eval_results",
                      help="评估结果和回放文件保存目录")
    parser.add_argument("--env-registry-name", type=str, default="JSBSim-Combat",
                      help="Ray注册的环境名称")
    parser.add_argument("--address", type=str, default="auto",
                      help="Ray集群地址")
    parser.add_argument("--num-workers", type=int, default=1,
                      help="评估worker数量")
    parser.add_argument("--experiment-name", type=str, default="jsbsim_combat_vs_baseline",
                      help="实验名称，用于保存结果和日志")
    return parser.parse_args()


def env_creator(env_config):
    # 使用传入的环境名称
    env = MultipleCombatEnv(env_config["env_name"])
    # env.seed(5)
    return env


def get_config(args):
    config = (
        PPOConfig()
        .environment(args.env_registry_name, env_config={"env_name": args.env_name})
        .framework("torch")
        .resources(num_gpus=args.num_gpus)
        .env_runners(
            num_env_runners=args.num_env_runners,
            rollout_fragment_length='auto',
            num_envs_per_env_runner=1,
            num_cpus_per_env_runner=1,
            num_gpus_per_env_runner=0,
        )
        .learners(
            num_learners=1,
            num_cpus_per_learner=1,
            num_gpus_per_learner=0
        )
        .training(
            gamma=0.99,
            lr=5e-5,
            train_batch_size=16384,
            lambda_=0.95,
            clip_param=0.2,
            vf_clip_param=10.0,
            entropy_coeff=0.01,
            num_sgd_iter=10,
            minibatch_size=2048,
            model={
                # 中央评论家网络配置
                "fcnet_hiddens": [512, 512, 256],
                "fcnet_activation": "relu",
                "vf_share_layers": False,  # 分离价值网络和策略网络
                "use_lstm": True,
                "lstm_cell_size": 256,
                "max_seq_len": 32,
            },
        )
        .multi_agent(
            policies=["red_policy", "blue_policy"],
            policy_mapping_fn=lambda agent_id, *args, **kwargs: (
                "red_policy" if agent_id.startswith("A") else "blue_policy"
            ),
            policies_to_train=["red_policy"],  # 只训练红方策略
            # observation_fn=None,  # 使用默认观察函数
        )
        .evaluation(
            evaluation_interval=10,
            evaluation_duration=20,
            evaluation_num_workers=1,
            evaluation_config={
                "render_mode": "txt",
                "explore": False,
                "env_config": {
                    "env_name": args.env_name,
                },
            },
        )
    )
    
    # 清空探索配置
    config.exploration_config = {}
    return config


def evaluate_model(args):
    # 初始化Ray
    ray.init()

    # 注册环境
    register_env(args.env_registry_name, env_creator)

    if args.checkpoint_path is not None and args.checkpoint_path != "":
        checkpoint_path = os.path.abspath(args.checkpoint_path)
        if not os.path.exists(checkpoint_path):
            print(f"错误：检查点不存在: {checkpoint_path}")
            return
        print(f"使用检查点: {checkpoint_path}")
        # 创建PPO算法实例并加载检查点
        algo = PPO(config=get_config(args))
        algo.restore(checkpoint_path)
    else:
        print("未指定检查点，直接进行环境评估")
        algo = PPO(config=get_config(args))
    
    # 确保输出目录存在
    output_dir = os.path.expanduser(args.output_dir)
    os.makedirs(output_dir, exist_ok=True)

    # 设置回放文件路径
    timestamp = time.strftime("%Y%m%d-%H%M%S")
    # filepath = os.path.join(output_dir, f"JSBSimRecording_{timestamp}.txt.acmi")
    filepath = os.path.join(output_dir, f"{args.experiment_name}_{timestamp}.acmi")
    
    # 运行评估
    env = env_creator({"env_name": args.env_name})
    # print("action_space: ", env.action_space)
    total_reward = 0
    
    for episode in range(args.num_episodes):
        obs, _ = env.reset()
        done = False
        truncated = False
        episode_rewards = {agent_id: 0 for agent_id in obs.keys()}
        episode_length = 0

        while not done and not truncated:
            # 为每个智能体计算动作
            # print("obs: ", obs)
            actions = {}
            for agent_id, agent_obs in obs.items():
                # 检查智能体是否已经终止
                if agent_obs is None or agent_id.startswith("B"):
                    continue  # 跳过已终止的智能体
                
                # 根据智能体ID选择对应的策略
                policy_id = "red_policy" if agent_id.startswith("A") else "blue_policy"
                
                torch_obs_batch = torch.from_numpy(agent_obs).float()
                if len(torch_obs_batch.shape) == 1:
                    torch_obs_batch = torch_obs_batch.unsqueeze(0)
                
                # 使用对应策略获取动作
                action_logits = algo.get_module(policy_id).forward_inference({"obs": torch_obs_batch})["action_dist_inputs"]
                
                # 处理 MultiDiscrete 动作
                # multi_discrete_logits = action_logits[0][:11]  # 3 + 5 + 3 = 11
                multi_discrete_logits = action_logits[0]
                # action_splits = torch.split(multi_discrete_logits, [3, 5, 3])
                action_dim = env.action_spaces[agent_id].nvec.tolist()
                action_splits = torch.split(multi_discrete_logits, action_dim)
                multi_discrete_action = [torch.argmax(split).item() for split in action_splits]
                
                actions[agent_id] = multi_discrete_action
            
            # 执行环境步进
            obs, rewards, dones, truncateds, _ = env.step(actions)

            # print(f"actions: {actions}")
            
            # 更新奖励和回合状态
            for agent_id, reward in rewards.items():
                if reward is not None:  # 添加检查确保奖励不是 None
                    episode_rewards[agent_id] += reward
                else:
                    episode_rewards[agent_id] += 0.0  # 如果是 None，则加 0
            
            # 检查是否所有智能体都完成
            done = all(dones.values()) if dones else False
            truncated = all(truncateds.values()) if truncateds else False
            
            # 渲染环境
            env.render(filepath=filepath)
            episode_length += 1

        # for index, reward in enumerate(env.jsb_multicombat_env.task.reward_functions):
        #     if index == 0:
        #         print(f"{reward.__class__.__name__}: {reward.reward_trajectory['A0300']}")

        # 打印每个智能体的奖励
        print(f"\nEpisode {episode} completed:")
        print(f"Episode Length: {episode_length}")
        for agent_id, reward in episode_rewards.items():
            print(f"Agent {agent_id} Reward: {reward}")
        for agent_id, agent in env.jsb_multicombat_env.agents.items():
            status = 'alive' if agent.is_alive else 'dead'
            print(f"Agent {agent_id} status: {status}")
        print("------------------------")

    avg_reward = total_reward / args.num_episodes
    print(f"Average reward over {args.num_episodes} episodes: {avg_reward}")
    print(f"回放文件已保存到: {filepath}")
    
    # 将评估结果保存到文件
    # results_file = os.path.join(output_dir, f"eval_results_{timestamp}.txt")
    results_file = os.path.join(output_dir, f"{args.experiment_name}_{timestamp}.txt")
    with open(results_file, "w") as f:
        f.write(f"环境: {args.env_name}\n")
        f.write(f"检查点: {args.checkpoint_path}\n")
        f.write(f"评估轮数: {args.num_episodes}\n")
        f.write(f"平均奖励: {avg_reward}\n")
    
    print(f"评估结果已保存到: {results_file}")

    # 清理
    env.close()
    if algo is not None:
        algo.stop()
    # ray.shutdown()

if __name__ == "__main__":
    args = parse_args()
    evaluate_model(args)
