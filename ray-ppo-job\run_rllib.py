import ray
from ray import tune
from ray.tune.registry import register_env
import gymnasium as gym
from ray.rllib.algorithms.ppo import PPOConfig

def env_creator(env_config):
    env = gym.make("ALE/Breakout-v5", render_mode=None)
    return env

register_env("Breakout", env_creator)

ray.init()

config = (PPOConfig()
    .environment("Breakout")
    .framework("torch")
    .resources(num_gpus=1)
    .rollouts(num_rollout_workers=4)
    .training(
        gamma=0.99,
        lr=3e-4,
        train_batch_size=4000,
        lambda_=0.95,
        clip_param=0.2,
        vf_clip_param=10.0,
        entropy_coeff=0.01,
        num_sgd_iter=10,
        sgd_minibatch_size=128,
    )
    .evaluation(
        evaluation_interval=10,
        evaluation_duration=10,
    )
)

tune.run(
    "PPO",
    config=config,
    stop={
        "training_iteration": 10000,
        "episode_reward_mean": 200,
    },
    checkpoint_freq=100,
    checkpoint_at_end=True,
    local_dir="./ray_results",
)