# 统一训练工作流API接口文档

## 概述

统一训练工作流系统支持三种类型的训练任务：
- **深度学习** (deep_learning): 数据加载分割 → 训练参数配备 → 提交模型训练 → 训练结果评估 → 模型导出存储
- **强化学习** (reinforcement_learning): 交互环境接入 → 训练参数配置 → 提交模型训练 → 训练结果评估 → 模型导出存储  
- **大模型** (large_model): 数据加载分割 → 训练参数配备 → 提交微调训练 → 训练结果评估 → 模型量化剪枝 → 模型部署推理

## 基础信息

**Base URL:** `/backend/`
**认证方式:** Bearer Token
**Content-Type:** `application/json`

## 接口列表

### 1. 创建训练工作流

**接口地址:** `POST /workflows/create/`
**功能描述:** 创建新的训练工作流任务
**使用场景:** 用户点击"开始训练"按钮时调用

#### 请求参数
```json
{
  "name": "训练任务名称",
  "description": "任务描述",
  "training_type": "deep_learning",
  "task_type": "object_detection_yolov8",
  "model_name": "模型名称"
}
```

#### 参数说明
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| name | string | 是 | 训练任务名称 |
| description | string | 否 | 任务描述 |
| training_type | string | 是 | 训练类型：deep_learning/reinforcement_learning/large_model |
| task_type | string | 是 | 具体任务类型 |
| model_name | string | 否 | 模型名称，默认使用任务名称 |

#### 响应示例
```json
{
  "success": true,
  "message": "训练任务创建成功",
  "task_id": "550e8400-e29b-41d4-a716-************",
  "workflow_id": 123,
  "model_info_id": 456,
  "training_type": "deep_learning",
  "current_step": 1,
  "status": "draft"
}
```

### 2. 跳转到指定步骤

**接口地址:** `POST /workflows/jump/`
**功能描述:** 从任务列表跳转到指定任务的当前步骤
**使用场景:** 用户点击右侧任务列表中的任务时调用

#### 请求参数
```json
{
  "task_id": "550e8400-e29b-41d4-a716-************"
}
```

#### 参数说明
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| task_id | string | 是 | 任务ID (UUID格式) |

#### 响应示例
```json
{
  "success": true,
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-************",
    "workflow_id": 123,
    "name": "训练任务名称",
    "training_type": "deep_learning",
    "task_type": "object_detection_yolov8",
    "current_step": 2,
    "target_step": 2,
    "target_page": "step2",
    "step_name": "训练参数配备",
    "step_data": {
      "epochs": 100,
      "batch_size": 32
    },
    "status": "step1_saved",
    "training_status": "not_started",
    "progress_percentage": 40,
    "can_proceed": true,
    "next_step_number": 3,
    "training_data": {}
  }
}
```

### 3. 保存步骤数据

**接口地址:** `POST /workflows/step/save/`
**功能描述:** 保存前两步的配置数据并进入下一步
**使用场景:** 第一步和第二步点击"下一步"按钮时调用

#### 请求参数
```json
{
  "task_id": "550e8400-e29b-41d4-a716-************",
  "step_number": 1,
  "step_data": {
    "dataset_id": "dataset-123",
    "dataset_name": "数据集名称",
    "train_percent": 70,
    "validation_percent": 20,
    "test_percent": 10
  }
}
```

#### 参数说明
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| task_id | string | 是 | 任务ID |
| step_number | integer | 是 | 步骤号 (1或2) |
| step_data | object | 是 | 步骤配置数据 |

#### 响应示例
```json
{
  "success": true,
  "message": "步骤1数据保存成功",
  "current_step": 2,
  "status": "step1_saved",
  "next_step_number": 3
}
```

### 4. 开始数据分析

**接口地址:** `POST /workflows/data/analysis/`
**功能描述:** 开始数据分析处理
**使用场景:** 第一步点击"开始分析"按钮时调用

#### 请求参数
```json
{
  "task_id": "550e8400-e29b-41d4-a716-************",
  "dataset_config": {
    "dataset_id": "dataset-123",
    "analysis_type": "basic"
  }
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "数据分析已开始",
  "analysis_result": {
    "total_samples": 10000,
    "class_distribution": {
      "class1": 5000,
      "class2": 3000,
      "class3": 2000
    }
  }
}
```

### 5. 提交模型训练

**接口地址:** `POST /workflows/training/submit/`
**功能描述:** 提交模型训练任务
**使用场景:** 第三步点击"开始训练"按钮时调用

#### 请求参数
```json
{
  "task_id": "550e8400-e29b-41d4-a716-************",
  "training_config": {
    "epochs": 100,
    "batch_size": 32,
    "learning_rate": 0.001,
    "optimizer": "adam"
  }
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "训练任务已提交",
  "training_task_id": "training-uuid-123",
  "status": "training",
  "training_status": "running"
}
```

### 6. 训练控制

**接口地址:** `POST /workflows/training/control/`
**功能描述:** 控制训练任务的执行状态
**使用场景:** 第三步的暂停/恢复/停止按钮时调用

#### 请求参数
```json
{
  "task_id": "550e8400-e29b-41d4-a716-************",
  "action": "pause"
}
```

#### 参数说明
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| task_id | string | 是 | 任务ID |
| action | string | 是 | 操作类型：pause/resume/stop |

#### 响应示例
```json
{
  "success": true,
  "message": "训练已暂停",
  "training_status": "paused"
}
```

### 7. 完成步骤

**接口地址:** `POST /workflows/step/complete/`
**功能描述:** 完成当前步骤并进入下一步
**使用场景:** 第三步训练完成后点击"下一步"按钮时调用

#### 请求参数
```json
{
  "task_id": "550e8400-e29b-41d4-a716-************",
  "step_number": 3,
  "final_metrics": {
    "accuracy": 0.95,
    "loss": 0.05,
    "training_time": 3600
  }
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "训练完成，进入评估阶段",
  "current_step": 4,
  "status": "training_completed",
  "training_status": "completed"
}
```

### 8. 获取任务列表

**接口地址:** `GET /workflows/list/`
**功能描述:** 获取用户的训练任务列表
**使用场景:** 加载右侧任务列表时调用

#### 请求参数 (Query Parameters)
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | integer | 否 | 页码，默认1 |
| page_size | integer | 否 | 每页数量，默认10 |
| training_type | string | 否 | 训练类型筛选 |
| status | string | 否 | 状态筛选 |

#### 响应示例
```json
{
  "workflows": [
    {
      "task_id": "550e8400-e29b-41d4-a716-************",
      "name": "深度学习训练任务",
      "training_type": "deep_learning",
      "task_type": "object_detection_yolov8",
      "status": "training",
      "training_status": "running",
      "current_step": 3,
      "step_name": "提交模型训练",
      "progress_percentage": 60,
      "created_at": "2024-01-01T10:00:00Z",
      "updated_at": "2024-01-01T12:00:00Z",
      "status_display": "训练中",
      "training_status_display": "运行中"
    }
  ],
  "total": 50,
  "page": 1,
  "page_size": 10
}
```

### 9. 更新训练指标

**接口地址:** `POST /workflows/metrics/update/`
**功能描述:** 更新训练过程中的指标数据
**使用场景:** 训练过程中实时更新指标时调用

#### 请求参数
```json
{
  "task_id": "550e8400-e29b-41d4-a716-************",
  "metrics": {
    "epoch": 10,
    "loss": 0.25,
    "accuracy": 0.85,
    "learning_rate": 0.001
  },
  "logs": [
    {
      "timestamp": "2024-01-01T12:00:00Z",
      "level": "INFO",
      "message": "Epoch 10 completed"
    }
  ]
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "训练指标更新成功"
}
```

## 状态说明

### 工作流状态 (status)
- `draft`: 草稿状态，刚创建
- `step1_saved`: 第一步已保存
- `step2_saved`: 第二步已保存  
- `training_ready`: 准备开始训练
- `training`: 训练中
- `paused`: 训练暂停
- `training_completed`: 训练完成
- `evaluation_completed`: 评估完成
- `export_completed`: 导出完成
- `completed`: 全部完成

### 训练状态 (training_status)
- `not_started`: 未开始
- `running`: 运行中
- `paused`: 已暂停
- `stopped`: 已停止
- `completed`: 已完成
- `failed`: 训练失败

## 错误码说明

### 通用错误
- `400`: 请求参数错误
- `401`: 未认证或认证失败
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

### 业务错误响应
```json
{
  "success": false,
  "error": "错误描述信息"
}
```

### 常见错误信息
- `训练任务不存在`: 指定的task_id不存在
- `无权限访问该任务`: 用户无权访问该任务
- `当前状态不允许此操作`: 任务状态不允许执行该操作
- `步骤配置不完整`: 缺少必要的配置参数
- `训练任务已在运行`: 重复提交训练任务

## 认证说明

所有接口都需要在请求头中包含认证信息：

```http
Authorization: Bearer <your_access_token>
Content-Type: application/json
```

## 调用示例

### Python 示例
```python
import requests

headers = {
    'Authorization': 'Bearer your_token',
    'Content-Type': 'application/json'
}

# 创建工作流
data = {
    'name': '测试任务',
    'training_type': 'deep_learning',
    'task_type': 'object_detection_yolov8'
}
response = requests.post(
    'http://localhost:8000/backend/workflows/create/',
    json=data,
    headers=headers
)
```

### JavaScript 示例
```javascript
// 使用 axios
const response = await axios.post('/backend/workflows/create/', {
  name: '测试任务',
  training_type: 'deep_learning',
  task_type: 'object_detection_yolov8'
}, {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
})
```

## 注意事项

1. **任务ID格式**: 所有task_id都是UUID格式的字符串
2. **步骤顺序**: 必须按照步骤顺序执行，不能跳步
3. **状态检查**: 执行操作前需要检查当前状态是否允许
4. **数据持久化**: 每个步骤的数据都会持久化保存
5. **并发控制**: 同一任务不能同时执行多个操作
6. **权限控制**: 用户只能操作自己创建的任务
7. **超时处理**: 长时间运行的操作需要考虑超时处理
