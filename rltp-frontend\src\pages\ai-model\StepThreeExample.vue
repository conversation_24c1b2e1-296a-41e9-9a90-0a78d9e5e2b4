<template>
  <WorkflowStepWrapper
    :step-number="3"
    :training-type="TRAINING_TYPES.DEEP_LEARNING"
    :need-save="true"
    @step-data-changed="onStepDataChanged"
    @step-completed="onStepCompleted"
  >
    <template #default="{
      loading,
      onTrainingSubmit,
      onTrainingControl,
      onStepComplete
    }">
      <div class="step-three-content">
        <div class="header">
          <h5 class="q-ma-none">提交模型训练</h5>
          <p class="text-grey-6">配置训练基础信息并开始训练</p>
        </div>

        <!-- 基础信息配置 -->
        <div class="config-section" v-if="!isTrainingStarted">
          <div class="section-title">基础信息配置</div>
          
          <q-form class="q-gutter-md">
            <div class="row q-gutter-md">
              <div class="col-5">
                <q-select
                  v-model="trainingConfig.algorithm_version"
                  :options="algorithmVersionOptions"
                  label="算法版本"
                  outlined
                  dense
                  emit-value
                  map-options
                />
              </div>
              <div class="col-5">
                <q-input
                  v-model="trainingConfig.model_path"
                  label="模型路径"
                  outlined
                  dense
                />
              </div>
            </div>

            <div class="row q-gutter-md">
              <div class="col-5">
                <q-input
                  v-model="trainingConfig.save_dir"
                  label="保存目录"
                  outlined
                  dense
                />
              </div>
              <div class="col-5">
                <q-input
                  v-model="trainingConfig.project_name"
                  label="项目名称"
                  outlined
                  dense
                />
              </div>
            </div>
          </q-form>

          <div class="button-section">
            <q-btn
              color="primary"
              label="开始训练"
              size="lg"
              :loading="loading"
              @click="startTraining(onTrainingSubmit)"
            />
          </div>
        </div>

        <!-- 训练控制和监控 -->
        <div class="training-section" v-if="isTrainingStarted">
          <div class="section-title">训练控制</div>
          
          <!-- 训练状态 -->
          <div class="status-card">
            <q-card flat bordered>
              <q-card-section>
                <div class="row items-center q-gutter-md">
                  <div class="col">
                    <div class="text-h6">训练状态</div>
                    <q-chip
                      :color="getStatusColor(currentTrainingStatus)"
                      text-color="white"
                      size="md"
                    >
                      {{ getStatusDisplay(currentTrainingStatus) }}
                    </q-chip>
                  </div>
                  <div class="col-auto">
                    <div class="training-controls">
                      <q-btn
                        v-if="currentTrainingStatus === 'running'"
                        color="orange"
                        label="暂停"
                        @click="controlTraining('pause', onTrainingControl)"
                        :loading="controlLoading"
                      />
                      <q-btn
                        v-if="currentTrainingStatus === 'paused'"
                        color="green"
                        label="恢复"
                        @click="controlTraining('resume', onTrainingControl)"
                        :loading="controlLoading"
                      />
                      <q-btn
                        v-if="['running', 'paused'].includes(currentTrainingStatus)"
                        color="red"
                        label="停止"
                        @click="controlTraining('stop', onTrainingControl)"
                        :loading="controlLoading"
                        class="q-ml-sm"
                      />
                    </div>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>

          <!-- 训练指标 -->
          <div class="metrics-section">
            <div class="section-title">训练指标</div>
            
            <div class="row q-gutter-md">
              <div class="col-6">
                <q-card flat bordered>
                  <q-card-section>
                    <div class="text-h6">当前进度</div>
                    <div class="q-mt-md">
                      <div class="text-body1">
                        Epoch: {{ currentMetrics.epoch || 0 }} / {{ totalEpochs }}
                      </div>
                      <q-linear-progress
                        :value="(currentMetrics.epoch || 0) / totalEpochs"
                        color="primary"
                        size="12px"
                        class="q-mt-sm"
                      />
                    </div>
                  </q-card-section>
                </q-card>
              </div>
              
              <div class="col-6">
                <q-card flat bordered>
                  <q-card-section>
                    <div class="text-h6">损失值</div>
                    <div class="q-mt-md">
                      <div class="text-body1">
                        训练损失: {{ currentMetrics.train_loss?.toFixed(4) || 'N/A' }}
                      </div>
                      <div class="text-body1">
                        验证损失: {{ currentMetrics.val_loss?.toFixed(4) || 'N/A' }}
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </div>

            <div class="row q-gutter-md q-mt-md">
              <div class="col-6">
                <q-card flat bordered>
                  <q-card-section>
                    <div class="text-h6">准确率</div>
                    <div class="q-mt-md">
                      <div class="text-body1">
                        训练准确率: {{ (currentMetrics.train_accuracy * 100)?.toFixed(2) || 'N/A' }}%
                      </div>
                      <div class="text-body1">
                        验证准确率: {{ (currentMetrics.val_accuracy * 100)?.toFixed(2) || 'N/A' }}%
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
              </div>
              
              <div class="col-6">
                <q-card flat bordered>
                  <q-card-section>
                    <div class="text-h6">其他指标</div>
                    <div class="q-mt-md">
                      <div class="text-body1">
                        学习率: {{ currentMetrics.learning_rate?.toFixed(6) || 'N/A' }}
                      </div>
                      <div class="text-body1">
                        GPU内存: {{ currentMetrics.gpu_memory || 'N/A' }}
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </div>
          </div>

          <!-- 训练日志 -->
          <div class="logs-section">
            <div class="section-title">训练日志</div>
            
            <q-card flat bordered>
              <q-card-section>
                <div class="logs-container">
                  <div
                    v-for="(log, index) in recentLogs"
                    :key="index"
                    class="log-entry"
                  >
                    <span class="log-time">{{ formatLogTime(log.timestamp) }}</span>
                    <span class="log-content">{{ log.content }}</span>
                  </div>
                  <div v-if="recentLogs.length === 0" class="text-grey-6">
                    暂无日志信息
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>

          <!-- 下一步按钮 (训练完成后显示) -->
          <div class="button-section" v-if="currentTrainingStatus === 'completed'">
            <q-btn
              color="primary"
              label="下一步"
              size="lg"
              @click="completeStep(onStepComplete)"
            />
          </div>
        </div>
      </div>
    </template>
  </WorkflowStepWrapper>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useWorkflowStore } from 'src/stores/workflowStore'
import { TRAINING_TYPES, TRAINING_STATUS } from 'src/services/workflowApi'
import WorkflowStepWrapper from 'src/components/WorkflowStepWrapper.vue'
import { date } from 'quasar'

const workflowStore = useWorkflowStore()

// 响应式数据
const controlLoading = ref(false)
const metricsUpdateTimer = ref(null)

// 训练配置
const trainingConfig = ref({
  algorithm_version: 'v8',
  model_path: 'yolov8n.pt',
  save_dir: './runs/train',
  project_name: 'training_project'
})

// 算法版本选项
const algorithmVersionOptions = [
  { label: 'YOLOv8n', value: 'v8n' },
  { label: 'YOLOv8s', value: 'v8s' },
  { label: 'YOLOv8m', value: 'v8m' },
  { label: 'YOLOv8l', value: 'v8l' },
  { label: 'YOLOv8x', value: 'v8x' }
]

// 计算属性
const isTrainingStarted = computed(() => {
  return workflowStore.trainingStatus !== 'not_started'
})

const currentTrainingStatus = computed(() => {
  return workflowStore.trainingStatus || 'not_started'
})

const currentMetrics = computed(() => {
  return workflowStore.trainingMetrics || {}
})

const recentLogs = computed(() => {
  return workflowStore.trainingLogs.slice(-10) || []
})

const totalEpochs = computed(() => {
  return workflowStore.currentWorkflow?.step2_config?.epochs || 100
})

/**
 * 开始训练
 */
const startTraining = async (onTrainingSubmit) => {
  try {
    await onTrainingSubmit(trainingConfig.value)
    
    // 开始模拟训练指标更新
    startMetricsSimulation()
    
  } catch (error) {
    console.error('开始训练失败:', error)
  }
}

/**
 * 训练控制
 */
const controlTraining = async (action, onTrainingControl) => {
  try {
    controlLoading.value = true
    await onTrainingControl(action)
    
    // 根据操作更新模拟状态
    if (action === 'pause') {
      stopMetricsSimulation()
    } else if (action === 'resume') {
      startMetricsSimulation()
    } else if (action === 'stop') {
      stopMetricsSimulation()
    }
    
  } catch (error) {
    console.error('训练控制失败:', error)
  } finally {
    controlLoading.value = false
  }
}

/**
 * 完成步骤
 */
const completeStep = async (onStepComplete) => {
  try {
    const finalMetrics = {
      final_train_loss: currentMetrics.value.train_loss,
      final_val_loss: currentMetrics.value.val_loss,
      final_train_accuracy: currentMetrics.value.train_accuracy,
      final_val_accuracy: currentMetrics.value.val_accuracy,
      total_epochs: currentMetrics.value.epoch,
      training_completed_at: new Date().toISOString()
    }
    
    await onStepComplete(finalMetrics)
    
  } catch (error) {
    console.error('完成步骤失败:', error)
  }
}

/**
 * 获取状态颜色
 */
const getStatusColor = (status) => {
  const colorMap = {
    [TRAINING_STATUS.NOT_STARTED]: 'grey',
    [TRAINING_STATUS.RUNNING]: 'green',
    [TRAINING_STATUS.PAUSED]: 'orange',
    [TRAINING_STATUS.STOPPED]: 'red',
    [TRAINING_STATUS.COMPLETED]: 'positive',
    [TRAINING_STATUS.FAILED]: 'negative'
  }
  return colorMap[status] || 'grey'
}

/**
 * 获取状态显示文本
 */
const getStatusDisplay = (status) => {
  const displayMap = {
    [TRAINING_STATUS.NOT_STARTED]: '未开始',
    [TRAINING_STATUS.RUNNING]: '运行中',
    [TRAINING_STATUS.PAUSED]: '已暂停',
    [TRAINING_STATUS.STOPPED]: '已停止',
    [TRAINING_STATUS.COMPLETED]: '已完成',
    [TRAINING_STATUS.FAILED]: '训练失败'
  }
  return displayMap[status] || status
}

/**
 * 格式化日志时间
 */
const formatLogTime = (timestamp) => {
  return date.formatDate(timestamp, 'HH:mm:ss')
}

/**
 * 开始指标模拟
 */
const startMetricsSimulation = () => {
  if (metricsUpdateTimer.value) {
    clearInterval(metricsUpdateTimer.value)
  }
  
  let epoch = currentMetrics.value.epoch || 0
  
  metricsUpdateTimer.value = setInterval(async () => {
    if (currentTrainingStatus.value === 'running' && epoch < totalEpochs.value) {
      epoch++
      
      const metrics = {
        epoch: epoch,
        step: epoch * 100,
        train_loss: Math.max(0.1, 2.0 - epoch * 0.05 + Math.random() * 0.1),
        val_loss: Math.max(0.15, 2.2 - epoch * 0.045 + Math.random() * 0.1),
        train_accuracy: Math.min(0.98, 0.3 + epoch * 0.02 + Math.random() * 0.05),
        val_accuracy: Math.min(0.95, 0.25 + epoch * 0.018 + Math.random() * 0.05),
        learning_rate: 0.001 * Math.pow(0.95, Math.floor(epoch / 10)),
        gpu_memory: '2.5GB'
      }
      
      const logEntry = `Epoch ${epoch}/${totalEpochs.value} - Loss: ${metrics.train_loss.toFixed(4)} - Acc: ${(metrics.train_accuracy * 100).toFixed(2)}%`
      
      // 更新指标
      await workflowStore.updateTrainingMetrics(metrics, logEntry)
      
      // 训练完成
      if (epoch >= totalEpochs.value) {
        stopMetricsSimulation()
        // 这里可以自动更新训练状态为完成
      }
    }
  }, 2000) // 每2秒更新一次
}

/**
 * 停止指标模拟
 */
const stopMetricsSimulation = () => {
  if (metricsUpdateTimer.value) {
    clearInterval(metricsUpdateTimer.value)
    metricsUpdateTimer.value = null
  }
}

/**
 * 步骤数据变化事件
 */
const onStepDataChanged = (data) => {
  console.log('步骤3数据已更新:', data)
}

/**
 * 步骤完成事件
 */
const onStepCompleted = (data) => {
  console.log('步骤3已完成:', data)
}

// 监听训练状态变化
watch(currentTrainingStatus, (newStatus) => {
  if (newStatus === 'running') {
    startMetricsSimulation()
  } else {
    stopMetricsSimulation()
  }
})

// 组件挂载时的初始化
onMounted(() => {
  // 如果训练正在进行，开始指标模拟
  if (currentTrainingStatus.value === 'running') {
    startMetricsSimulation()
  }
})

// 组件卸载时清理
onUnmounted(() => {
  stopMetricsSimulation()
})
</script>

<style scoped>
.step-three-content {
  max-width: 1000px;
  margin: 0 auto;
  padding: 24px;
}

.header {
  margin-bottom: 32px;
}

.config-section,
.training-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #1976d2;
  margin: 24px 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e3f2fd;
}

.button-section {
  display: flex;
  justify-content: center;
  padding-top: 24px;
  border-top: 1px solid #e0e0e0;
}

.status-card {
  margin-bottom: 24px;
}

.training-controls {
  display: flex;
  gap: 8px;
}

.metrics-section {
  margin-bottom: 24px;
}

.logs-section {
  margin-bottom: 24px;
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-entry {
  display: block;
  margin-bottom: 4px;
  padding: 2px 0;
}

.log-time {
  color: #666;
  margin-right: 8px;
}

.log-content {
  color: #333;
}
</style>
