import ray
from ray import tune
from ray.tune.registry import register_env
from ray.rllib.algorithms.ppo import PPOConfig
from envs.JSBSim.envs.multiplecombat_rllib_env import MultipleCombatEnv
import argparse
import os
import subprocess
import time


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--checkpoint-path", type=str, default="",
                      help="检查点路径，如果不指定则使用随机动作")
    parser.add_argument("--num-env-runners", type=int, default=32,
                      help="环境运行器数量")
    parser.add_argument("--num-learners", type=int, default=1,
                      help="学习器数量")
    parser.add_argument("--num-gpus", type=int, default=1,
                      help="GPU数量")
    parser.add_argument("--local-dir", type=str, default="~/ray_results",
                      help="结果保存路径")
    parser.add_argument("--tensorboard-dir", type=str, default="~/tensorboard_logs",
                      help="TensorBoard日志路径")
    parser.add_argument("--env-name", type=str, default="1v1/ShootMissile/HierarchyVsBaseline",
                      help="环境名称，例如：1v1/DodgeMissile/vsBaseline, 1v1/NoWeapon/vsBaseline, 1v1/ShootMissile/Selfplay")
    parser.add_argument("--training-iterations", type=int, default=2000,
                      help="训练迭代次数")
    parser.add_argument("--env-registry-name", type=str, default="JSBSim-Combat",
                      help="Ray注册的环境名称")
    parser.add_argument("--experiment-name", type=str, default="jsbsim_combat_vs_baseline",
                      help="实验名称，用于保存结果和日志")

    return parser.parse_args()

def env_creator(env_config):
    env = MultipleCombatEnv(env_config["env_name"])
    # env.seed(5)
    return env

def get_config(args):
    config = (
        PPOConfig()
        .environment(args.env_registry_name, env_config={"env_name": args.env_name})
        .framework("torch")
        .resources(num_gpus=args.num_gpus)
        .env_runners(
            num_env_runners=args.num_env_runners,
            rollout_fragment_length='auto',
            num_envs_per_env_runner=1,
            num_cpus_per_env_runner=1,
            num_gpus_per_env_runner=0,
        )
        .learners(
            num_learners=1,
            num_cpus_per_learner=1,
            num_gpus_per_learner=1
        )
        .training(
            gamma=0.99,
            lr=5e-5,
            train_batch_size=16384,
            lambda_=0.95,
            clip_param=0.2,
            vf_clip_param=10.0,
            entropy_coeff=0.01,
            num_sgd_iter=10,
            minibatch_size=2048,
        )
        .multi_agent(
            policies=["red_policy", "blue_policy"],
            policy_mapping_fn=lambda agent_id, *args, **kwargs: (
                "red_policy" if agent_id.startswith("A") else "blue_policy"
            ),
            policies_to_train=["red_policy"],  # 只训练红方策略
            # observation_fn=None,  # 使用默认观察函数
        )
        .evaluation(
            evaluation_interval=10,
            evaluation_duration=20,
            evaluation_num_workers=1,
            evaluation_config={
                "render_mode": "txt",
                "explore": False,
                "env_config": {
                    "env_name": args.env_name,
                },
            },
        )
    )
    
    # 清空探索配置
    config.exploration_config = {}
    return config

if __name__ == "__main__":
    args = parse_args()

    timestamp = time.strftime("%Y%m%d-%H%M%S")
    experiment_name = f"{args.experiment_name}_{timestamp}"
    
    local_dir = args.local_dir
    
    # 启动tensorboard
    subprocess.Popen(['tensorboard --logdir ~/ray_results/' + experiment_name + ' --bind_all'], shell=True)
    # subprocess.Popen([f'tensorboard --logdir {local_dir} {experiment_name} --bind_all'], shell=True)
    
    # 注册环境
    register_env(args.env_registry_name, env_creator)  # 使用指定的环境注册名称
    
    # 初始化Ray
    ray.init()
    
    # 获取配置
    config = get_config(args)
    
    # 运行训练
    tune.run(
        "PPO",
        config=config,
        stop={
            "training_iteration": args.training_iterations,
            # "episode_reward_mean": 800,
        },
        checkpoint_freq=50,
        checkpoint_at_end=True,
        # local_dir=os.path.expanduser(args.local_dir),
        storage_path=os.path.expanduser(local_dir),
        name=experiment_name,
        verbose=2,
    )
