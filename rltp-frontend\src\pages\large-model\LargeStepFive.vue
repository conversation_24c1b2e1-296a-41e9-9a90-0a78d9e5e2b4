<!--
 * @Author: Szc
 * @Date: 2025-01-01 10:00:00
 * @LastEditors: Szc
 * @LastEditTime: 2025-08-19 10:49:14
 * @Description: 大模型-第五步：模型量化剪枝
-->
<template>
    <div class="content">
        <div class="top">
            <div class="left">
                <div class="left-decoration"></div>
                <div class="optimization-section">
                    <div class="section-header">
                        <img class="arrow-icon" src="../../assets/images/icon_dz.png" alt="">
                        <span class="section-title">模型轻量化处理</span>
                    </div>
                    <div class="section-desc">配置量化和剪枝技术参数优化模型性能</div>


                    <div class="quantization-config">

                        <div class="config-row">
                            <div class="config-label">量化参数配置:</div>
                            <q-select v-model="quantizationConfig.method" :options="quantizationMethods"
                                class="config-select" outlined dense color="primary" />
                        </div>
                    </div>


                    <div class="pruning-config">

                        <div class="sparsity-config">
                            <div class="sparsity-row">
                                <div class="config-label">剪枝参数配置:</div>
                                <div class="slider-container">
                                    <el-slider v-model="pruningConfig.sparsityValue" :min="0" :max="1" :step="0.01"
                                        :format-tooltip="formatSparsityTooltip" show-tooltip
                                        @input="updateSparsityDisplay" />
                                </div>
                                <q-input v-model="sparsityDisplayValueWithPercent" class="sparsity-value-box" outlined
                                    dense @blur="updateSparsityFromInput" @keyup.enter="updateSparsityFromInput" />
                            </div>
                        </div>
                    </div>


                    <div class="pruning-metrics">
                        <div class="metric-item">
                            <div class="metric-value pruning-param">{{ pruningMetrics.parameterReduction }} %</div>
                            <div class="metric-label">参数减少</div>
                        </div>
                        <div class="metric-divider"></div>
                        <div class="metric-item">
                            <div class="metric-value pruning-speed">{{ pruningMetrics.speedImprovement }} %</div>
                            <div class="metric-label">推理速度提升</div>
                        </div>
                        <div class="metric-divider"></div>
                        <div class="metric-item">
                            <div class="metric-value pruning-loss">{{ pruningMetrics.accuracyLoss }} %</div>
                            <div class="metric-label">预计精度损失</div>
                        </div>
                    </div>


                    <div v-if="isOptimizing || optimizationCompleted || optimizationFailed || isPruning || pruningCompleted || pruningFailed"
                        class="optimization-status">

                        <div v-if="isOptimizing || isPruning" class="status-item processing">
                            <div class="status-header">
                                <div class="status-icon loading-spinner"></div>
                                <span class="status-text">正在处理: {{ isOptimizing ? '模型验证' : '模型剪枝' }}</span>
                            </div>
                            <div class="status-progress">
                                <el-progress :percentage="isOptimizing ? optimizationProgress : pruningProgress"
                                    :stroke-width="6" :show-text="true" />
                            </div>
                        </div>


                        <div v-if="(optimizationCompleted && !optimizationFailed) || (pruningCompleted && !pruningFailed)"
                            class="status-item completed">
                            <div class="status-header">
                                <img src="../../assets/images/icon_cg.png" alt="成功" class="status-icon" />
                                <span class="status-text">{{ optimizationCompleted ? '优化完成' : '剪枝完成' }}</span>
                            </div>
                            <div class="status-progress">
                                <el-progress :percentage="100" :stroke-width="6" />
                            </div>
                        </div>


                        <div v-if="optimizationFailed || pruningFailed" class="status-item failed">
                            <div class="status-header">
                                <img src="../../assets/images/icon_shibai.png" alt="失败" class="status-icon" />
                                <span class="status-text">{{ optimizationFailed ? '优化失败' : '剪枝失败' }}</span>
                            </div>
                            <div class="status-progress">
                                <el-progress :percentage="optimizationFailed ? optimizationProgress : pruningProgress"
                                    :stroke-width="6" :show-text="true" />
                            </div>
                        </div>


                        <div class="status-logs">
                            <div class="log-content">
                                <div class="log-icon-wrapper">
                                    <img src="../../assets/images/icon_rizhi.png" alt="日志" class="log-icon" />
                                    <div class="log-title">日志处理</div>
                                </div>
                                <div v-for="(log, index) in (isOptimizing || optimizationCompleted || optimizationFailed ? optimizationLogs : pruningLogs)"
                                    :key="index" class="log-item">
                                    <span class="log-time">[{{ log.time }}]</span>
                                    <span class="log-message">{{ log.message }}</span>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="optimization-actions">
                        <q-btn class="start-btn roundBox" color="primary" @click="startOptimization"
                            :disable="!canStartOptimization || isOptimizing || isPruning">
                            <img src="../../assets/images/btn_icon_ksyh.png" alt="开始优化" class="btn-icon">
                            <span style="font-size: .225rem;">开始优化</span>
                        </q-btn>
                        <q-btn class="export-btn roundBox" color="secondary" @click="exportOptimizedModel"
                            :disable="!optimizationCompleted && !pruningCompleted">
                            <img src="../../assets/images/btn_icon_dcmx.png" alt="导出模型" class="btn-icon">
                            <span style="font-size: .225rem;">导出模型</span>
                        </q-btn>
                    </div>


                </div>
            </div>


            <div class="right">
                <div class="comparison-section">
                    <div class="section-header">
                        <img class="arrow-icon" src="../../assets/images/icon_dz.png" alt="">
                        <span class="section-title">模型性能对比</span>
                    </div>
                    <div class="section-desc">优化前后的详细性能指标对比分析</div>


                    <div class="comparison-content">

                        <div class="radar-container">
                            <div class="radar-header">
                                <img class="radar-icon" src="../../assets/images/icon_ww.png" alt="">
                                <span class="radar-title">五维性能雷达图</span>
                            </div>
                            <div ref="radarChartRef" class="radar-chart"></div>
                        </div>


                        <div class="metrics-container">
                            <div class="metrics-header">
                                <img class="metrics-icon" src="../../assets/images/icon_xxzb.png" alt="">
                                <span class="metrics-title">详细指标对比</span>
                            </div>
                            <div class="metrics-table">
                                <div class="table-header">
                                    <div class="header-cell indicator">指标</div>
                                    <div class="header-cell original">原始模型</div>
                                    <div class="header-cell optimized">优化模型</div>
                                    <div class="header-cell change">变化</div>
                                    <div class="header-cell improvement">改进率</div>
                                </div>
                                <div class="table-body">
                                    <div v-for="(metric, index) in detailedMetrics" :key="index" class="table-row">
                                        <div class="cell indicator">{{ metric.name }}</div>
                                        <div class="cell original">{{ metric.original }}</div>
                                        <div class="cell optimized">{{ metric.optimized }}</div>
                                        <div class="cell change">
                                            <img v-if="metric.changeColor == 'green'" class="changeIcon"
                                                src="../../assets/images/icon_zhang.png" alt="">
                                            <img v-else class="changeIcon" src="../../assets/images/icon_jiang.png"
                                                alt="">
                                        </div>
                                        <div class="cell improvement" :style="{ color: metric.improvementColor }">
                                            {{ metric.improvement }}
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <div class="optimization-summary">
                                <div v-for="(summary, index) in optimizationSummary" :key="index" class="summary-item">
                                    <div class="summary-value" :style="{ color: summary.color }">{{ summary.value }}
                                    </div>
                                    <div class="summary-label">{{ summary.label }}</div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="bottom">
                        <div class="next">
                            <q-btn class="prevBtn roundBox" color="grey-7" label="上一步" @click="prevStep" />
                            <q-btn class="nextBtn roundBox" color="primary" label="下一步" @click="nextStep" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, defineEmits, nextTick, computed, watch } from 'vue'
import * as echarts from 'echarts'
import { usePlugin } from 'composables/plugin.js'
import { useLargeModelStore } from '../../stores/largeModelStore'

const emit = defineEmits(['next-step', 'prev-step'])
const { notify } = usePlugin()
const largeModelStore = useLargeModelStore()
const quantizationConfig = ref({
    method: 'INT8 量化',
    bitWidth: '8 位',
    calibrationDataset: ''
})

const pruningConfig = ref({
    method: '结构化剪枝',
    granularity: '全局剪枝',
    sparsityValue: 0.3,
    finetuneDataset: ''
})

const quantizationMethods = ['INT8 量化', 'INT4 量化', 'FP16 量化', '动态量化']

const pruningMetrics = ref({
    parameterReduction: 75,
    speedImprovement: 45,
    accuracyLoss: 6
})

const isOptimizing = ref(false)
const optimizationCompleted = ref(false)
const optimizationFailed = ref(false)
const optimizationProgress = ref(0)
const optimizationLogs = ref([])

const isPruning = ref(false)
const pruningCompleted = ref(false)
const pruningFailed = ref(false)
const pruningProgress = ref(0)
const pruningLogs = ref([])

const radarChartRef = ref(null)
let radarChartInstance = null
const detailedMetrics = ref([
    {
        name: '参数量',
        original: '175.0B',
        optimized: '87.5B',
        change: '-50.0%',
        improvement: '-50.0%',
        changeIcon: 'trending_down',
        changeColor: 'green',
        improvementColor: '#4CAF50'
    },
    {
        name: '推理速度',
        original: '45.20ms',
        optimized: '28.70ms',
        change: '-36.5%',
        improvement: '-36.5%',
        changeIcon: 'trending_down',
        changeColor: 'green',
        improvementColor: '#4CAF50'
    },
    {
        name: '显存占用',
        original: '12.80GB',
        optimized: '6.4GB',
        change: '-50.0%',
        improvement: '-50.0%',
        changeIcon: 'trending_down',
        changeColor: 'green',
        improvementColor: '#4CAF50'
    },
    {
        name: '准确率',
        original: '94.50%',
        optimized: '93.30%',
        change: '-3.5%',
        improvement: '-3.5%',
        changeIcon: 'trending_down',
        changeColor: 'red',
        improvementColor: '#F44336'
    },
    {
        name: '模型大小',
        original: '350.00MB',
        optimized: '175.00MB',
        change: '-50.0%',
        improvement: '-50.0%',
        changeIcon: 'trending_down',
        changeColor: 'green',
        improvementColor: '#4CAF50'
    }
])

const optimizationSummary = ref([
    { value: '75 %', label: '模型大小减少', color: '#41bd73' },
    { value: '-36.5 %', label: '推理速度提升', color: '#26b6d0' },
    { value: '50.0 %', label: '内存占用减少', color: '#ffa363' },
    { value: '1.38 %', label: '精度损失', color: '#4ab4ff' }
])

const sparsityDisplayValue = ref('30.0')
const sparsityDisplayValueWithPercent = computed({
    get() {
        return sparsityDisplayValue.value + '%'
    },
    set(value) {
        const numericValue = value.replace('%', '')
        sparsityDisplayValue.value = numericValue
    }
})

const canStartOptimization = computed(() => {
    return quantizationConfig.value.method
})

function formatSparsityTooltip(value) {
    return `${(value * 100).toFixed(1)}%`
}

function updateSparsityFromInput() {
    const inputValue = sparsityDisplayValueWithPercent.value.replace('%', '')
    const value = parseFloat(inputValue)
    if (!isNaN(value) && value >= 0 && value <= 100) {
        pruningConfig.value.sparsityValue = value / 100
        sparsityDisplayValue.value = value.toFixed(1)
    } else {
        sparsityDisplayValue.value = (pruningConfig.value.sparsityValue * 100).toFixed(1)
    }
}

function updateSparsityDisplay() {
    sparsityDisplayValue.value = (pruningConfig.value.sparsityValue * 100).toFixed(1)
}

function initRadarChart() {
    if (radarChartRef.value) {
        try {
            if (radarChartInstance) {
                radarChartInstance.dispose()
            }

            radarChartInstance = echarts.init(radarChartRef.value)

            const radarOption = {
                title: {
                    text: '',
                },
                tooltip: {
                    axisPointer: {
                        type: 'shadow'
                    },
                    textStyle: {
                        fontSize: window.screen.width > 1536 ? '20' : '14',
                        color: '#333',
                    }
                },
                legend: {
                    show: true,
                    data: ['原始模型', '优化模型'],
                    textStyle: {
                        color: '#fff',
                        fontSize: window.screen.width > 1536 ? '20' : 14,
                    },
                    top: '5%',
                    left: '5%',
                    orient: 'vertical',
                },
                radar: {
                    center: ['50%', '55%'],
                    radius: window.screen.width > 1536 ? '70%' : '60%',
                    shape: 'polygon',
                    name: {
                        textStyle: {
                            color: '#fff',
                            fontSize: window.screen.width > 1536 ? '20' : '14',
                            padding: [3, 5]
                        }
                    },
                    splitNumber: 5,
                    indicator: [
                        { name: '参数量', max: 100 },
                        { name: '推理速度', max: 100 },
                        { name: '现存占用', max: 100 },
                        { name: '准确率', max: 100 },
                        { name: '模型大小', max: 100 }
                    ],
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(255, 255, 255, 0.3)',
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(255, 255, 255, 0.3)',
                        }
                    },
                    splitArea: {
                        show: true,
                        areaStyle: {
                            color: ['rgba(255, 255, 255, 0.05)', 'rgba(255, 255, 255, 0.1)']
                        }
                    }
                },
                textStyle: { fontSize: window.screen.width > 1536 ? '22' : '14' },
                series: [
                    {
                        name: '原始模型',
                        type: 'radar',
                        areaStyle: {
                            normal: {
                                opacity: 0.3,
                                color: 'rgba(135, 206, 250, 0.3)'
                            }
                        },
                        lineStyle: {
                            color: 'rgb(135, 206, 250)',
                            width: 2
                        },
                        data: [
                            {
                                value: [40, 65, 45, 95, 30],
                                name: '原始模型'
                            }
                        ]
                    },
                    {
                        name: '优化模型',
                        type: 'radar',
                        areaStyle: {
                            normal: {
                                opacity: 0.3,
                                color: 'rgba(78, 205, 196, 0.3)'
                            }
                        },
                        lineStyle: {
                            color: 'rgb(78, 205, 196)',
                            width: 2
                        },
                        data: [
                            {
                                value: [90, 85, 90, 93, 90],
                                name: '优化模型'
                            }
                        ]
                    }
                ]
            }

            radarChartInstance.setOption(radarOption)
        } catch (error) {
            console.error('初始化雷达图失败:', error)
        }
    }
}

function resizeChart() {
    radarChartInstance?.resize()
}

async function startOptimization() {
    await startQuantization()
}

async function startQuantization() {
    isOptimizing.value = true
    optimizationCompleted.value = false
    optimizationFailed.value = false
    optimizationProgress.value = 0
    optimizationLogs.value = []

    const logs = [
        { time: '09:09:50', message: '开始 量化处理...' },
        { time: '09:09:51', message: '量化处理 完成...' },
        { time: '09:09:53', message: '开始 模型验证...' }
    ]

    try {
        for (let i = 0; i <= 100; i += 5) {
            await new Promise(resolve => setTimeout(resolve, 200))
            optimizationProgress.value = i

            if (i === 20 && logs[0]) {
                optimizationLogs.value.push(logs[0])
            } else if (i === 60 && logs[1]) {
                optimizationLogs.value.push(logs[1])
            } else if (i === 80 && logs[2]) {
                optimizationLogs.value.push(logs[2])
            }

            // 模拟随机失败 (20% 概率)
            if (i === 65 && Math.random() < 0.2) {
                throw new Error('模型验证失败')
            }
        }

        isOptimizing.value = false
        optimizationCompleted.value = true
        optimizationLogs.value.push({ time: '09:09:53', message: '量化优化完成' })
        notify('模型量化完成！', 'positive')
    } catch (error) {
        isOptimizing.value = false
        optimizationFailed.value = true
        optimizationLogs.value.push({ time: new Date().toLocaleTimeString(), message: `未接受 XXX 数据 ...` })
        notify('模型量化失败！', 'negative')
    }
}

async function startPruning() {
    isPruning.value = true
    pruningCompleted.value = false
    pruningFailed.value = false
    pruningProgress.value = 0
    pruningLogs.value = []

    const logs = [
        { time: '09:09:50', message: '开始 剪枝处理...' },
        { time: '09:09:51', message: '剪枝处理 完成...' },
        { time: '09:09:53', message: '开始 模型验证...' }
    ]

    try {
        for (let i = 0; i <= 100; i += 5) {
            await new Promise(resolve => setTimeout(resolve, 200))
            pruningProgress.value = i

            if (i === 20 && logs[0]) {
                pruningLogs.value.push(logs[0])
            } else if (i === 60 && logs[1]) {
                pruningLogs.value.push(logs[1])
            } else if (i === 80 && logs[2]) {
                pruningLogs.value.push(logs[2])
            }

            if (i === 65 && Math.random() < 0.2) {
                throw new Error('模型验证失败')
            }
        }

        isPruning.value = false
        pruningCompleted.value = true
        pruningLogs.value.push({ time: '09:09:53', message: '剪枝优化完成' })
        notify('模型剪枝完成！', 'positive')
    } catch (error) {
        isPruning.value = false
        pruningFailed.value = true
        pruningLogs.value.push({ time: new Date().toLocaleTimeString(), message: `未接受 XXX 数据 ...` })
        notify('模型剪枝失败！', 'negative')
    }
}

function exportOptimizedModel() {
    notify('模型导出功能开发中...', 'info')
}

function nextStep() {
    largeModelStore.updateStepFiveData({
        quantizationConfig: quantizationConfig.value,
        pruningConfig: pruningConfig.value,
        optimizationCompleted: optimizationCompleted.value,
        pruningCompleted: pruningCompleted.value,
        detailedMetrics: detailedMetrics.value
    })

    emit('next-step')
}

function prevStep() {
    emit('prev-step')
}

watch(
    () => pruningConfig.value.sparsityValue,
    (newValue) => {
        sparsityDisplayValue.value = (newValue * 100).toFixed(1)
    },
    { immediate: true }
)

onMounted(async () => {
    sparsityDisplayValue.value = (pruningConfig.value.sparsityValue * 100).toFixed(1)

    nextTick(() => {
        initRadarChart()
    })

    window.addEventListener('resize', resizeChart)
})

onBeforeUnmount(() => {
    if (radarChartInstance) {
        radarChartInstance.dispose()
        radarChartInstance = null
    }

    window.removeEventListener('resize', resizeChart)
})
</script>

<style lang="scss" scoped>
.content {
    display: flex;
    flex-direction: column;
    flex:1;
    .top {
        display: flex;
        margin-bottom: .125rem;
        flex:1;
        min-height: 9.5rem;

        .left {
            width: 30%;
            height: inherit;
            border: .025rem solid #707070;
            background-color: #181a24;
            padding: .3375rem;
            display: flex;
            flex-direction: column;
            margin-right: .125rem;
            position: relative;

            // 底纹层 - 使用伪元素，控制z-index
            &::before {
                position: absolute;
                content: '';
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-image:
                    repeating-linear-gradient(130deg,
                        rgba(255, 255, 255, 0.05) 0px,
                        rgba(255, 255, 255, 0.01) 4px,
                        transparent 1px,
                        transparent 15px);
                z-index: 10;
                pointer-events: none; // 允许点击穿透到内容元素
            }

            // 左侧装饰线1
            &::after {
                position: absolute;
                content: '';
                left: -0.1rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198);
                border-radius: .125rem;
                z-index: 3;
            }

            // 左侧装饰线2 - 使用额外的伪元素
            .left-decoration {
                position: absolute;
                left: -0.075rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198, .5);
                z-index: 3;
            }

            // 确保内容在底纹之上
            .optimization-section {
                position: relative;
                z-index: 1;
            }
        }

        .right {
            width: 70%;
            height: inherit;
            border: .025rem solid #707070;
            background-color: #181a24;
            background-image:
                repeating-linear-gradient(130deg,
                    rgba(255, 255, 255, 0.05) 0px,
                    rgba(255, 255, 255, 0.01) 4px,
                    transparent 1px,
                    transparent 15px);
            padding: .3375rem;
            display: flex;
            flex-direction: column;
            position: relative;
            margin-left: .0625rem;

            // overflow: hidden; 注释掉 让装饰线显示出来
            &::before {
                position: absolute;
                content: '';
                left: -0.1rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198);
                border-radius: .125rem;
            }

            &::after {
                position: absolute;
                content: '';
                left: -0.075rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198, .5);
            }
        }
    }
}

// 优化区域
.optimization-section {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    padding-right: .125rem; // 给滚动条留出空间

    // 自定义滚动条样式
    &::-webkit-scrollbar {
        width: .0625rem; // 滚动条宽度
    }

    &::-webkit-scrollbar-track {
        background: rgb(0, 0, 0); // 滚动条轨道背景
        border-radius: .03125rem;
    }

    &::-webkit-scrollbar-thumb {
        background: rgba(114, 114, 114, 0.6); // 滚动条颜色
        border-radius: .03125rem;

        &:hover {
            background: rgba(74, 180, 255, 0.8); // 悬停时的颜色
        }
    }

    .section-header {
        display: flex;
        align-items: center;
        margin-bottom: .125rem;

        .arrow-icon {
            width: .2rem;
            height: .2rem;
            margin-right: .125rem;
        }

        .section-title {
            color: #fff;
            font-size: .2rem;
        }
    }

    .section-desc {
        color: #9cacc6;
        font-size: .15rem;
        margin-bottom: .25rem;
        padding-left: .35rem;
    }



    .quantization-config,
    .pruning-config {
        display: flex;
        flex-direction: column;
        margin-bottom: .25rem;
    }
    .sparsity-config {
        margin-bottom: .1875rem;

        .sparsity-row {
            display: flex;
            align-items: center;
            gap: .125rem;

            .config-label {
                color: white;
                font-size: .175rem;
                min-width: 1.5rem;
                flex-shrink: 0;
            }

            .slider-container {
                flex: 1;
                display: flex;
                align-items: center;

                :deep(.el-slider) {
                    width: 100%;
                }
            }

            .sparsity-value-box {
                width: 1.25rem;
                flex-shrink: 0;

                :deep(.q-field__native) {
                    color: white !important;
                    text-align: center !important;
                }

                :deep(.q-field) {
                    font-size: .175rem;
                }
            }

            .q-field {
                font-size: .175rem !important;
            }
        }
    }


    .pruning-metrics {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: .25rem;
        padding: .1875rem;
        background-color: #1f2839;
        border-radius: .0625rem;

        .metric-item {
            flex: 1;
            text-align: center;

            .metric-value {
                font-size: .325rem;
                font-weight: bold;

                &.pruning-param {
                    color: #41bd73;
                }

                &.pruning-speed {
                    color: #26b6d0;
                }

                &.pruning-loss {
                    color: #4ab4ff;
                }
            }

            .metric-label {
                color: #999;
                font-size: .15rem;
                margin-top: .0625rem;
            }
        }

        .metric-divider {
            width: .025rem;
            height: .625rem;
            background-color: #707070;
            margin: 0 .125rem;
        }
    }

    .config-row {
        display: flex;
        align-items: center;
        margin-bottom: .1875rem;

        .config-label {
            width: 1.5rem;
            color: white;
            font-size: .175rem;
            margin-right: .125rem;
        }

        .config-select,
        .config-input {
            flex: 1;

            :deep(.q-field__control) {
                height: .5rem !important;
                min-height: .5rem !important;
            }

            :deep(.q-field__native) {
                color: white !important;
                font-size: .15rem !important;
            }

            :deep(.q-field__label) {
                color: #999 !important;
                font-size: .15rem !important;
            }
        }
    }

    .optimization-status {
        margin-bottom: .25rem;
        padding: .1875rem;
        background-color: #1a293e;
        border-radius: .0625rem;

        .status-item {
            display: flex;
            flex-direction: column;
            margin-bottom: .125rem;

            .status-header {
                display: flex;
                align-items: center;
                margin-bottom: .125rem;

                .status-icon {
                    width: .375rem;
                    height: .375rem;
                    margin-right: .125rem;
                    flex-shrink: 0;
                }

                .loading-spinner {
                    border: .05rem solid rgba(255, 255, 255, 0.3);
                    border-top: .05rem solid #4ab4ff;
                    border-radius: 50%;
                    animation: loading-spin 1s linear infinite;
                }

                .status-text {
                    color: #4ab4ff;
                    font-size: .175rem;
                }
            }

            .status-progress {
                display: flex;
                align-items: center;

                :deep(.el-progress) {
                    flex: 1;
                    margin-right: .125rem;

                    .el-progress-bar__outer {
                        background-color: #2a2d3a;
                    }

                }

                :deep(.el-progress__text) {
                    font-size: .25rem !important;
                    color: white !important;
                }

                .progress-text {
                    color: white;
                    font-size: .15rem;
                    min-width: .75rem;
                    text-align: right;
                }
            }

        }

        .status-logs {
            margin-top: .125rem;

            .log-header {
                display: flex;
                align-items: center;
                color: white;
                font-size: .15rem;
                margin-bottom: .125rem;

                .q-icon {
                    margin-right: .0625rem;
                }
            }

            .log-content {
                background-color: #273649;
                border-radius: .0625rem;
                padding: .125rem;
                max-height: 1.2rem;
                overflow-y: auto;

                .log-icon-wrapper {
                    margin-bottom: .0625rem;
                    display: flex;
                    align-items: center;

                    .log-icon {
                        width: .25rem;
                        height: .25rem;
                        margin-right: .1rem;
                    }

                    .log-title {
                        font-size: .175rem;
                        color: white;
                    }
                }

                .log-item {
                    color: #999;
                    font-size: .125rem;
                    margin-bottom: .0625rem;
                    font-family: 'Consolas', monospace;

                    .log-time {
                        color: #4ab4ff;
                        margin-right: .0625rem;
                    }

                    .log-message {
                        color: #fff;
                    }
                }
            }
        }
    }

    .optimization-actions {
        margin-bottom: .25rem;
        display: flex;
        justify-content: space-between;
        gap: .125rem;

        .start-btn,
        .export-btn {
            flex: 1;
            height: .5rem;
            font-size: .15rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: .0625rem;

            .btn-icon {
                width: .3rem;
                height: .3rem;
                margin-right: .125rem;
            }
        }

        .start-btn {
            color: white !important;
        }

        .export-btn {
            background-color: #4a4a4a !important;
            color: white !important;

            &:hover:not(:disabled) {
                background-color: #5a5a5a !important;
            }

            &:disabled {
                background-color: #333 !important;
                color: white !important;
            }
        }
    }
}

.comparison-section {
    height: 100%;
    display: flex;
    flex-direction: column;

    .section-header {
        display: flex;
        align-items: center;
        margin-bottom: .125rem;

        .arrow-icon {
            width: .2rem;
            height: .2rem;
            margin-right: .125rem;
        }

        .section-title {
            color: #fff;
            font-size: .2rem;
        }
    }

    .section-desc {
        color: #9cacc6;
        font-size: .15rem;
        margin-bottom: .25rem;
        padding-left: .35rem;
    }

    .comparison-content {
        flex: 1;
        display: flex;
        gap: .125rem;
        margin-bottom: .1875rem;
    }

    .radar-container {
        width: 45%;
        display: flex;
        flex-direction: column;

        .radar-header {
            display: flex;
            align-items: center;
            margin-bottom: .125rem;
            padding-left: .35rem;

            .radar-icon {
                width: .275rem;
                height: .275rem;
                margin-right: .125rem;
            }

            .radar-title {
                color: #fff;
                font-size: .175rem;

            }
        }

        .radar-chart {
            flex: 1;
            min-height: 6rem;
        }
    }

    .metrics-container {
        width: 55%;
        display: flex;
        flex-direction: column;
        margin-top: .375rem;
        margin-left: .375rem;

        .metrics-header {
            display: flex;
            align-items: center;
            margin-bottom: .125rem;

            .metrics-icon {
                width: .275rem;
                height: .275rem;
                margin-right: .125rem;
            }

            .metrics-title {
                color: #fff;
                font-size: .2rem;
            }
        }

        .metrics-table {
            background-color: transparent;
            overflow: hidden;
            margin-bottom: .25rem;
            .table-header {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
                background: linear-gradient(to right, rgba(33, 111, 110, 0.7) 30%, rgba(0, 51, 102, 0.7) 70%);

                .header-cell {
                    padding: .125rem .0625rem;
                    text-align: center;
                    color: #63d4ff;
                    font-size: .2rem;
                }
            }

            .table-body {
                .table-row {
                    display: grid;
                    grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
                    border-bottom: .0125rem solid #333;

                    &:nth-child(even) {
                        background: rgba(34, 69, 101, .1);
                    }

                    &:last-child {
                        border-bottom: none;
                    }

                    .cell {
                        padding: .125rem .0625rem;
                        text-align: center;
                        font-size: .175rem;
                        color: white;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: .0625rem;

                        &.indicator {
                            color: white;
                            justify-content: center;
                        }

                        .changeIcon {
                            width: .3625rem;
                            height: .175rem;
                        }
                    }
                }
            }
        }

        .optimization-summary {
            display: flex;
            gap: .125rem;
            justify-content: space-between;

            .summary-item {
                width: 1.85rem;
                height: 1.25rem;
                text-align: center;
                padding: .125rem;
                background: rgba(34, 69, 101, .2);
                border-radius: .0625rem;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;

                .summary-value {
                    font-size: .325rem;
                    font-weight: bold;
                    margin-bottom: .0625rem;
                }

                .summary-label {
                    color: #999;
                    font-size: .175rem;
                }
            }
        }
    }

    .bottom {
        display: flex;
        align-items: center;
        flex-shrink: 0;
        margin-top: .1875rem;

        .next {
            display: flex;
            justify-content: space-between;
            width: 100%;
            padding: 0 .25rem;
            gap: .25rem;

            .prevBtn {
                margin-right: auto;
            }

            .nextBtn {
                margin-left: auto;
            }
        }
    }
}



@keyframes loading-spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

:deep(.q-field--dense .q-field__marginal) {
    height: 100% !important;
}

// el-slider 样式 - 参考 LargeStepOne，更细的轨道和更小的按钮
:deep(.el-slider__runway) {
    background-color: #2a2d3a;
    height: .0625rem; // 更细的轨道
    border-radius: .03125rem;
}

:deep(.el-slider__bar) {
    background-color: #4ab4ff;
    height: .0625rem; // 更细的进度条
    border-radius: .03125rem;
}

:deep(.el-slider__button) {
    border: .015625rem solid white !important; // 更细的边框
    background-color: #4ab4ff !important;
    width: .1875rem; // 更小的按钮
    height: .1875rem; // 更小的按钮
    border-radius: 50% !important;
}

:deep(.el-slider__button-wrapper) {
    width: .1875rem !important;
    height: .1875rem !important;
    top: -.09375rem !important; // 调整垂直位置以居中对齐
}
</style>