import time
import ray
from ray import tune
from ray.tune.registry import register_env
from ray.rllib.algorithms.ppo import PPOConfig
from ray.rllib.algorithms import Algorithm
from ray.rllib.callbacks.callbacks import RLlibCallback
from torch import Tensor
from envs.JSBSim.envs.multiplecombat_rllib_env import MultipleCombatEnv
import argparse
import os
import subprocess

class AlgorithFix(RLlibCallback):
    def __init__(self, **kwargs):
        super().__init__()

    def on_checkpoint_loaded(self, *, algorithm: Algorithm, **kwargs, ) -> None:
        def betas_tensor_to_float(learner):
            param_grp = next(iter(learner._optimizer_parameters.keys())).param_groups[0]
            if not param_grp['capturable'] and isinstance(param_grp["betas"][0], Tensor):
                param_grp["betas"] = tuple(beta.item() for beta in param_grp["betas"])
        algorithm.learner_group.foreach_learner(betas_tensor_to_float)
        

def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--checkpoint-path", type=str, default="",
                      help="检查点路径，如果不指定则使用随机动作")
    parser.add_argument("--num-env-runners", type=int, default=32,
                      help="环境运行器数量")
    parser.add_argument("--num-learners", type=int, default=1,
                      help="学习器数量")
    parser.add_argument("--num-gpus", type=int, default=1,
                      help="GPU数量")
    parser.add_argument("--local-dir", type=str, default="~/ray_results",
                      help="结果保存路径")
    parser.add_argument("--tensorboard-dir", type=str, default="~/tensorboard_logs",
                      help="TensorBoard日志路径")
    parser.add_argument("--env-name", type=str, default="3v1/ShootMissile/HierarchyVsBaseline",
                      help="环境名称，例如：1v1/DodgeMissile/vsBaseline, 1v1/NoWeapon/vsBaseline, 1v1/ShootMissile/Selfplay")
    parser.add_argument("--training-iterations", type=int, default=2000,
                      help="训练迭代次数")
    parser.add_argument("--env-registry-name", type=str, default="JSBSim-Combat",
                      help="Ray注册的环境名称")
    parser.add_argument("--experiment-name", type=str, default="jsbsim_combat_vs_baseline",
                      help="实验名称，用于保存结果和日志")
    parser.add_argument("--episode-reward-mean", type=float, default=1600,
                      help="训练停止条件：平均奖励阈值")
    parser.add_argument("--checkpoint-freq", type=int, default=10,
                      help="检查点保存频率（每多少次迭代保存一次）")

    return parser.parse_args()

def env_creator(env_config):
    # 使用传入的环境名称
    env = MultipleCombatEnv(env_config["env_name"])
    # env.seed(5)
    return env

def get_config(args):
    config = (
        PPOConfig()
        .environment(args.env_registry_name, env_config={"env_name": args.env_name})
        .framework("torch")
        .resources(num_gpus=args.num_gpus)
        .env_runners(
            num_env_runners=args.num_env_runners,
            rollout_fragment_length='auto',
            num_envs_per_env_runner=1,
            num_cpus_per_env_runner=1,
            num_gpus_per_env_runner=0,
        )
        .learners(
            num_learners=args.num_learners,
            num_cpus_per_learner=1,
            num_gpus_per_learner=args.num_gpus/args.num_learners
        )
        # .callbacks(callbacks_class=AlgorithFix)
        .training(
            gamma=0.99,
            lr=5e-5,
            train_batch_size=16384,
            lambda_=0.95,
            clip_param=0.2,
            vf_clip_param=10.0,
            entropy_coeff=0.01,
            num_sgd_iter=10,
            minibatch_size=2048,
            # optimizer={
            #     # "type": "Adam",
            #     "foreach": False, # Disable foreach optimization
            #     "capturable": False,
            # },
            model={
                # 中央评论家网络配置
                "fcnet_hiddens": [512, 512, 256],
                "fcnet_activation": "relu",
                "vf_share_layers": False,  # 分离价值网络和策略网络
                "use_lstm": True,
                "lstm_cell_size": 256,
                "max_seq_len": 32,
            },
        )
        .multi_agent(
            policies=["red_policy", "blue_policy"],
            policy_mapping_fn=lambda agent_id, *args, **kwargs: (
                "red_policy" if agent_id.startswith("A") else "blue_policy"
            ),
            policies_to_train=["red_policy"],  # 只训练红方策略
        )
        .evaluation(
            evaluation_interval=10,
            evaluation_duration=20,
            evaluation_num_workers=10,
            evaluation_config={
                "render_mode": "txt",
                "explore": False,
                "env_config": {
                    "env_name": args.env_name,
                },
            },
        )
    )
    
    # 清空探索配置
    config.exploration_config = {}
    return config

if __name__ == "__main__":
    args = parse_args()
    
    timestamp = time.strftime("%Y%m%d-%H%M%S")
    experiment_name = f"{args.experiment_name}_{timestamp}"
    local_dir = args.local_dir
    
    # experiment_name = "jsbsim_combat_vs_baseline_20250509-160411"
    # print("experiment_name: ", experiment_name)
    
    # 启动tensorboard
    subprocess.Popen(['tensorboard --logdir ~/ray_results/' + experiment_name + ' --bind_all'], shell=True)
    # subprocess.Popen([f'tensorboard --logdir {local_dir} {experiment_name} --bind_all'], shell=True)
    
    # 注册环境
    register_env(args.env_registry_name, env_creator)  # 使用指定的环境注册名称
    
    # 初始化Ray
    ray.init()
    
    # 获取配置
    config = get_config(args)
    
    # 运行训练
    tune.run(
        "PPO",
        config=config,
        # resume="Local",
        # restore=args.checkpoint_path if args.checkpoint_path else None,
        stop={
            "training_iteration": args.training_iterations,
            # "episode_reward_mean": args.episode_reward_mean,
        },
        checkpoint_freq=args.checkpoint_freq,
        checkpoint_at_end=True,
        # local_dir=os.path.expanduser(args.local_dir),
        storage_path=os.path.expanduser(local_dir),
        name=experiment_name,
        verbose=2,
    )
    
    # jsbsim_combat_vs_baseline_20250509-143640
    
    
