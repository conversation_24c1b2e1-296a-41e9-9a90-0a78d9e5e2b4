import ray
from ray.rllib.algorithms.ppo import PPOConfig
from ray.rllib.algorithms.ppo import PPO
import argparse
import os
import time
import json
import torch
import numpy as np
import gymnasium as gym
from pathlib import Path


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--checkpoint-path", type=str, required=True,
                      help="检查点路径，必须指定")
    parser.add_argument("--env-name", type=str, default="Pendulum-v1",
                      help="环境名称")
    parser.add_argument("--num-episodes", type=int, default=1,
                      help="评估的回合数")
    parser.add_argument("--render-mode", type=str, default="rgb_array",
                      help="渲染模式: rgb_array, human, 或 None")
    parser.add_argument("--output-dir", type=str, default="./replay_files",
                      help="回放文件输出目录")
    parser.add_argument("--save-video", action="store_true",
                      help="是否保存视频文件")
    parser.add_argument("--video-fps", type=int, default=30,
                      help="视频帧率")
    parser.add_argument("--explore", action="store_true",
                      help="是否使用探索策略")
    parser.add_argument("--seed", type=int, default=42,
                      help="随机种子")
    
    return parser.parse_args()


def create_eval_config(args):
    """创建评估配置"""
    config = (
        PPOConfig()
        .environment(args.env_name)
        .framework("torch")
        .env_runners(
            num_env_runners=1,
            num_cpus_per_env_runner=1,
            num_gpus_per_env_runner=0,
            num_envs_per_env_runner=1,
        )
        .learners(
            num_learners=1,
            num_cpus_per_learner=1,
            num_gpus_per_learner=0
        )
        .training(
            gamma=0.95,
            lambda_=0.5,
        )
        .evaluation(
            evaluation_interval=1,
            evaluation_duration=args.num_episodes,
            evaluation_num_workers=1,
            evaluation_config={
                "render_mode": args.render_mode,
                "explore": args.explore,
            },
        )
    )
    
    # 清空探索配置
    config.exploration_config = {}
    return config


def save_replay_data(episode_data, output_dir, episode_idx):
    """保存回放数据"""
    output_path = Path(output_dir) / f"episode_{episode_idx:03d}.json"
    
    # 转换numpy数组为列表以便JSON序列化
    serializable_data = {}
    for key, value in episode_data.items():
        if isinstance(value, np.ndarray):
            serializable_data[key] = value.tolist()
        elif isinstance(value, list) and len(value) > 0 and isinstance(value[0], np.ndarray):
            serializable_data[key] = [v.tolist() for v in value]
        else:
            serializable_data[key] = value
    
    with open(output_path, 'w') as f:
        json.dump(serializable_data, f, indent=2)
    
    print(f"保存回放数据到: {output_path}")


def create_video_from_frames(frames, output_path, fps=30):
    """从帧序列创建视频文件"""
    try:
        import cv2
        
        if len(frames) == 0:
            print("没有帧数据，跳过视频创建")
            return
        
        height, width, _ = frames[0].shape
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        for frame in frames:
            # 确保帧是BGR格式
            if frame.shape[2] == 3:  # RGB
                frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            else:
                frame_bgr = frame
            out.write(frame_bgr)
        
        out.release()
        print(f"视频保存到: {output_path}")
        
    except ImportError:
        print("OpenCV未安装，跳过视频创建")
    except Exception as e:
        print(f"创建视频时出错: {e}")


def evaluate_agent(args):
    """评估智能体并生成回放文件"""
    print(f"开始评估智能体...")
    print(f"检查点路径: {args.checkpoint_path}")
    print(f"环境: {args.env_name}")
    print(f"回合数: {args.num_episodes}")
    print(f"渲染模式: {args.render_mode}")
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 初始化Ray
    if not ray.is_initialized():
        ray.init()
    
    # 创建配置
    config = create_eval_config(args)
    
    # 创建算法实例
    algo = PPO(config=config)
    
    # 从检查点恢复
    print(f"从检查点恢复: {args.checkpoint_path}")
    algo.restore(args.checkpoint_path)
    
    # 创建环境
    # env = algo.get_policy().config["env"]
    # env_name = env if isinstance(env, str) else env.__name__
    
    # 设置随机种子
    # np.random.seed(args.seed)
    
    # 评估统计
    total_rewards = []
    episode_lengths = []
    
    for episode_idx in range(args.num_episodes):
        print(f"\n开始第 {episode_idx + 1}/{args.num_episodes} 回合")
        
        # 创建环境实例
        env_instance = gym.make(args.env_name, render_mode="rgb_array")
        # env_instance.seed(args.seed + episode_idx)
        
        obs, info = env_instance.reset(seed=args.seed)
        done = False
        truncated = False
        episode_reward = 0
        episode_length = 0
        
        # 存储回合数据
        episode_data = {
            "observations": [],
            "actions": [],
            "rewards": [],
            "dones": [],
            "infos": [],
            "frames": [] if args.render_mode == "rgb_array" else None
        }
        
        while not (done or truncated):
            # 获取动作
            # print("obs: ", obs)
            torch_obs_batch = torch.from_numpy(obs).float()
            if len(torch_obs_batch.shape) == 1:
                torch_obs_batch = torch_obs_batch.unsqueeze(0)
            # print("torch_obs_batch: ", torch_obs_batch)
            
            # 获取动作分布参数
            action_dist_inputs = algo.get_module("default_policy").forward_inference({"obs": torch_obs_batch})["action_dist_inputs"]
            # print("action_dist_inputs: ", action_dist_inputs)
            # print("action_dist_inputs.shape: ", action_dist_inputs.shape)
            
            # 对于连续动作空间，action_dist_inputs通常是[mean, log_std]
            # 获取动作均值
            if len(action_dist_inputs.shape) == 2:
                # 如果输出是 [batch_size, action_dim*2] (mean + log_std)
                action_dim = action_dist_inputs.shape[1] // 2
                action_mean = action_dist_inputs[0, :action_dim]
                action_log_std = action_dist_inputs[0, action_dim:]
            else:
                # 如果输出是 [batch_size, action_dim]
                action_mean = action_dist_inputs[0]
                action_log_std = None
            
            
            # # 根据是否探索来决定动作
            # if args.explore and action_log_std is not None:
            #     # 探索模式：从分布中采样
            #     action_std = torch.exp(action_log_std)
            #     action = torch.normal(action_mean, action_std)
            # else:
            #     # 非探索模式：使用均值
            #     action = action_mean
            
            action_std = torch.exp(action_log_std)
            action = torch.normal(action_mean, action_std)
            
            # 确保动作在有效范围内
            action = torch.clamp(action, -2.0, 2.0)
            action = action.detach().numpy()
            
            
            # 执行动作
            next_obs, reward, done, truncated, info = env_instance.step(action)
            
            # 存储数据
            episode_data["observations"].append(obs)
            episode_data["actions"].append(action)
            episode_data["rewards"].append(reward)
            episode_data["dones"].append(done)
            episode_data["infos"].append(info)
            
            # 保存渲染帧
            if args.render_mode == "rgb_array":
                try:
                    frame = env_instance.render()
                    # print("frame: ", frame)
                    if frame is not None:
                        episode_data["frames"].append(frame)
                except Exception as e:
                    print(f"渲染帧时出错: {e}")
            
            obs = next_obs
            episode_reward += reward
            episode_length += 1
        
        # 保存回放数据
        episode_data["total_reward"] = episode_reward
        episode_data["episode_length"] = episode_length
        episode_data["episode_idx"] = episode_idx
        
        save_replay_data(episode_data, output_dir, episode_idx)
        
        # 创建视频文件
        # if args.save_video and args.render_mode == "rgb_array" and episode_data["frames"]:
        video_path = output_dir / f"episode_{episode_idx:03d}.mp4"
        create_video_from_frames(episode_data["frames"], str(video_path), args.video_fps)
        
        # 更新统计
        total_rewards.append(episode_reward)
        episode_lengths.append(episode_length)
        
        print(f"回合 {episode_idx + 1} 完成:")
        print(f"  总奖励: {episode_reward:.2f}")
        print(f"  回合长度: {episode_length}")
        
        env_instance.close()
    
    # 打印评估统计
    print(f"\n评估完成!")
    print(f"平均奖励: {np.mean(total_rewards):.2f} ± {np.std(total_rewards):.2f}")
    print(f"平均回合长度: {np.mean(episode_lengths):.2f} ± {np.std(episode_lengths):.2f}")
    print(f"最小奖励: {np.min(total_rewards):.2f}")
    print(f"最大奖励: {np.max(total_rewards):.2f}")
    print(f"回放文件保存在: {output_dir}")
    
    # 保存评估统计
    stats = {
        "total_rewards": total_rewards,
        "episode_lengths": episode_lengths,
        "mean_reward": float(np.mean(total_rewards)),
        "std_reward": float(np.std(total_rewards)),
        "mean_length": float(np.mean(episode_lengths)),
        "std_length": float(np.std(episode_lengths)),
        "min_reward": float(np.min(total_rewards)),
        "max_reward": float(np.max(total_rewards)),
        "num_episodes": args.num_episodes,
        "checkpoint_path": args.checkpoint_path,
        "env_name": args.env_name,
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
    }
    
    stats_path = output_dir / "evaluation_stats.json"
    with open(stats_path, 'w') as f:
        json.dump(stats, f, indent=2)
    
    print(f"评估统计保存到: {stats_path}")


if __name__ == "__main__":
    args = parse_args()
    
    # 验证检查点路径
    if not os.path.exists(args.checkpoint_path):
        print(f"错误: 检查点路径不存在: {args.checkpoint_path}")
        exit(1)
    
    try:
        evaluate_agent(args)
    except Exception as e:
        print(f"评估过程中出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if ray.is_initialized():
            ray.shutdown()
