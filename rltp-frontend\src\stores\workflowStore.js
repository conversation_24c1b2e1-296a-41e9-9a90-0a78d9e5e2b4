import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { workflow<PERSON>pi, TRAINING_TYPES, WORKFLOW_STATUS, TRAINING_STATUS } from 'src/services/workflowApi'
import { Notify } from 'quasar'

export const useWorkflowStore = defineStore('workflow', () => {
  // 当前工作流信息
  const currentWorkflow = ref(null)
  
  // 工作流列表
  const workflowList = ref([])
  
  // 加载状态
  const loading = ref(false)
  
  // 训练指标数据
  const trainingMetrics = ref({})
  
  // 训练日志
  const trainingLogs = ref([])

  // 计算属性
  const currentTaskId = computed(() => currentWorkflow.value?.task_id)
  const currentStep = computed(() => currentWorkflow.value?.current_step || 1)
  const trainingType = computed(() => currentWorkflow.value?.training_type)
  const workflowStatus = computed(() => currentWorkflow.value?.status)
  const trainingStatus = computed(() => currentWorkflow.value?.training_status)

  /**
   * 创建新的训练工作流
   */
  const createWorkflow = async (workflowData) => {
    try {
      loading.value = true
      const data = await workflowApi.createWorkflow(workflowData)

      // 检查响应是否存在 (axios拦截器已经返回了data，不是response.data)
      if (!data) {
        throw new Error('服务器响应异常')
      }

      if (data.success) {
        currentWorkflow.value = {
          task_id: data.task_id,
          workflow_id: data.workflow_id,
          model_info_id: data.model_info_id,
          name: workflowData.name,
          training_type: workflowData.training_type,
          task_type: workflowData.task_type,
          current_step: data.current_step,
          status: data.status,
          training_status: 'not_started'
        }

        Notify.create({
          type: 'positive',
          message: data.message
        })

        return data
      } else {
        throw new Error(data.error || '创建失败')
      }
    } catch (error) {
      console.error('创建工作流失败:', error)
      Notify.create({
        type: 'negative',
        message: error.error || error.message || '创建工作流失败'
      })
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 开始数据分析
   */
  const startDataAnalysis = async (datasetConfig) => {
    try {
      loading.value = true
      const data = await workflowApi.startDataAnalysis({
        task_id: currentTaskId.value,
        dataset_config: datasetConfig
      })

      if (data.success) {
        Notify.create({
          type: 'positive',
          message: data.message
        })
        return data.analysis_result
      } else {
        throw new Error(data.error || '数据分析失败')
      }
    } catch (error) {
      console.error('数据分析失败:', error)
      Notify.create({
        type: 'negative',
        message: error.error || error.message || '数据分析失败'
      })
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 保存步骤数据 (前两步的下一步按钮)
   */
  const saveStepData = async (stepNumber, stepData) => {
    try {
      loading.value = true
      const data = await workflowApi.saveStepData({
        task_id: currentTaskId.value,
        step_number: stepNumber,
        step_data: stepData
      })

      if (data.success) {
        // 更新当前工作流状态
        if (currentWorkflow.value) {
          currentWorkflow.value.current_step = data.current_step
          currentWorkflow.value.status = data.status
        }

        Notify.create({
          type: 'positive',
          message: data.message
        })

        return data
      } else {
        throw new Error(data.error || '保存失败')
      }
    } catch (error) {
      console.error('保存步骤数据失败:', error)
      Notify.create({
        type: 'negative',
        message: error.error || error.message || '保存步骤数据失败'
      })
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 提交训练 (第三步开始训练)
   */
  const submitTraining = async (trainingConfig) => {
    try {
      loading.value = true
      const response = await workflowApi.submitTraining({
        task_id: currentTaskId.value,
        training_config: trainingConfig
      })
      
      if (response.data.success) {
        // 更新当前工作流状态
        if (currentWorkflow.value) {
          currentWorkflow.value.status = response.data.status
          currentWorkflow.value.training_status = response.data.training_status
          currentWorkflow.value.training_task_id = response.data.training_task_id
        }
        
        Notify.create({
          type: 'positive',
          message: response.data.message
        })
        
        return response.data
      } else {
        throw new Error(response.data.error || '提交训练失败')
      }
    } catch (error) {
      console.error('提交训练失败:', error)
      Notify.create({
        type: 'negative',
        message: error.response?.data?.error || error.message || '提交训练失败'
      })
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 训练控制 (暂停/恢复/停止)
   */
  const controlTraining = async (action) => {
    try {
      loading.value = true
      const response = await workflowApi.controlTraining({
        task_id: currentTaskId.value,
        action: action
      })
      
      if (response.data.success) {
        // 更新当前工作流状态
        if (currentWorkflow.value) {
          currentWorkflow.value.status = response.data.status
          currentWorkflow.value.training_status = response.data.training_status
        }
        
        Notify.create({
          type: 'positive',
          message: response.data.message
        })
        
        return response.data
      } else {
        throw new Error(response.data.error || '操作失败')
      }
    } catch (error) {
      console.error('训练控制失败:', error)
      Notify.create({
        type: 'negative',
        message: error.response?.data?.error || error.message || '训练控制失败'
      })
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 完成步骤 (第三步的下一步)
   */
  const completeStep = async (stepNumber, finalMetrics = {}) => {
    try {
      loading.value = true
      const data = await workflowApi.completeStep({
        task_id: currentTaskId.value,
        step_number: stepNumber,
        final_metrics: finalMetrics
      })

      if (data.success) {
        // 更新当前工作流状态
        if (currentWorkflow.value) {
          currentWorkflow.value.current_step = data.current_step
          currentWorkflow.value.status = data.status
          currentWorkflow.value.training_status = data.training_status
        }

        Notify.create({
          type: 'positive',
          message: data.message
        })

        return data
      } else {
        throw new Error(data.error || '完成步骤失败')
      }
    } catch (error) {
      console.error('完成步骤失败:', error)
      Notify.create({
        type: 'negative',
        message: error.error || error.message || '完成步骤失败'
      })
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取工作流列表
   */
  const fetchWorkflowList = async (params = {}) => {
    try {
      loading.value = true
      const response = await workflowApi.getWorkflowList(params)
      
      if (response.data.success) {
        workflowList.value = response.data.data.workflows
        return response.data.data
      } else {
        throw new Error(response.data.error || '获取列表失败')
      }
    } catch (error) {
      console.error('获取工作流列表失败:', error)
      Notify.create({
        type: 'negative',
        message: error.response?.data?.error || error.message || '获取工作流列表失败'
      })
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 跳转到指定步骤
   */
  const jumpToStep = async (taskId) => {
    try {
      loading.value = true

      const data = await workflowApi.jumpToStep({ task_id: taskId })

      if (data.success) {
        // 设置当前工作流
        currentWorkflow.value = data.data

        // 如果有训练数据，更新训练指标和日志
        if (data.data.training_data) {
          trainingMetrics.value = data.data.training_data.training_metrics || {}
          trainingLogs.value = data.data.training_data.training_logs || []
        }

        return data.data
      } else {
        throw new Error(data.error || '跳转失败')
      }
    } catch (error) {
      console.error('跳转到步骤失败:', error)
      Notify.create({
        type: 'negative',
        message: error.error || error.message || '跳转到步骤失败'
      })
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新训练指标
   */
  const updateTrainingMetrics = async (metrics, logEntry = null) => {
    try {
      const response = await workflowApi.updateTrainingMetrics({
        task_id: currentTaskId.value,
        metrics: metrics,
        log_entry: logEntry
      })
      
      if (response.data.success) {
        // 更新本地数据
        trainingMetrics.value = { ...trainingMetrics.value, ...metrics }
        
        if (logEntry) {
          trainingLogs.value.push({
            timestamp: new Date().toISOString(),
            content: logEntry
          })
          
          // 只保留最近100条日志
          if (trainingLogs.value.length > 100) {
            trainingLogs.value = trainingLogs.value.slice(-100)
          }
        }
        
        return response.data
      }
    } catch (error) {
      console.error('更新训练指标失败:', error)
      // 这里不显示错误通知，因为这是后台更新
    }
  }

  /**
   * 重置当前工作流
   */
  const resetCurrentWorkflow = () => {
    currentWorkflow.value = null
    trainingMetrics.value = {}
    trainingLogs.value = []
  }

  /**
   * 设置当前工作流
   */
  const setCurrentWorkflow = (workflow) => {
    currentWorkflow.value = workflow
  }

  return {
    // 状态
    currentWorkflow,
    workflowList,
    loading,
    trainingMetrics,
    trainingLogs,
    
    // 计算属性
    currentTaskId,
    currentStep,
    trainingType,
    workflowStatus,
    trainingStatus,
    
    // 方法
    createWorkflow,
    startDataAnalysis,
    saveStepData,
    submitTraining,
    controlTraining,
    completeStep,
    fetchWorkflowList,
    jumpToStep,
    updateTrainingMetrics,
    resetCurrentWorkflow,
    setCurrentWorkflow
  }
})
