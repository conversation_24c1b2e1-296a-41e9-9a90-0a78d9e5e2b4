<!--
 * @Author: Szc
 * @Date: 2025-08-20 09:05:18
 * @LastEditors: Szc
 * @LastEditTime: 2025-08-20 09:08:53
 * @Description: 
-->
<template>
    <div class="btn-container">
        <div class="flexC" v-for="(item,index) in menuItems" :key="item.id">
            <q-btn 
                class="styleBtn" 
                :class="getBtnClass(item.id)"
                no-caps
                flat
                @click="handleBtnClick(item.id)"
                :disabled="!canClickStep(item.id)"
            >
                <div class="btn-content">
                    <img :src="getBtnIcon(item.id)" :alt="item.label" class="btn-icon" />
                    <span class="btn-text">{{ item.label }}</span>
                </div>
            </q-btn>
            <div class="line" :class="{ 'line-active': isLineActive(index) }" v-if="index != menuItems.length - 1"></div>
        </div>
    </div>

</template>


<script setup>
import { ref, defineExpose } from 'vue'

// 当前步骤状态
const currentStep = ref(1)

const menuItems = ref([
    {
        id: 1,
        label: '格式转换',
    },
    {
        id: 2,
        label: '数据生成',
    },
    {
        id: 3,
        label: '数据清洗',
    },
    {
        id: 4,
        label: '数据增强',
    },
    {
        id: 5,
        label: '生成数据集',
    },
])

// 获取按钮状态类
const getBtnClass = (stepId) => {
    if (stepId < currentStep.value) {
        return 'btn-completed' // 已完成状态
    } else if (stepId === currentStep.value) {
        return 'btn-current' // 当前状态
    } else {
        return 'btn-default' // 默认状态
    }
}

// 获取按钮图标
const getBtnIcon = (stepId) => {
    const stepNum = stepId
    
    if (stepId < currentStep.value) {
        // 已完成状态 - 所有完成的都用同一个图标
        return new URL(`../../../assets/images/dlc_ywc_dg.png`, import.meta.url).href
    } else if (stepId === currentStep.value) {
        // 当前状态 - dlc_1.png, dlc_2.png 等
        return new URL(`../../../assets/images/dlc_${stepNum}.png`, import.meta.url).href
    } else {
        // 默认状态 - dlc_1_1.png, dlc_2_1.png 等
        return new URL(`../../../assets/images/dlc_${stepNum}_1.png`, import.meta.url).href
    }
}

// 判断连接线是否应该高亮
const isLineActive = (index) => {
    // index是从0开始的，menuItems[index]对应的stepId是index+1
    // 当前步骤前面的连接线应该高亮
    // 比如当前步骤是3，那么第1步和第2步后面的连接线(index为0和1)应该高亮
    const stepId = index + 1
    return stepId < currentStep.value
}

// 下一步方法
const nextStep = () => {
    if (currentStep.value < menuItems.value.length) {
        currentStep.value++
    }
}

// 上一步方法
const prevStep = () => {
    if (currentStep.value > 1) {
        currentStep.value--
    }
}

// 跳转到指定步骤
const goToStep = (step) => {
    if (step >= 1 && step <= menuItems.value.length) {
        currentStep.value = step
    }
}

// 判断步骤是否可以点击（只能往前跳，不能往后跳）
const canClickStep = (stepId) => {
    return stepId <= currentStep.value
}

// 处理按钮点击事件
const handleBtnClick = (stepId) => {
    if (canClickStep(stepId)) {
        goToStep(stepId)
    }
}

// 暴露方法给父组件使用
defineExpose({
    currentStep,
    nextStep,
    prevStep,
    goToStep
})

</script>


<style lang="scss" scoped>
.btn-container {
    width: 100%;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
}

.flexC {
    display: flex;
    align-items: center;
}

.styleBtn {
    min-width: 1.875rem !important;
    height: .5rem !important;
    padding: 0 !important;
    border-radius: 0 !important;
    font-size: .2rem !important;
    transition: all 0.3s ease;
    
    // 重置q-btn默认样式
    :deep(.q-btn__content) {
        line-height: .5rem;
        color: inherit;
        padding: 0;
    }
    
    // 移除q-btn默认的hover和focus效果
    &:before {
        display: none !important;
    }
    
    .btn-content {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: .125rem;
        width: 100%;
        height: 100%;
        
        .btn-icon {
            width: .25rem;
            height: .25rem;
            flex-shrink: 0;
        }
        
        .btn-text {
            font-size: .2rem;
            white-space: nowrap;
        }
    }
}

// 默认状态
.btn-default {
    background: url('../../../assets/images/dlc_djx.png') no-repeat center center !important;
    background-size: cover !important;
    color: #b1afad !important;
    
    :deep(.q-btn__content) {
        color: #b1afad !important;
    }
    
    .btn-content .btn-text {
        color: #b1afad !important;
    }
}

// 当前状态
.btn-current {
    background: url('../../../assets/images/dlc_jxz.png') no-repeat center center !important;
    background-size: cover !important;
    color: #ffa363 !important;
    
    :deep(.q-btn__content) {
        color: #ffa363 !important;
    }
    
    .btn-content .btn-text {
        color: #ffa363 !important;
    }
}

// 已完成状态
.btn-completed {
    background: url('../../../assets/images/dlc_ywc.png') no-repeat center center !important;
    background-size: cover !important;
    color: #4ab4ff !important;
    
    :deep(.q-btn__content) {
        color: #4ab4ff !important;
    }
    
    .btn-content .btn-text {
        color: #4ab4ff !important;
    }
    
    // 禁用状态
    &:disabled {
        cursor: not-allowed !important;
        opacity: 0.5 !important;
        
        .btn-content .btn-text {
            color: #666 !important;
        }
        
        &:hover {
            transform: none !important;
        }
    }
}

.line {
    width: .5rem;
    height: .025rem;
    background: #585e6c;
    transition: background-color 0.3s ease;
    
    &.line-active {
        background: #3b6992;
    }
}
</style>