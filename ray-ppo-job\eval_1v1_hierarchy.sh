source /opt/conda/bin/activate
conda activate ray

python ray_eval_jsbsim1v1_record.py \
    --checkpoint-path /home/<USER>/xiangshuai/projects/xs/ray-ppo-job/ray_results/jsbsim-1v1-bsl-hierarchy_20250628-014652/PPO_JSBSim-Combat_b6885_00000_0_2025-06-28_01-46-54/checkpoint_000035 \
    --env-name 1v1/ShootMissile/HierarchyVsBaseline --experiment-name jsbsim-1v1-bsl-hierarchy --output-dir /home/<USER>/xiangshuai/projects/xs/ray-ppo-job/eval_results \
    --num-gpus 0 > eval_1v1_hierarchy.log 2>&1
