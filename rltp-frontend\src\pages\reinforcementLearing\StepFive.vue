<!--
 * @Author: Szc
 * @Date: 2025-08-07 10:45:00
 * @LastEditors: Szc
 * @LastEditTime: 2025-08-13 11:35:28
 * @Description: 
-->
<template>
  <div class="content">
    <div class="top">
      <div class="left">
        <!-- 选择测试环境 -->
        <div class="section">
          <div class="section-header">
            <span class="section-title">选择测试环境：</span>
          </div>

          <div class="upload-section">
            <div class="upload-list">
              <!-- 已上传的图片 -->
              <div
                v-for="(image, index) in uploadedImages"
                :key="index"
                class="upload-item image-preview"
              >
                <img :src="image.url" class="preview-image" alt="预览" />
                <div class="delete-btn" @click="removeUploadedImage(index)">
                  <img
                    src="../../assets/images/icon_btn_delete.png"
                    alt="删除"
                    class="delete-icon"
                  />
                </div>
              </div>

              <!-- 上传按钮 -->
              <div class="upload-item">
                <el-upload
                  class="test-file-uploader"
                  action=""
                  :auto-upload="false"
                  :show-file-list="false"
                  :on-change="handleFileUpload"
                  accept=".jpeg,.jpg,.png,.tiff,.tif,.bmp"
                  multiple
                >
                  <div class="upload-area">
                    <img
                      src="../../assets/images/icon_sc.png"
                      alt="上传"
                      class="upload-icon"
                    />
                  </div>
                </el-upload>
              </div>
            </div>

            <div class="upload-tips">
              可以上传测试图像（.jpeg/.jpg/.png/.tiff/.tif/.bmp
              文件格式），文件体积不超过100MB，文件数量不超过8个
              <br><strong>注意：强化学习模型主要用于环境交互，图像测试仅用于演示</strong>
            </div>
          </div>
        </div>

        <!-- 选择模型 -->
        <div class="model-section">
          <div class="model-row">
            <span class="model-title">选择模型：</span>
            <q-select
              v-if="modelOptions.length > 0"
              v-model="selectedModel"
              :options="modelOptions"
              :label="selectedModel ? '' : '请选择模型'"
              :rules="[(val) => !!val || '请选择模型']"
              outlined
              emit-value
              map-options
              class="model-selector"
            />
            <q-select
              v-else
              filled
              v-model="selectedModel"
              :options="[]"
              option-value="id"
              option-label="name"
              :placeholder="getTrainingTaskId() ? '暂无训练完成的模型' : '请先完成训练任务'"
              emit-value
              map-options
              class="model-selector"
              disable
            />
          </div>
        </div>

        <!-- 按钮区域 -->
        <div class="button-section">
          <q-btn class="action-btn reset-btn" flat no-caps @click="resetAll">
            重置
          </q-btn>
          <q-btn
            class="action-btn run-btn"
            flat
            no-caps
            :loading="inferenceLoading"
            :disable="inferenceLoading || !canRunInference"
            @click="runTest"
          >
            {{
              inferenceLoading
                ? uploadedImages.length > 1
                  ? "批量推理中..."
                  : "推理中..."
                : "运行"
            }}
          </q-btn>
          <q-btn class="action-btn save-btn" flat no-caps @click="saveTest"> 保存 </q-btn>
        </div>
      </div>

      <div class="right">
        <!-- 运行结果 -->
        <div class="result-section">
          <div class="result-header">
            <img class="arrow-icon" src="../../assets/images/icon_dz.png" alt="" />
            <span class="result-title">运行结果</span>
          </div>

          <div class="result-content">
            <!-- 空状态 -->
            <div v-if="!showResult && !inferenceLoading" class="empty-result">
              <div class="empty-text">请上传测试文件并点击运行按钮</div>
            </div>

            <!-- 推理加载状态 -->
            <div v-if="inferenceLoading" class="loading-result">
              <div class="loading-spinner">
                <q-spinner-dots color="primary" size="3rem" />
              </div>
              <div class="loading-text">强化学习模型测试中，请稍候...</div>
              <div class="loading-tips">模型测试任务正在后台处理中，请耐心等待</div>
            </div>

            <!-- 单张推理结果 -->
            <div
              v-if="showResult && !inferenceLoading && !batchResults.batch_results"
              class="result-display"
            >
              <div class="image-container">
                <img :src="resultImageSrc" alt="运行结果" class="result-image" />
                <div class="zoom-icon" @click="openImagePreview(resultImageSrc)">
                  <q-icon name="zoom_in" size="0.8rem" color="white" />
                </div>
              </div>
              <div class="result-info">
                <div class="info-item">
                  <span class="info-label">模型响应时间:</span>
                  <span class="info-value">{{ inferenceTime }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">内存占用:</span>
                  <span class="info-value">{{ memoryUsage }}</span>
                </div>
              </div>
            </div>

            <!-- 批量推理结果 -->
            <div
              v-if="showResult && !inferenceLoading && batchResults.batch_results"
              class="batch-result-display"
            >
              <div class="batch-summary">
                <div class="summary-item">
                  <span class="summary-label">处理图片:</span>
                  <span class="summary-value"
                    >{{ batchResults.processed_images || 0 }} /
                    {{ batchResults.total_images || 0 }}</span
                  >
                </div>
                <div class="summary-item">
                  <span class="summary-label">总检测数:</span>
                  <span class="summary-value">{{ totalDetections }}</span>
                </div>
                <div class="summary-item">
                  <span class="summary-label">平均推理时间:</span>
                  <span class="summary-value">{{ averageInferenceTime }}</span>
                </div>
              </div>

              <div class="batch-results-grid">
                <div
                  v-for="(result, index) in batchResults.batch_results"
                  :key="index"
                  class="batch-result-item"
                  @click="selectBatchResult(index)"
                  :class="{ selected: selectedBatchIndex === index }"
                >
                  <div v-if="result.image_data" class="batch-image-container">
                    <img
                      :src="`data:image/${result.image_format || 'jpeg'};base64,${
                        result.image_data
                      }`"
                      alt="批量推理结果"
                      class="batch-result-image"
                    />
                    <div
                      class="batch-zoom-icon"
                      @click.stop="
                        openImagePreview(
                          `data:image/${result.image_format || 'jpeg'};base64,${
                            result.image_data
                          }`
                        )
                      "
                    >
                      <q-icon name="zoom_in" size="0.6rem" color="white" />
                    </div>
                  </div>
                  <div v-else class="batch-result-placeholder">
                    <span>图片 {{ index + 1 }}</span>
                  </div>
                  <div class="batch-result-info">
                    <span class="detection-count"
                      >检测: {{ result.detection_count || 0 }}</span
                    >
                  </div>
                </div>
              </div>

              <!-- 选中的批量结果详情 -->
              <div v-if="selectedBatchResult" class="selected-result-detail">
                <div class="detail-header">
                  <span class="detail-title">图片 {{ selectedBatchIndex + 1 }} 详情</span>
                </div>
                <div class="detail-content">
                  <div
                    v-if="selectedBatchResult.image_data"
                    class="detail-image-container"
                  >
                    <img
                      :src="`data:image/${
                        selectedBatchResult.image_format || 'jpeg'
                      };base64,${selectedBatchResult.image_data}`"
                      alt="选中结果"
                      class="detail-image"
                    />
                    <div
                      class="detail-zoom-icon"
                      @click="
                        openImagePreview(
                          `data:image/${
                            selectedBatchResult.image_format || 'jpeg'
                          };base64,${selectedBatchResult.image_data}`
                        )
                      "
                    >
                      <q-icon name="zoom_in" size="0.7rem" color="white" />
                    </div>
                  </div>
                  <div class="detail-info">
                    <div class="detail-item">
                      <span class="detail-label">检测数量:</span>
                      <span class="detail-value">{{
                        selectedBatchResult.detection_count || 0
                      }}</span>
                    </div>
                    <div
                      v-if="
                        selectedBatchResult.detections &&
                        selectedBatchResult.detections.length > 0
                      "
                      class="detections-list"
                    >
                      <div class="detections-header">检测结果:</div>
                      <div
                        v-for="(detection, idx) in selectedBatchResult.detections.slice(
                          0,
                          5
                        )"
                        :key="idx"
                        class="detection-item"
                      >
                        <span class="detection-class">{{ detection.class }}</span>
                        <span class="detection-confidence"
                          >{{ (detection.confidence * 100).toFixed(1) }}%</span
                        >
                      </div>
                      <div
                        v-if="selectedBatchResult.detections.length > 5"
                        class="more-detections"
                      >
                        还有 {{ selectedBatchResult.detections.length - 5 }} 个检测结果...
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 下一步按钮 -->
          <div class="bottom">
            <div class="next">
              <q-btn
                class="prevBtn roundBox"
                color="grey-7"
                label="上一步"
                @click="prevStep"
              />
              <q-btn
                class="nextBtn roundBox"
                color="primary"
                label="完成"
                @click="nextStep"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 图片预览对话框 -->
  <q-dialog v-model="imagePreviewVisible" class="image-preview-dialog">
    <q-card class="image-preview-card">
      <q-card-section class="image-preview-header">
        <div class="preview-title">图片预览</div>
        <q-btn
          flat
          round
          dense
          icon="close"
          @click="imagePreviewVisible = false"
          class="close-btn"
        />
      </q-card-section>

      <q-card-section class="image-preview-content">
        <img :src="previewImageSrc" alt="预览图片" class="preview-image" />
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, defineEmits, onMounted, computed } from "vue";
import { usePlugin } from "src/composables/plugin.js";
import { api } from "boot/axios";
import { LocalStorage as quasarLocalStorage } from 'quasar';

// 定义事件
const emit = defineEmits(["next-step", "prev-step"]);

// 引入通知插件
const { notify } = usePlugin();

// 强化学习训练API
const rlTrainingApi = {
  async getModelList(trainingId) {
    const response = await api.get(`/backend/training/rl/${trainingId}/models`)
    return response.data
  }
};

// 当前训练ID（从路由或父组件获取）
const currentTrainingId = ref(getTrainingTaskId() || ''); // 从存储中获取训练任务ID

// 上传的图片列表
const uploadedImages = ref([]);

// 选中的模型
const selectedModel = ref("");

// 是否显示运行结果
const showResult = ref(false);

// 推理加载状态
const inferenceLoading = ref(false);

// 批量推理结果
const batchResults = ref({});

// 选中的批量结果索引
const selectedBatchIndex = ref(0);

// 图片预览相关
const imagePreviewVisible = ref(false);
const previewImageSrc = ref("");

// 模型选项
const modelOptions = ref([]);

// 获取模型列表
async function fetchModelOptions() {
    try {
        console.log('🔍 StepFive: 开始获取模型列表')

        // 获取训练任务ID
        const taskId = getTrainingTaskId()
        console.log('🔍 StepFive: 获取到的训练任务ID:', taskId)

        if (!taskId) {
            console.log('❌ StepFive: 未找到训练任务ID，不显示任何模型选项')
            modelOptions.value = []
            return
        }

        console.log('📡 StepFive: 准备调用API获取模型列表，任务ID:', taskId)
        const apiUrl = `backend/training/rl/models?task_id=${taskId}`
        console.log('📡 StepFive: API URL:', apiUrl)

        // 根据任务ID查询模型列表（与第四步保持一致）
        const response = await api.get(apiUrl)
        console.log('📡 StepFive: API响应完整数据:', response)
        console.log('📡 StepFive: API响应状态:', response?.status)
        console.log('📡 StepFive: API响应数据:', response?.data)

        if (response && response.data && Array.isArray(response.data)) {
            if (response.data.length > 0) {
                // 处理API返回的模型数据，在模型名称后加上.om后缀
                modelOptions.value = response.data.map(model => ({
                    label: `${model.model_name || `模型 ${model.id}`}.om`,
                    value: model.id
                }))
                console.log(`成功获取到 ${modelOptions.value.length} 个模型`)
            } else {
                console.log(`任务ID ${taskId} 暂无训练完成的模型`)
                modelOptions.value = []
            }
        } else if (response && response.success && response.data && Array.isArray(response.data)) {
            if (response.data.length > 0) {
                // 处理API返回的成功响应格式，在模型名称后加上.om后缀
                modelOptions.value = response.data.map(model => ({
                    label: `${model.model_name || `模型 ${model.id}`}.om`,
                    value: model.id
                }))
                console.log(`成功获取到 ${modelOptions.value.length} 个模型`)
            } else {
                console.log(`任务ID ${taskId} 暂无训练完成的模型`)
                modelOptions.value = []
            }
        } else {
            console.log(`任务ID ${taskId} 未获取到模型数据`)
            modelOptions.value = []
        }
    } catch (error) {
        console.error('获取模型列表失败:', error)

        // 获取训练任务ID用于错误提示
        const taskId = getTrainingTaskId()

        if (!taskId) {
            console.log('无训练任务ID，不显示模型选项')
            modelOptions.value = []
        } else {
            console.log('API调用失败，不显示模型选项')
            modelOptions.value = []
        }
    }
}

// 处理文件上传
const handleFileUpload = (uploadFile, uploadFiles) => {
  // 如果是多选上传，处理所有新增的文件
  const filesToProcess = uploadFiles ? uploadFiles.slice(-1) : [uploadFile];

  let successCount = 0;

  filesToProcess.forEach((file) => {
    if (file && file.raw) {
      // 验证文件类型
      const allowedTypes = /\.(jpeg|jpg|png|tiff|tif|bmp)$/i;
      if (!allowedTypes.test(file.name)) {
        notify(
          `文件 ${file.name} 格式不支持，请上传 .jpeg/.jpg/.png/.tiff/.tif/.bmp 格式的图片`,
          "negative"
        );
        return;
      }

      // 验证文件大小 (100MB)
      if (file.raw.size / 1024 / 1024 > 100) {
        notify(`文件 ${file.name} 大小不能超过100MB`, "negative");
        return;
      }

      // 验证文件数量
      if (uploadedImages.value.length >= 8) {
        notify("最多只能上传8个文件", "negative");
        return;
      }

      // 检查是否已经上传过相同文件
      const exists = uploadedImages.value.some((img) => img.name === file.name);
      if (exists) {
        notify(`文件 ${file.name} 已存在`, "negative");
        return;
      }

      // 创建预览URL并添加到列表
      const imageItem = {
        name: file.name,
        url: URL.createObjectURL(file.raw),
        raw: file.raw,
      };
      uploadedImages.value.push(imageItem);
      successCount++;
      console.log("文件上传成功:", file.name);
    }
  });

  if (successCount > 0) {
    notify(`成功上传 ${successCount} 个文件`, "positive");
  }
};

// 删除上传的图片
const removeUploadedImage = (index) => {
  if (uploadedImages.value[index]) {
    URL.revokeObjectURL(uploadedImages.value[index].url);
    uploadedImages.value.splice(index, 1);
  }
  // 如果删除后没有图片了，隐藏结果
  if (uploadedImages.value.length === 0) {
    showResult.value = false;
  }
};

// 重置所有
const resetAll = () => {
  // 清空所有上传的图片
  uploadedImages.value.forEach((image) => {
    URL.revokeObjectURL(image.url);
  });
  uploadedImages.value = [];
  selectedModel.value = "";
  showResult.value = false;
  inferenceLoading.value = false;

  // 重置批量推理相关状态
  batchResults.value = {};
  selectedBatchIndex.value = 0;

  console.log("已重置所有内容");
};

// 结果数据
const resultImageSrc = ref("../../assets/images/demoImage.png");
const inferenceTime = ref("15ms");
const memoryUsage = ref("1.2GB");

// 计算属性
const canRunInference = computed(() => {
  if (!selectedModel.value) return false;
  return uploadedImages.value.length > 0;
});

const selectedBatchResult = computed(() => {
  if (batchResults.value.batch_results && selectedBatchIndex.value >= 0) {
    return batchResults.value.batch_results[selectedBatchIndex.value];
  }
  return null;
});

const totalDetections = computed(() => {
  if (!batchResults.value.batch_results) return 0;
  return batchResults.value.batch_results.reduce((total, result) => {
    return total + (result.detection_count || 0);
  }, 0);
});

const averageInferenceTime = computed(() => {
  if (!batchResults.value.batch_results || batchResults.value.batch_results.length === 0)
    return "0ms";
  // 这里可以根据实际情况计算平均推理时间
  return "25ms"; // 暂时使用固定值
});

// 轮询检查推理状态
const pollInferenceStatus = async (inferenceLogId, maxAttempts = 60, interval = 3000) => {
  let attempts = 0;

  while (attempts < maxAttempts) {
    try {
      console.log(
        `轮询推理状态，第 ${attempts + 1} 次尝试，推理日志ID: ${inferenceLogId}`
      );

      const response = await api.get(
        `http://127.0.0.1:8000/backend/training/models/inference/${inferenceLogId}/status`
      );
      console.log("推理状态响应:", response);

      // 检查推理是否完成
      if (
        response &&
        response.success === true &&
        response.data &&
        response.data.result
      ) {
        const result = response.data.result;

        // 检查状态
        if (result.status === "completed") {
          console.log("推理完成，返回结果");
          return response;
        } else if (result.status === "failed") {
          throw new Error(result.message || "推理失败");
        } else if (result.status === "processing") {
          console.log("推理仍在进行中，继续等待...");
          // 继续轮询，不显示任何提示
        } else {
          console.log(`未知状态: ${result.status}，继续等待...`);
        }
      } else if (response && response.success === false) {
        throw new Error(response.message || "推理失败");
      }

      // 等待指定时间后继续轮询
      await new Promise((resolve) => setTimeout(resolve, interval));
      attempts++;
    } catch (error) {
      console.error("轮询推理状态失败:", error);

      // 如果是网络错误，继续重试
      if (error.code === "ECONNABORTED" || error.message.includes("timeout")) {
        console.log("网络超时，继续重试...");
        await new Promise((resolve) => setTimeout(resolve, interval));
        attempts++;
        continue;
      }

      // 其他错误直接抛出
      throw error;
    }
  }

  // 超时
  throw new Error("推理超时，请稍后重试");
};

// 运行强化学习模型测试
const runTest = async () => {
  if (uploadedImages.value.length === 0) {
    notify("请先上传测试文件", "warning");
    return;
  }

  if (!selectedModel.value) {
    notify("请选择强化学习模型", "warning");
    return;
  }

  try {
    // 显示加载状态
    inferenceLoading.value = true;
    showResult.value = false;

    if (uploadedImages.value.length === 1) {
      // 单张推理逻辑
      await runSingleInference();
    } else {
      // 批量推理逻辑
      await runBatchInference();
    }
  } catch (error) {
    console.error("推理失败:", error);
    notify(error.message || "推理失败", "negative");
    inferenceLoading.value = false;
  }
};

// 单张推理
const runSingleInference = async () => {
  // 获取第一张图片作为测试输入
  const testImage = uploadedImages.value[0];

  // 将图片转换为base64格式
  let imageData = testImage.url;

  // 如果URL是Blob URL，需要转换为base64
  if (imageData.startsWith("blob:")) {
    try {
      const response = await fetch(imageData);
      const blob = await response.blob();

      // 将Blob转换为base64
      const reader = new FileReader();
      imageData = await new Promise((resolve) => {
        reader.onloadend = () => resolve(reader.result);
        reader.readAsDataURL(blob);
      });

      // 移除base64前缀，只保留数据部分
      imageData = imageData.split(",")[1];
    } catch (error) {
      console.error("图片转换失败:", error);
      notify("图片转换失败", "negative");
      inferenceLoading.value = false;
      return;
    }
  }

  console.log("运行单张推理，模型ID:", selectedModel.value, "图片数据已准备");

  // 构建请求参数
  const requestData = {
    model_id: selectedModel.value,
    input_source: imageData,
    confidence_threshold: 0.5,
    iou_threshold: 0.45,
    max_detections: 1000,
    save_result: true,
    return_image: true,
  };

  console.log("单张推理请求参数:", {
    ...requestData,
    input_source: "(base64数据已省略)",
  });

  // 调用统一推理API启动任务
  const startResponse = await api.post(
    "http://127.0.0.1:8000/backend/training/models/inference",
    requestData
  );
  console.log("推理启动响应:", startResponse);

  // 检查启动响应
  if (
    !startResponse ||
    !startResponse.success ||
    !startResponse.data ||
    !startResponse.data.inference_log_id
  ) {
    throw new Error(startResponse?.message || "推理任务启动失败，未返回推理日志ID");
  }

  const inferenceLogId = startResponse.data.inference_log_id;
  console.log("推理任务已启动，推理日志ID:", inferenceLogId);

  // 检查初始状态
  if (
    startResponse.status === "processing" ||
    startResponse.data.result?.status === "processing"
  ) {
    console.log("推理任务正在后台处理中，开始轮询状态...");
    // 静默开始轮询，不显示额外提示
  }

  // 开始轮询推理状态
  const response = await pollInferenceStatus(inferenceLogId);
  // 处理推理结果
  if (response && response.success && response.data && response.data.result) {
    const result = response.data.result;
    console.log("推理结果数据:", result);

    // 显示推理结果
    showResult.value = true;

    // 检查推理状态
    if (result.status === "completed") {
      // 检查是否有图像数据
      if (result.image_data && result.has_image) {
        // 更新结果图片 - 根据后端返回的格式构建完整的data URL
        const imageFormat = result.image_format || "jpeg";
        resultImageSrc.value = `data:image/${imageFormat};base64,${result.image_data}`;

        // 更新推理信息
        if (result.inference_time_ms !== undefined) {
          inferenceTime.value = `${result.inference_time_ms}ms`;
        } else {
          inferenceTime.value = "15ms";
        }

        // 更新检测数量和内存占用信息
        if (result.detection_count !== undefined) {
          // 可以在这里添加检测数量的显示
          console.log(`检测到 ${result.detection_count} 个目标`);
        }

        // 显示内存占用（如果后端提供的话，否则使用默认值）
        memoryUsage.value = "1.2GB"; // 默认值，可以根据实际情况调整

        notify("推理完成", "positive");
      } else {
        // 推理完成但没有图像数据
        console.log("推理完成但未返回图像数据:", result.image_message || "未知原因");
        resultImageSrc.value = "../../assets/images/demoImage.png";
        inferenceTime.value = result.inference_time_ms
          ? `${result.inference_time_ms}ms`
          : "15ms";
        memoryUsage.value = "1.2GB";
        notify("推理完成，但未返回图像数据", "info");
      }
    } else {
      // 其他状态（理论上不应该到这里，因为轮询会处理）
      console.warn("推理状态异常:", result.status);
      resultImageSrc.value = "../../assets/images/demoImage.png";
      inferenceTime.value = "15ms";
      memoryUsage.value = "1.2GB";
      notify("推理状态异常", "warning");
    }
  } else {
    // API调用成功但响应格式不符合预期
    console.warn("推理API响应格式异常:", response);
    showResult.value = true;
    resultImageSrc.value = "../../assets/images/demoImage.png";
    inferenceTime.value = "15ms";
    memoryUsage.value = "1.2GB";
    notify("推理完成，显示默认结果", "info");
  }

  // 关闭加载状态
  inferenceLoading.value = false;
};

// 批量推理
const runBatchInference = async () => {
  console.log("开始批量推理，图片数量:", uploadedImages.value.length);

  // 将所有图片转换为base64格式
  const inputSources = [];

  for (let i = 0; i < uploadedImages.value.length; i++) {
    const image = uploadedImages.value[i];
    let imageData = image.url;

    // 如果URL是Blob URL，需要转换为base64
    if (imageData.startsWith("blob:")) {
      try {
        const response = await fetch(imageData);
        const blob = await response.blob();

        // 将Blob转换为base64
        const reader = new FileReader();
        imageData = await new Promise((resolve) => {
          reader.onloadend = () => resolve(reader.result);
          reader.readAsDataURL(blob);
        });

        // 移除base64前缀，只保留数据部分
        imageData = imageData.split(",")[1];
      } catch (error) {
        console.error(`图片 ${i + 1} 转换失败:`, error);
        notify(`图片 ${i + 1} 转换失败`, "negative");
        inferenceLoading.value = false;
        return;
      }
    }

    inputSources.push(imageData);
  }

  console.log(
    "批量推理，模型ID:",
    selectedModel.value,
    "图片数据已准备，数量:",
    inputSources.length
  );

  // 构建批量推理请求参数
  const requestData = {
    model_id: selectedModel.value,
    input_sources: inputSources,
    confidence_threshold: 0.5,
    iou_threshold: 0.45,
    max_detections: 1000,
    save_result: true,
    return_image: true,
  };

  console.log("批量推理请求参数:", {
    ...requestData,
    input_sources: `(${inputSources.length}张图片的base64数据已省略)`,
  });

  // 调用统一推理API启动任务（自动判断批量）
  const startResponse = await api.post(
    "http://127.0.0.1:8000/backend/training/models/inference",
    requestData
  );
  console.log("批量推理启动响应:", startResponse);

  // 检查启动响应
  if (
    !startResponse ||
    !startResponse.success ||
    !startResponse.data ||
    !startResponse.data.inference_log_id
  ) {
    throw new Error(startResponse?.message || "批量推理任务启动失败，未返回推理日志ID");
  }

  const inferenceLogId = startResponse.data.inference_log_id;
  console.log("批量推理任务已启动，推理日志ID:", inferenceLogId);

  // 开始轮询推理状态
  const response = await pollInferenceStatus(inferenceLogId);

  // 处理批量推理结果
  if (response && response.success && response.data && response.data.result) {
    const result = response.data.result;
    console.log("批量推理结果数据:", result);

    // 显示推理结果
    showResult.value = true;

    // 检查推理状态
    if (result.status === "completed") {
      // 保存批量推理结果
      batchResults.value = result;
      selectedBatchIndex.value = 0;

      notify(
        `批量推理完成，成功处理 ${result.processed_images || 0} / ${
          result.total_images || 0
        } 张图片`,
        "positive"
      );
    } else {
      console.warn("批量推理状态异常:", result.status);
      notify("批量推理状态异常", "warning");
    }
  } else {
    console.warn("批量推理API响应格式异常:", response);
    notify("批量推理完成，但响应格式异常", "info");
  }

  // 关闭加载状态
  inferenceLoading.value = false;
};

// 选择批量结果
const selectBatchResult = (index) => {
  selectedBatchIndex.value = index;
};

// 打开图片预览
const openImagePreview = (imageSrc) => {
  previewImageSrc.value = imageSrc;
  imagePreviewVisible.value = true;
};

// 保存测试
const saveTest = () => {
  console.log("保存测试结果");
};

// 获取强化学习训练任务ID
function getTrainingTaskId() {
    try {
        // 优先查找强化学习特有的键名
        const taskId = localStorage.getItem('currentRLTrainingTaskId') ||
                      sessionStorage.getItem('currentRLTrainingTaskId')

        if (taskId) {
            console.log('从存储中获取到强化学习训练任务ID:', taskId)
            return taskId
        }
    } catch (error) {
        console.warn('获取强化学习训练任务ID失败:', error)
    }

    return null
}

// 停止训练任务 
async function stopTrainingTask(taskId) {
  try {
    console.log('调用后端停止训练接口，任务ID:', taskId)

    // 获取token
    let token = quasarLocalStorage.getItem('token')
    if (!token) {
      token = localStorage.getItem('token') || sessionStorage.getItem('token')
    }

    // 处理token格式
    if (token && token.startsWith('Bearer ')) {
      token = token.substring(7).trim()
    }

    // 调用停止强化学习训练接口 - 使用cancel接口来停止训练并释放容器
    const response = await api.post(`backend/training/rl/${taskId}/cancel`, {}, {
      headers: token ? { 'Authorization': `Bearer ${token}` } : {}
    })

    console.log('停止强化学习训练响应:', response)

    if (response && response.success) {
      console.log('训练任务停止成功')

      // 清除保存的任务ID
      clearTrainingTaskId()

      return response
    } else {
      throw new Error(response?.message || '停止训练失败')
    }

  } catch (error) {
    console.error('停止训练任务API调用失败:', error)
    throw error
  }
}

// 清除训练任务ID 
function clearTrainingTaskId() {
    try {
        quasarLocalStorage.remove('currentTrainingTaskId')
        localStorage.removeItem('currentTrainingTaskId')
        sessionStorage.removeItem('currentTrainingTaskId')
        console.log('训练任务ID已清除')
    } catch (error) {
        console.warn('清除训练任务ID失败:', error)
    }
}

// 下一步按钮点击事件（完成按钮）
async function nextStep() {
  console.log('StepFive: 完成按钮被点击')

  try {
    // 获取训练任务ID
    const taskId = getTrainingTaskId()

    if (taskId) {
      console.log('开始停止训练任务，任务ID:', taskId)

      // 调用后端停止训练接口
      await stopTrainingTask(taskId)

      notify('训练任务已停止，容器资源已释放', 'positive')
    } else {
      console.log('未找到训练任务ID，直接完成流程')
      notify('流程已完成', 'positive')
    }

    // 发出完成事件
    emit('next-step')

  } catch (error) {
    console.error('停止训练任务失败:', error)
    notify('停止训练任务失败: ' + (error.message || '未知错误'), 'negative')

    // 即使停止失败也允许完成流程
    emit('next-step')
  }
}

// 上一步按钮点击事件
function prevStep() {
  emit("prev-step");
}

// 组件挂载时获取模型列表和检查训练任务ID
onMounted(() => {
  fetchModelOptions();

  // 尝试获取训练任务ID
  const taskId = getTrainingTaskId()
  if (taskId) {
    console.log('StepFive: 找到训练任务ID:', taskId)
  } else {
    console.log('StepFive: 未找到训练任务ID')
  }
});
</script>

<style lang="scss" scoped>
.content {
  display: flex;
  flex-direction: column;
  flex: 1;

  .top {
    display: flex;
    margin-bottom: 0.125rem;
    min-height: 9.5rem;
    flex: 1;

    .left {
      width: 40%;
      height: inherit;
      border: 0.025rem solid #707070;
      background-color: #181a24;
      padding: 0.3375rem;
      display: flex;
      flex-direction: column;
      margin-right: 0.125rem;
      position: relative;
      background-color: #181a24;
      background-image: repeating-linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.05) 0,
        rgba(255, 255, 255, 0.01) 0.05rem,
        transparent 0.0125rem,
        transparent 0.1875rem
      );
      &::before {
        position: absolute;
        content: "";
        left: -0.1rem;
        top: 0;
        width: 0.025rem;
        height: 100%;
        background: rgba(156, 172, 198);
        border-radius: 0.125rem;
      }

      &::after {
        position: absolute;
        content: "";
        left: -0.075rem;
        top: 0;
        width: 0.025rem;
        height: 100%;
        background: rgba(156, 172, 198, 0.5);
      }
    }

    .right {
      width: 60%;
      height: inherit;
      border: 0.025rem solid #707070;
      background-color: #181a24;
      padding: 0.3375rem;
      display: flex;
      flex-direction: column;
      position: relative;
      background-color: #181a24;
      background-image: repeating-linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.05) 0,
        rgba(255, 255, 255, 0.01) 0.05rem,
        transparent 0.0125rem,
        transparent 0.1875rem
      );
      &::before {
        position: absolute;
        content: "";
        left: -0.1rem;
        top: 0;
        width: 0.025rem;
        height: 100%;
        background: rgba(156, 172, 198);
        border-radius: 0.125rem;
      }

      &::after {
        position: absolute;
        content: "";
        left: -0.075rem;
        top: 0;
        width: 0.025rem;
        height: 100%;
        background: rgba(156, 172, 198, 0.5);
      }
    }
  }
}

// 左侧区域样式
.section {
  margin-bottom: 0.375rem;

  .section-header {
    margin-bottom: 0.25rem;

    .section-title {
      color: #4ab4ff;
      font-size: 0.2rem;
    }
  }
}

.upload-section {
  .upload-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.45rem;
    margin-bottom: 0.1875rem;
  }

  .upload-item {
    width: 1.725rem;
    height: 1.725rem;
    position: relative;
  }

  .test-file-uploader {
    width: 100%;
    height: 100%;

    :deep(.el-upload) {
      width: 100%;
      height: 100%;
      border: 0.0125rem solid #8c939d;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);
      background: #181a24;

      &:hover {
        border-color: #4ab4ff;
      }
    }

    .upload-area {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .upload-icon {
        width: 0.35rem;
        height: 0.35rem;
      }
    }
  }

  .image-preview {
    border: 0.0125rem solid #8c939d;
    overflow: hidden;
    .preview-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .delete-btn {
      position: absolute;
      top: 0;
      right: 0;
      width: 0.45rem;
      height: 0.45rem;
      cursor: pointer;

      .delete-icon {
        width: 100%;
        height: 100%;
      }
    }
  }

  .upload-tips {
    color: #cea345;
    font-size: 0.15rem;
    line-height: 1.4;
  }
}

.model-section {
  margin-bottom: 0.375rem;

  .model-row {
    display: flex;
    align-items: center;
    gap: 0.25rem;

    .model-title {
      color: #4ab4ff;
      font-size: 0.2rem;
      white-space: nowrap;
    }

    .model-selector {
      flex: 1;
      padding-bottom: 0 !important;

      :deep(.q-field__control-container) {
        padding-top: 0 !important;
      }

      :deep(.q-field__control) {
        height: 0.5rem !important;
        min-height: 0.5rem !important;
      }

      :deep(.q-field__native) {
        color: white !important;
        font-size: 0.175rem !important;
      }
      :deep(.q-field__label) {
        font-size: 0.225rem;
        top: 0.125rem;
        line-height: 100%;
        color: white;
      }
      :deep(.q-field--with-bottom) {
        padding-bottom: 0;
      }
    }
  }
}

.button-section {
  display: flex;
  gap: 0.25rem;
  margin-top: auto;

  .action-btn {
    flex: 1;
    height: 0.5rem !important;
    font-size: 0.175rem !important;
    padding: 0 0.25rem !important;
    min-height: auto !important;
    border-radius: 0.0625rem;

    &:before {
      display: none !important;
    }

    &.reset-btn {
      background-color: #2b2d37 !important;
      color: #fff !important;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1) !important;
      }
    }

    &.run-btn {
      background-color: #164c82 !important;
      color: #fff !important;

      &:hover {
        background-color: rgba(22, 76, 130, 0.8) !important;
      }
    }

    &.save-btn {
      background-color: #164c82 !important;
      color: #fff !important;

      &:hover {
        background-color: rgba(22, 76, 130, 0.8) !important;
      }
    }
  }
}

// 右侧区域样式
.result-section {
  height: 100%;
  display: flex;
  flex-direction: column;

  .result-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.25rem;

    .arrow-icon {
      width: 0.2rem;
      height: 0.2rem;
      margin-right: 0.125rem;
    }

    .result-title {
      color: #4ab4ff;
      font-size: 0.2rem;
    }
  }

  .result-content {
    flex: 1;
    padding: 0.25rem;
    margin-bottom: 0.25rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .empty-result {
      .empty-text {
        color: #999;
        font-size: 0.175rem;
      }
    }

    // 批量推理结果样式
    .batch-result-display {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;

      .batch-summary {
        display: flex;
        gap: 0.25rem;
        margin-bottom: 0.25rem;
        padding: 0.125rem;
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 0.0625rem;

        .summary-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          flex: 1;

          .summary-label {
            color: #999;
            font-size: 0.125rem;
            margin-bottom: 0.0625rem;
          }

          .summary-value {
            color: #4ab4ff;
            font-size: 0.15rem;
            font-weight: bold;
          }
        }
      }

      .batch-results-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(1.5rem, 1fr));
        gap: 0.125rem;
        max-height: 4rem;
        overflow-y: auto;
        margin-bottom: 0.25rem;

        .batch-result-item {
          position: relative;
          border: 0.025rem solid #444;
          border-radius: 0.0625rem;
          overflow: hidden;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            border-color: #4ab4ff;
            transform: scale(1.02);
          }

          &.selected {
            border-color: #4ab4ff;
            box-shadow: 0 0 0.125rem rgba(74, 180, 255, 0.5);
          }

          .batch-image-container {
            position: relative;
            width: 100%;
            height: 1.2rem;

            .batch-result-image {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }

            .batch-zoom-icon {
              position: absolute;
              bottom: 0.0625rem;
              right: 0.0625rem;
              width: 0.3rem;
              height: 0.3rem;
              background: rgba(0, 0, 0, 0.7);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              transition: all 0.2s ease;
              opacity: 0;

              &:hover {
                background: rgba(74, 180, 255, 0.8);
                transform: scale(1.1);
              }
            }

            &:hover .batch-zoom-icon {
              opacity: 1;
            }
          }

          .batch-result-placeholder {
            width: 100%;
            height: 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #333;
            color: #999;
            font-size: 0.125rem;
          }

          .batch-result-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
            padding: 0.0625rem;

            .detection-count {
              color: #4ab4ff;
              font-size: 0.1rem;
            }
          }
        }
      }

      .selected-result-detail {
        flex: 1;
        border: 0.025rem solid #444;
        border-radius: 0.0625rem;
        overflow: hidden;

        .detail-header {
          background-color: rgba(74, 180, 255, 0.1);
          padding: 0.125rem;
          border-bottom: 0.025rem solid #444;

          .detail-title {
            color: #4ab4ff;
            font-size: 0.15rem;
            font-weight: bold;
          }
        }

        .detail-content {
          padding: 0.125rem;
          height: calc(100% - 0.375rem);
          display: flex;
          gap: 0.125rem;

          .detail-image-container {
            position: relative;
            width: 2rem;
            height: 1.5rem;

            .detail-image {
              width: 100%;
              height: 100%;
              object-fit: cover;
              border-radius: 0.0625rem;
              border: 0.025rem solid #444;
            }

            .detail-zoom-icon {
              position: absolute;
              bottom: 0.0625rem;
              right: 0.0625rem;
              width: 0.325rem;
              height: 0.325rem;
              background: rgba(0, 0, 0, 0.7);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              transition: all 0.2s ease;
              opacity: 0;

              &:hover {
                background: rgba(74, 180, 255, 0.8);
                transform: scale(1.1);
              }
            }

            &:hover .detail-zoom-icon {
              opacity: 1;
            }
          }

          .detail-info {
            flex: 1;

            .detail-item {
              display: flex;
              justify-content: space-between;
              margin-bottom: 0.0625rem;

              .detail-label {
                color: #999;
                font-size: 0.125rem;
              }

              .detail-value {
                color: #4ab4ff;
                font-size: 0.125rem;
                font-weight: bold;
              }
            }

            .detections-list {
              margin-top: 0.125rem;

              .detections-header {
                color: #999;
                font-size: 0.125rem;
                margin-bottom: 0.0625rem;
              }

              .detection-item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 0.0625rem;
                padding: 0.0625rem;
                background-color: rgba(255, 255, 255, 0.05);
                border-radius: 0.0625rem;

                .detection-class {
                  color: #fff;
                  font-size: 0.1rem;
                }

                .detection-confidence {
                  color: #4ab4ff;
                  font-size: 0.1rem;
                }
              }

              .more-detections {
                color: #999;
                font-size: 0.1rem;
                text-align: center;
                margin-top: 0.0625rem;
              }
            }
          }
        }
      }
    }

    .loading-result {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .loading-spinner {
        margin-bottom: 0.25rem;
      }

      .loading-text {
        color: #4ab4ff;
        font-size: 0.2rem;
        margin-bottom: 0.125rem;
        font-weight: 500;
      }

      .loading-tips {
        color: #999;
        font-size: 0.15rem;
        text-align: center;
        line-height: 1.4;
      }
    }

    .result-display {
      display: flex;
      flex-direction: column;
      align-items: center;

      .image-container {
        position: relative;
        margin-bottom: 0.1875rem;

        .result-image {
          width: auto;
          max-width: 100%;
          height: auto;
          max-height: 6rem;
          object-fit: contain;
          border: 0.025rem solid #333;
          border-radius: 0.0625rem;
          display: block;
        }

        .zoom-icon {
          position: absolute;
          bottom: 0.0625rem;
          right: 0.0625rem;
          width: 0.375rem;
          height: 0.375rem;
          background: rgba(0, 0, 0, 0.7);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease;
          opacity: 0.8;

          &:hover {
            background: rgba(74, 180, 255, 0.8);
            opacity: 1;
            transform: scale(1.1);
          }
        }
      }

      .result-info {
        display: flex;
        gap: 2rem;

        .info-item {
          display: flex;
          flex-direction: column;
          align-items: center;

          .info-label {
            color: #4ab4ff;
            font-size: 0.15rem;
            margin-bottom: 0.0625rem;
          }

          .info-value {
            color: #cea345;
            font-size: 0.175rem;
            font-weight: bold;
          }
        }
      }
    }
  }

  // 底部按钮
  .bottom {
    display: flex;
    align-items: center;
    flex-shrink: 0;

    .next {
      display: flex;
      justify-content: space-between;
      width: 100%;
      padding: 0 0.25rem;
      gap: 0.25rem;

      .prevBtn {
        margin-right: auto;
      }

      .nextBtn {
        margin-left: auto;
      }
    }
  }
}

// 通用样式
.labelColor {
  color: #4ab4ff;
}
// 图片预览对话框样式
.image-preview-dialog {
  :deep(.q-dialog__inner) {
    padding: 2rem;
  }
}

.image-preview-card {
  background-color: #1a1a1a;
  color: #fff;
  max-width: 70vw;
  max-height: 70vh;
  border-radius: 0.125rem;
  box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.5);

  .image-preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.125rem 0.25rem;
    border-bottom: 0.025rem solid #444;
    background-color: #2a2a2a;
    border-radius: 0.125rem 0.125rem 0 0;

    .preview-title {
      color: #4ab4ff;
      font-size: 0.175rem;
      font-weight: 500;
    }

    .close-btn {
      color: #fff;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }

  .image-preview-content {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.375rem;
    background-color: #1a1a1a;
    border-radius: 0 0 0.125rem 0.125rem;

    .preview-image {
      max-width: 100%;
      max-height: 50vh;
      object-fit: contain;
      border-radius: 0.0625rem;
      box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.3);
    }
  }
}

// fix small screnen bug
@media screen and (max-width: 1528px) {
  :deep(.q-field__marginal) {
    height: unset !important;
  }
}
</style>
