# NPU环境强化学习训练问题解决指南

## 问题分析

根据错误日志，主要问题包括：

1. **TBE子进程错误**: `TBE Subprocess[task_distribute] raise error[], main process disappeared!`
2. **Ray Actor重启问题**: `SingleAgentEnvRunner` actors构造参数问题
3. **多进程资源跟踪器错误**: 资源清理问题
4. **连接拒绝错误**: 多进程通信问题

## 解决方案

### 1. 使用简化版训练脚本

我们创建了 `ray_train_gym_simple.py`，专门解决NPU环境问题：

```bash
# 使用简化版脚本
python ray_train_gym_simple.py --config /workspace/rl_training_config.json --task_id rl_task_123 --use-npu
```

**简化版特点：**
- 最小化env_runners数量
- 禁用Actor重启
- 简化网络结构
- 减少评估频率
- 优化资源配置

### 2. 问题诊断工具

运行诊断脚本检查环境：

```bash
python diagnose_npu_issues.py
```

**诊断内容：**
- NPU驱动状态
- Ray环境检查
- 系统资源状态
- 进程清理
- 环境变量设置

### 3. 修改原始脚本

对 `ray_train_gym-npu.py` 的主要修改：

#### 3.1 Ray初始化优化
```python
# 添加更稳定的Ray配置
ray_init_config = {
    "ignore_reinit_error": True,
    "include_dashboard": False,
    "log_to_driver": False,
    "_temp_dir": "/tmp/ray",
}
```

#### 3.2 环境配置优化
```python
.env_runners(
    num_env_runners=min(2, max(1, cpu_count // 4)),  # 减少数量
    rollout_fragment_length=200,  # 固定值
    max_env_runner_restarts=0,  # 禁用重启
    restart_failed_sub_environments=False,
)
```

#### 3.3 信号处理
```python
def signal_handler(signum, frame):
    global graceful_exit
    graceful_exit = True
    if ray.is_initialized():
        ray.shutdown()
    sys.exit(0)
```

## 使用建议

### 1. 环境准备

```bash
# 设置环境变量
export RAY_DISABLE_IMPORT_WARNING=1
export RAY_DEDUP_LOGS=0
export ASCEND_RT_VISIBLE_DEVICES=0
export PYTHONPATH=/workspace

# 清理旧进程
pkill -f ray
rm -rf /tmp/ray*
```

### 2. 训练参数调整

**推荐配置：**
```json
{
  "hyperParams": {
    "epochs": 100,        // 减少训练轮数
    "batchSize": 32,      // 适中的批次大小
    "learningRate": "1e-4"
  },
  "cluster": {
    "cpuCount": "4",      // 适中的CPU数量
    "gpuCount": "0"       // NPU环境下设为0
  }
}
```

### 3. 故障排除步骤

#### 步骤1: 环境检查
```bash
# 检查NPU状态
npu-smi info

# 检查Python环境
python -c "import torch_npu; print('NPU可用')"

# 检查Ray
python -c "import ray; print(f'Ray版本: {ray.__version__}')"
```

#### 步骤2: 清理环境
```bash
# 运行诊断脚本
python diagnose_npu_issues.py

# 手动清理
pkill -f ray
rm -rf /tmp/ray*
ipcs -m | awk '/0x/ {print $2}' | xargs -I {} ipcrm -m {}
```

#### 步骤3: 使用简化脚本
```bash
# 先尝试简化版本
python ray_train_gym_simple.py --config config.json --task_id test_123

# 如果成功，再尝试完整版本
python ray_train_gym-npu.py --config config.json --task_id test_123 --use-npu
```

### 4. 常见错误处理

#### 错误1: TBE子进程错误
**解决方案：**
- 减少并发数量
- 使用简化配置
- 禁用Actor重启

#### 错误2: 连接拒绝
**解决方案：**
- 清理共享内存
- 重启Ray
- 检查端口占用

#### 错误3: 资源跟踪器错误
**解决方案：**
- 设置环境变量
- 使用固定临时目录
- 优雅退出处理

## 性能优化建议

### 1. 资源配置
- **CPU**: 使用4-8核心
- **内存**: 确保至少8GB可用
- **NPU**: 单卡训练，避免多卡复杂性

### 2. 训练参数
- **batch_size**: 32-64
- **num_env_runners**: 1-2
- **evaluation_interval**: 20-50

### 3. 监控指标
- CPU使用率 < 80%
- 内存使用率 < 70%
- NPU利用率监控

## 备用方案

如果NPU环境问题持续存在：

### 1. CPU模式训练
```bash
python ray_train_gym_simple.py --config config.json --task_id test_123
# 不使用 --use-npu 参数
```

### 2. 单进程训练
```python
# 修改配置，使用最小资源
config = config.env_runners(num_env_runners=0)  # 禁用env_runners
```

### 3. 传统训练方式
考虑使用其他强化学习框架，如Stable-Baselines3等。

## 联系支持

如果问题仍然存在，请提供：
1. 完整错误日志
2. 系统环境信息
3. NPU驱动版本
4. Ray版本信息

通过这些改进，应该能够解决大部分NPU环境下的训练问题。
