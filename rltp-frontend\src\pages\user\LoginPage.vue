<template>
  <!-- <q-page class="row items-center justify-evenly bg-white" style="height: 100vh"> -->
  <div class="LoginContainer">
    <img class="LoginBg" src="../../assets/images/loginImg.png" alt="">
    <img class="headerBg" src="../../assets/images/header.png" alt="">
    <div class="headerTitle">模型训练环境</div>
    <!-- Right Side Login Form -->
    <q-card flat bordered class="loginCard">
      <img class="loginUser" src="../../assets/images/loginUser.png" alt="">

      <q-card-section class="movePos" >
        <div class="font20">账号:</div>
        <q-input style="margin-bottom: .35rem;" v-model="username" :label="username ? '' : '请输入登录账号'" outlined class="q-mb-md" @keydown.enter="login">
          <template v-slot:prepend>
            <img src="../../assets/images/login_icon_zh.png" alt="user icon" class="input-icon" />
          </template>
        </q-input>

        <div class="font20">密码:</div>
        <q-input v-model="password" :label="password ? '' : '请输入密码'" type="password" outlined class="q-mb-md"
          @keydown.enter="login">
          <template v-slot:prepend>
            <img src="../../assets/images/login_icon_mm.png" alt="password icon" class="input-icon" />
          </template>
        </q-input>

        <div class="row items-center q-mb-md mt20" >
          <q-checkbox v-model="remember" label="记住账号" class="font16" />
        </div>

        <q-btn :loading="loading" label="登录"  class="full-width loginBtn" @click="login" />
      </q-card-section>
    </q-card>
  </div>
  <!-- </q-page> -->
</template>

<script setup>
import { ref, } from 'vue'
import { useRouter } from 'vue-router'
import { api } from 'boot/axios'
import { usePlugin } from 'composables/plugin.js'
import { useUserStore } from '../../stores/userStore.js'
import { storeToRefs } from 'pinia'

const { notify, localStorage } = usePlugin()
const router = useRouter()

// 在Vite中使用相对路径引入图片
const smartModelImage = '/src/assets/images/smart_model_image.png'

const username = ref('')
const password = ref('')
const remember = ref(false)
const loading = ref(false)

const userStore = useUserStore()
let { userAvatar } = storeToRefs(userStore)

function login() {
  // Add your login logic here
  console.log('Logging in with:', username.value, password.value, remember.value)

  api
    .post('user/login/', {
      username: username.value,
      password: password.value,
    })
    .then(res => {
      loading.value = false
      localStorage.set('token', res.access)
      localStorage.set('username', res.user)
      notify('登录成功', 'positive')
      router.push('/')
      console.log('ravatar_url', res.avatar_url)
      userStore.changeUserAvatar(res.avatar_url)
      console.log("userAvatar", userAvatar.value)
    })
    .catch(() => {
      loading.value = false
      notify('账号或密码错误')
    })
}

function register() {
  // Navigate to registration page
  router.push('/user/register')
}

function forgotPassword() {
  // Navigate to forgot password page
  router.push('/user/forgot-password')
}
</script>

<style lang="scss" scoped>
.LoginContainer {
  .LoginBg {
    width: 100vw;
    height: 95vh;
    position: fixed;
    left: 0;
    top: 0;
  }
  .headerBg{
    position: fixed;
    left: 0;
    top:0;
    width: 100%;
    height: 1.4875rem;
  }
  .headerTitle{
    position: fixed;
    left:50%;
    top:.1875rem;
    font-size: .45rem;
    color: white;
    transform: translateX(-50%);
    font-family:MicrosoftYaHei-Bold;
    font-weight: bold;
    text-shadow: 0rem .075rem .075rem rgba(4, 34, 66, 0.45);
  }

  .loginCard {
    position: fixed;
    right: 2.125rem;
    bottom: 3.5rem;
    width: 5.6625rem;
    height: 6.075rem;

    .loginUser {
      position: absolute;
      left: 0;
      top: 0;
    }
  }
}

.font20 {
  font-size: .25rem;
  margin-bottom: .175rem;
}
.movePos{
  padding: .5rem !important;
  padding-top: 1rem !important;
}
.mt20{
  margin-top: .25rem !important;
}
@media screen and (min-width: 1921px) {
  .movePos{
    padding-top: 1.2rem !important;
  }
  .mt20{
    margin-top: .5rem !important;
  }
}

.input-icon {
  width: .325rem;
  height: .325rem;
}

.loginBtn {
  height: .675rem;
  border-radius: .0625rem;
  font-size: .3rem;
  background: #0d4a87 !important;
}

:deep(.q-checkbox__inner) {
  color: #4ab4ff !important;
}

:deep(.q-checkbox__label) {
  color: #4ab4ff !important;
}

.text-primary {
  color: #409eff;
}

.font16 {
  font-size: .15rem !important;
}
@media screen and (max-width: 1536px) {
  .q-mb-md{
    margin-bottom: 0;
  }
  .mt20{
    margin-top: 0 !important;
  }
}

:deep(.q-field) {
  font-size: .175rem !important;
}
</style>
