import argparse
import ray
from ray.rllib.algorithms.ppo import PPO
from ray.rllib.algorithms.ppo import PPOConfig
from ray.tune.registry import register_env
import torch
from envs.JSBSim.envs.singlecombat_env import SingleCombatEnv
from envs.JSBSim.envs.multiplecombat_rllib_env import MultipleCombatEnv
import os
import argparse
from ray import tune
import time

def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--checkpoint-path", type=str, default="",
                      help="检查点路径，如果不指定则使用随机动作")
    parser.add_argument("--env-name", type=str, default="1v1/ShootMissile/Selfplay",
                      help="环境名称")
    parser.add_argument("--num-episodes", type=int, default=1,
                      help="评估轮数")
    parser.add_argument("--num-env-runners", type=int, default=2,
                      help="训练时的环境运行器数量")
    parser.add_argument("--num-gpus", type=int, default=1,
                      help="GPU数量")
    parser.add_argument("--output-dir", type=str, default="./eval_results",
                      help="评估结果和回放文件保存目录")
    parser.add_argument("--env-registry-name", type=str, default="JSBSim-Combat",
                      help="Ray注册的环境名称")
    parser.add_argument("--address", type=str, default="auto",
                      help="Ray集群地址")
    parser.add_argument("--num-workers", type=int, default=2,
                      help="评估worker数量")
    return parser.parse_args()

def env_creator(env_config):
    env = MultipleCombatEnv(env_config["env_name"])
    # env.seed(5)
    return env

def get_config(args):
    config = (
        PPOConfig()
        .environment(args.env_registry_name, env_config={"env_name": args.env_name})
        .framework("torch")
        .resources(num_gpus=args.num_gpus)
        .env_runners(
            num_env_runners=args.num_env_runners,
            rollout_fragment_length='auto',
            num_envs_per_env_runner=1,
            num_cpus_per_env_runner=1,
            num_gpus_per_env_runner=0,
        )
        .learners(
            num_learners=1,
            num_cpus_per_learner=1,
            num_gpus_per_learner=1
        )
        .training(
            gamma=0.99,
            lr=5e-5,
            train_batch_size=16384,
            lambda_=0.95,
            clip_param=0.2,
            vf_clip_param=10.0,
            entropy_coeff=0.01,
            num_sgd_iter=10,
            minibatch_size=2048,
        )
        .multi_agent(
            policies=["red_policy", "blue_policy"],
            policy_mapping_fn=lambda agent_id, *args, **kwargs: (
                "red_policy" if agent_id.startswith("A") else "blue_policy"
            ),
            # policies_to_train=["red_policy"],  # 只训练红方策略
            # observation_fn=None,  # 使用默认观察函数
        )
        .evaluation(
            evaluation_interval=10,
            evaluation_duration=20,
            evaluation_num_workers=1,
            evaluation_config={
                "render_mode": "txt",
                "explore": False,
                "env_config": {
                    "env_name": args.env_name,
                },
            },
        )
    )
    
    # 清空探索配置
    config.exploration_config = {}
    return config

def evaluate_model(args):
    # 初始化Ray，连接到集群
    ray.init()
    print(f"已连接到Ray集群: {ray.cluster_resources()}")

    # 注册环境
    register_env(args.env_registry_name, env_creator)

    if args.checkpoint_path is not None and args.checkpoint_path != "":
        checkpoint_path = os.path.abspath(args.checkpoint_path)
        if not os.path.exists(checkpoint_path):
            print(f"错误：检查点不存在: {checkpoint_path}")
            return
        print(f"使用检查点: {checkpoint_path}")
        
        # 创建PPO算法实例并加载检查点
        algo = PPO(config=get_config(args))
        algo.restore(checkpoint_path)
    else:
        print("未指定检查点，直接进行环境评估")
        algo = PPO(config=get_config(args))
    
    # 确保输出目录存在
    output_dir = os.path.expanduser(args.output_dir)
    os.makedirs(output_dir, exist_ok=True)

    # 设置回放文件路径
    timestamp = time.strftime("%Y%m%d-%H%M%S")
    filepath = os.path.join(output_dir, f"JSBSimRecording_{timestamp}.txt.acmi")
    
    # 使用Ray的评估器进行并行评估
    print("开始并行评估...")
    evaluation_results = algo.evaluate()

    # 提取评估结果
    metrics = evaluation_results["env_runners"]
    episode_rewards = metrics["episode_return_mean"]
    episode_lengths = metrics["episode_len_mean"]

    print(f"评估完成:")
    print(f"平均奖励: {episode_rewards}")
    print(f"平均回合长度: {episode_lengths}")

    # 将评估结果保存到文件
    results_file = os.path.join(output_dir, f"eval_results_{timestamp}.txt")
    with open(results_file, "w") as f:
        f.write(f"环境: {args.env_name}\n")
        f.write(f"检查点: {args.checkpoint_path}\n")
        f.write(f"评估轮数: {args.num_episodes}\n")
        f.write(f"评估worker数量: {args.num_workers}\n")
        f.write(f"平均奖励: {episode_rewards}\n")
        f.write(f"平均回合长度: {episode_lengths}\n")
        f.write(f"详细评估指标:\n")
        # 只保存重要的指标
        important_metrics = {
            "episode_return_mean": metrics["episode_return_mean"],
            "episode_return_min": metrics["episode_return_min"],
            "episode_return_max": metrics["episode_return_max"],
            "episode_len_mean": metrics["episode_len_mean"],
            "episode_len_min": metrics["episode_len_min"],
            "episode_len_max": metrics["episode_len_max"],
            "num_episodes": metrics["num_episodes"],
            "episode_duration_sec_mean": metrics["episode_duration_sec_mean"]
        }
        for key, value in important_metrics.items():
            f.write(f"{key}: {value}\n")
    
    print(f"评估结果已保存到: {results_file}")

    # 清理
    if algo is not None:
        algo.stop()
    ray.shutdown()

if __name__ == "__main__":
    args = parse_args()
    evaluate_model(args)
