import yaml
from kubernetes import client, config
from kubernetes.client.rest import ApiException
import copy

def get_cluster_resources():
    cluster_resources = []
    v1 = client.CoreV1Api()

    nodes = v1.list_node().items
    for node in nodes:
        node_name = node.metadata.name

        # Get allocatable resources
        allocatable = node.status.allocatable
        allocatable_cpu = parse_cpu(allocatable['cpu'])  # Convert CPU cores to float
        allocatable_memory_gib = parse_memory(allocatable['memory'])  # Convert memory to Gi
        allocatable_gpu = int(allocatable.get('nvidia.com/gpu', 0))  # GPU might not be available

        # Get used resources by summing resources of all pods running on this node
        used_cpu = 0
        used_memory_gib = 0
        used_gpu = 0

        pods = v1.list_pod_for_all_namespaces(field_selector=f"spec.nodeName={node_name}").items
        for pod in pods:
            for container in pod.spec.containers:
                if container.resources.requests:
                    used_cpu += parse_cpu(container.resources.requests.get('cpu', '0'))
                    used_memory_gib += parse_memory(container.resources.requests.get('memory', '0Ki'))
                    used_gpu += int(container.resources.requests.get('nvidia.com/gpu', 0) or 0)

        # Calculate available resources
        available_cpu = int(allocatable_cpu * 0.85) - used_cpu
        available_memory_gib = int(allocatable_memory_gib * 0.85) - used_memory_gib
        available_gpu = allocatable_gpu - used_gpu

        cluster_resources.append({
            'cpu': max(0, available_cpu),
            'memory': max(0, available_memory_gib),  # Memory in Gi
            'gpu': max(0, available_gpu),
            'name': node_name
        })

    return cluster_resources

def parse_cpu(cpu_str):
    """
    Parse a CPU string (which may be in millicores, e.g., '500m') into a float representing cores.
    """
    if cpu_str.endswith('m'):
        return int(cpu_str[:-1]) / 1000.0  # Convert millicores to cores
    else:
        return float(cpu_str)  # No 'm' means it's in cores

def parse_memory(memory_str):
    """
    Parse a memory string (e.g., '2Gi', '512Mi') into a float representing GiB.
    """
    if memory_str.endswith('Ki'):
        return int(memory_str[:-2]) / (1024 ** 2)  # Ki to Gi
    elif memory_str.endswith('Mi'):
        return int(memory_str[:-2]) / 1024  # Mi to Gi
    elif memory_str.endswith('Gi'):
        return int(memory_str[:-2])  # Already in Gi
    elif memory_str.endswith('Ti'):
        return int(memory_str[:-2]) * 1024  # Ti to Gi
    else:
        return 0  # Handle cases where memory request is missing or not recognized

def allocate_resources(node, cpu_needed, gpu_needed, mem_needed):
    allocated = {
        'cpu': min(cpu_needed, node['cpu']),
        'gpu': min(gpu_needed, node['gpu']),
        'memory': min(mem_needed, node['memory']),
    }
    node['cpu'] -= allocated['cpu']
    node['gpu'] -= allocated['gpu']
    node['memory'] -= allocated['memory']
    return allocated

def split_resources(total_resources, cluster_resources):
    worker_configs = []
    for node in cluster_resources:
        while (total_resources['cpu'] > 0 or total_resources['gpu'] > 0 or total_resources['memory'] > 0) and \
              (node['cpu'] > 0 or node['gpu'] > 0 or node['memory'] > 0):
            allocation = allocate_resources(node, total_resources['cpu'], total_resources['gpu'], total_resources['memory'])
            worker_configs.append({
                'cpu': allocation['cpu'],
                'gpu': allocation['gpu'],
                'memory': f"{allocation['memory']}Gi",
                'node': node['name']
            })
            total_resources['cpu'] -= allocation['cpu']
            total_resources['gpu'] -= allocation['gpu']
            total_resources['memory'] -= allocation['memory']
    return worker_configs

def calculate_worker_configuration(
    cluster_resources, 
    actor_num, actor_cpu_num, actor_gpu_num, actor_mem_num, 
    learner_num, learner_cpu_num, learner_gpu_num, learner_mem_num):

    total_actor_resources = {
        'cpu': actor_num * actor_cpu_num,
        'gpu': actor_num * actor_gpu_num,
        'memory': actor_num * actor_mem_num,
    }

    total_learner_resources = {
        'cpu': learner_num * learner_cpu_num,
        'gpu': learner_num * learner_gpu_num,
        'memory': learner_num * learner_mem_num,
    }

    # Sort nodes by available resources (prefer nodes with GPUs)
    sorted_nodes = sorted(cluster_resources, key=lambda x: (x['gpu'], x['cpu'], x['memory']), reverse=True)

    # Allocate resources for learners
    learner_configs = split_resources(total_learner_resources, sorted_nodes)

    # Allocate resources for actors
    actor_configs = split_resources(total_actor_resources, sorted_nodes)

    return learner_configs + actor_configs

def submit_ray_job(
                   entrypoint: str = 'python /root/ray-gym/train.py',
                   run_image: str = 'core.**************.nip.io:30670/public/ray-gym-cartpole:latest',
                   ray_version: str = '2.7.0',
                   actor_num: int = 64,
                   actor_cpu_num: int = 1,
                   actor_gpu_num: int = 0,
                   actor_mem_num: int = 2,
                   learner_num: int = 1,
                   learner_cpu_num: int = 4,
                   learner_gpu_num: int = 1,
                   learner_mem_num: int = 4,
                   namespace: str = 'rl-platform',
                   ray_job_name: str = 'ray-job'):
    
    config.load_kube_config()
    api_instance = client.CustomObjectsApi()

    # Get cluster resources
    cluster_resources = get_cluster_resources()

    # Calculate worker configurations
    worker_configurations = calculate_worker_configuration(
        cluster_resources, 
        actor_num, actor_cpu_num, actor_gpu_num, actor_mem_num, 
        learner_num, learner_cpu_num, learner_gpu_num, learner_mem_num)

    # Create worker specs
    worker_specs = []
    for conf in worker_configurations:
        worker_spec = {
            'replicas': 1,
            'minReplicas': 1,
            'maxReplicas': 1,  # Each config is for one worker
            'groupName': 'worker-group',
            'rayStartParams': {},
            'template': {
                'spec': {
                    'nodeName': conf['node'],  # Assign worker to a specific node
                    'containers': [{
                        'name': 'ray-worker',
                        'image': run_image,
                        'imagePullPolicy': 'IfNotPresent',
                        'resources': {
                            'limits': {
                                'cpu': conf['cpu'],
                                'memory': conf['memory'],
                                'nvidia.com/gpu': conf['gpu'] if conf['gpu'] > 0 else None
                            },
                            'requests': {
                                'cpu': conf['cpu'],
                                'memory': conf['memory'],
                                'nvidia.com/gpu': conf['gpu'] if conf['gpu'] > 0 else None
                            }
                        },
                        'volumeMounts': [{
                            'name': 'shared-volume',
                            'mountPath': '/mnt/shared'  
                        }]
                    }],
                    'volumes': [{
                        'name': 'shared-volume',
                        'persistentVolumeClaim': {
                            'claimName': 'common-pvc'
                        }
                    }]
                }
            }
        }
        worker_specs.append(worker_spec)

    # Load the YAML template
    with open('job.yaml') as f:
        ray_job_yaml = yaml.safe_load(f)

    entrypoint += f' --num_actors {actor_num} --actor_cpu_num {actor_cpu_num} --actor_gpu_num {actor_gpu_num} --actor_mem_num {actor_mem_num} --learner_cpu_num {learner_cpu_num} --learner_gpu_num {learner_gpu_num} --learner_mem_num {learner_mem_num}'
    ray_job_yaml['spec']['rayClusterSpec']['workerGroupSpecs'] = worker_specs

    # Update YAML fields dynamically
    ray_job_yaml['metadata']['name'] = ray_job_name
    ray_job_yaml['metadata']['namespace'] = namespace
    ray_job_yaml['spec']['entrypoint'] = entrypoint
    ray_job_yaml['spec']['rayClusterSpec']['rayVersion'] = ray_version
    ray_job_yaml['spec']['rayClusterSpec']['headGroupSpec']['template']['spec']['containers'][0]['image'] = run_image

    # Create RayJob in Kubernetes
    try:
        api_response = api_instance.create_namespaced_custom_object(
            group="ray.io",
            version="v1",
            namespace=namespace,
            plural="rayjobs",
            body=ray_job_yaml
        )
        print(api_response)
        print("RayCluster created successfully!")
    except ApiException as e:
        print(f"Exception when creating RayCluster: {e}")

if __name__ == '__main__':
    submit_ray_job()
