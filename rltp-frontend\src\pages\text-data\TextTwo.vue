<!--
 * @Author: Szc
 * @Date: 2025-08-20 14:31:04
 * @LastEditors: Szc
 * @LastEditTime: 2025-08-20 21:05:06
 * @Description: 文档格式转换 - 第二步：数据生成
-->

<template>
    <div class="content">
        <div class="top">
            <!-- 左侧原文对照区域 -->
            <div class="left">
                <div class="header-section">
                    <img class="section-icon" src="../../assets/images/icon_dz.png" alt="">
                    <div class="section-title">原文对照</div>
                </div>

                <div class="original-content">
                    <div class="document-title">文档格式转换产品文档</div>

                    <div class="document-section">
                        <div class="section-header">一、产品介绍</div>
                        <div class="section-text">
                            文档格式转换可识别图片/PDF/OFD文档版面布局，提取文字内容，并转换为保留原文档版式的word/excel/双层PDF/双层ODF文件，方便二次编辑或复制，可支持各类格/印章/水印/手写等内容的文档，满足文档格式转换，企业档案电子化等场景需求。
                        </div>

                        <div class="document-image">
                            <img src="../../assets/images/ai-car.png" alt="产品展示图" />
                        </div>
                    </div>

                    <div class="document-section">
                        <div class="section-header">二、功能介绍</div>
                        <div class="subsection">
                            <div class="subsection-title">文档版式分析</div>
                            <div class="section-text">
                                识别全文信息，并分析文档版式结构，识别文档中包含的插图/表格/标题/段落/印章等版式元素。
                            </div>
                        </div>

                        <div class="subsection">
                            <div class="subsection-title">文档版式还原</div>
                            <div class="section-text">
                                还原文档版式信息，转换为保留原文档版式布局的word文档，方便二次编辑和复制。
                            </div>
                        </div>

                        <div class="subsection">
                            <div class="subsection-title">文档版式分析</div>
                            <div class="section-text">
                                识别全文信息，并分析文档版式结构，识别文档中包含的插图/表格/标题/段落/印章等版式元素。
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧人工审核和审核内容区域 -->
            <div class="right">
                <!-- 主标题区域 -->
                <div class="header-section">
                    <img class="section-icon" src="../../assets/images/icon_dz.png" alt="">
                    <div class="section-title">人工审核</div>
                </div>

                <!-- 主要内容容器 -->
                <div class="main-content">
                    <!-- 左侧人工审核区域 -->
                    <div class="audit-section">


                        <div class="questions-list">
                            <div class="audit-stats">
                                <span class="stats-text">已审核 0 个，剩余 100 个</span>
                                <q-select class="type-select" v-model="selectedType" :options="typeOptions" outlined
                                    dense emit-value map-options :label="selectedType? selectedType.value : '-- 请选择类型 --'" />
                            </div>
                            <div class="question-item" v-for="question in questions" :key="question.id"
                                :class="{ 'selected': question.selected }" @click="toggleQuestion(question)">
                                <div class="question-text">{{ question.text }}</div>
                                <div class="question-status">
                                    <div class="status-indicator" :class="question.status">
                                        <div v-if="question.status === 'confirmed'" class="status-icon green" />
                                        <img v-else-if="question.status === 'rejected'" src="" class="status-icon red" />
                                        <img v-else-if="question.status === 'pending'" src="" class="status-icon default" />
                                        <span class="status-text">{{ getStatusText(question.status) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧审核内容区域 -->
                    <div class="review-section">
                        <div class="review-content">

                            <div class="review-title">
                                <img class="title-icon" src="../../assets/images/icon_shenheneirong.png" alt="">
                                <div class="title-text">审核内容</div>
                            </div>

                            <div class="review-question">
                                <div class="question-label">问题：</div>
                                <div class="question-text">请审核生成的问题内容是否正确，如不正确请修改或删除，也可将其删除。</div>
                            </div>

                            <div class="review-question">
                                <div class="question-label">如何申请百亿贷？</div>
                            </div>

                            <div class="review-question">
                                <div class="question-label">回答：</div>
                                <div class="question-text">请审核生成的回答内容是否正确，如不正确请修改或删除，也可将其删除。</div>
                            </div>

                            <div class="answer-options">
                                <div class="option-item" v-for="(option, index) in answerOptions" :key="index"
                                    :class="{ 'selected': option.selected }" @click="toggleOption(option)">
                                    <div class="checkbox-wrapper">
                                        <img :src="option.selected ? '../../assets/images/btn_xz_2.png' : '../../assets/images/btn_xz_1.png'"
                                            class="checkbox-icon" />
                                    </div>
                                    <div class="option-text">{{ option.text }}</div>
                                </div>
                            </div>

                            <div class="action-buttons">
                                <q-btn class="action-btn confirm-btn roundBox" color="primary">
                                    确认无误
                                </q-btn>
                                <q-btn class="action-btn delete-btn roundBox" color="negative">
                                    删除问题
                                </q-btn>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 底部下一步按钮 -->
                <div class="bottom">
                    <div class="next">
                        <q-btn class="prevBtn roundBox" color="grey-7" label="上一步" @click="prevStep" />
                        <q-btn class="nextBtn roundBox" color="primary" label="下一步" @click="nextStep" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, defineEmits } from 'vue'

const emit = defineEmits(['next-step', 'prev-step'])

// 类型选择
const selectedType = ref('')
const typeOptions = [
    { label: '全部', value: 'all' },
    { label: '问题类型1', value: 'type1' },
    { label: '问题类型2', value: 'type2' }
]

// 问题列表
const questions = ref([
    {
        id: 1,
        text: 'Python中如何读取CSV文件？',
        status: 'confirmed',
        selected: false
    },
    {
        id: 2,
        text: '如何处理缺失的数据？',
        status: 'confirmed',
        selected: false
    },
    {
        id: 3,
        text: '如何申请退税？',
        status: 'confirmed',
        selected: false
    },
    {
        id: 4,
        text: '如何煮出完美的水煮蛋？',
        status: 'rejected',
        selected: false
    },
    {
        id: 5,
        text: '这是一条超长问题占位样式看着多少字符才会换行显示出来呢？',
        status: 'confirmed',
        selected: false
    },
    {
        id: 6,
        text: '如何申请百亿国补？',
        status: 'pending',
        selected: true
    },
    {
        id: 7,
        text: '如何申请退税？',
        status: 'pending',
        selected: false
    },
    {
        id: 8,
        text: '如何申请退税？',
        status: 'pending',
        selected: false
    },
    {
        id: 9,
        text: '如何申请退税？',
        status: 'pending',
        selected: false
    },
    {
        id: 10,
        text: '如何申请退税？',
        status: 'pending',
        selected: false
    }
])

// 答案选项
const answerOptions = ref([
    {
        id: 1,
        text: '问题回答 1',
        selected: false
    },
    {
        id: 2,
        text: '问题回答 2',
        selected: true
    },
    {
        id: 3,
        text: '问题回答 3',
        selected: false
    },
    {
        id: 4,
        text: '问题回答 4',
        selected: false
    },
    {
        id: 5,
        text: '问题回答 5',
        selected: false
    }
])

// 方法
const toggleQuestion = (question) => {
    questions.value.forEach(q => q.selected = false)
    question.selected = true
}

const toggleOption = (option) => {
    option.selected = !option.selected
}

const getStatusText = (status) => {
    switch (status) {
        case 'confirmed':
            return '已确认'
        case 'rejected':
            return '已删除'
        case 'pending':
            return '未确认'
        default:
            return ''
    }
}

const nextStep = () => {
    emit('next-step')
}

const prevStep = () => {
    emit('prev-step')
}
</script>

<style lang="scss" scoped>
.content {
    display: flex;
    flex-direction: column;
    flex: 1;

    .top {
        display: flex;
        flex: 1;
        min-height: 9.5rem;
        gap: .125rem;

        .left,
        .right {
            border: .025rem solid #707070;
            background-color: #181a24;
            background-image:
                repeating-linear-gradient(130deg,
                    rgba(255, 255, 255, 0.05) 0px,
                    rgba(255, 255, 255, 0.01) 4px,
                    transparent 1px,
                    transparent 15px);
            padding: .25rem;
            position: relative;

            &::before {
                position: absolute;
                content: '';
                left: -0.1rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198);
                border-radius: .125rem;
                z-index: 1;
            }

            &::after {
                position: absolute;
                content: '';
                left: -0.075rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198, .5);
                z-index: 1;
            }
        }

        // 左侧原文对照区域
        .left {
            width: 40%;
            display: flex;
            flex-direction: column;

            .original-content {
                flex: 1;
                overflow-y: auto;
                background: rgba(21,27,41,.6);
                border-radius: .125rem;
                padding: .25rem;
                color: white !important;

                .document-title {
                    font-size: .25rem;
                    font-weight: bold;
                    text-align: center;
                    margin-bottom: .25rem;
                }

                .document-section {
                    margin-bottom: .25rem;

                    .section-header {
                        font-size: .2rem;
                        font-weight: bold;
                        margin-bottom: .1875rem;
                    }

                    .section-text {
                        font-size: .175rem;
                        line-height: 1.6;
                        margin-bottom: .1875rem;
                        text-align: justify;
                    }

                    .subsection {
                        margin-bottom: .1875rem;

                        .subsection-title {
                            font-size: .175rem;
                            font-weight: bold;
                            margin-bottom: .125rem;
                        }
                    }

                    .document-image {
                        text-align: center;
                        margin: .1875rem 0;

                        img {
                            max-width: 100%;
                            height: 2rem;
                            object-fit: contain;
                            border-radius: .125rem;
                        }
                    }
                }
            }
        }

        // 右侧区域
        .right {
            width: 60%;
            display: flex;
            flex-direction: column;

            .main-content {
                display: flex;
                flex: 1;
                gap: .125rem;
                margin-bottom: .1875rem;

                .audit-section,
                .review-section {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                }
            }
        }
    }

    .header-section {
        display: flex;
        align-items: center;
        margin-bottom: .25rem;

        .section-icon {
            width: .2rem;
            height: .2rem;
            margin-right: .125rem;
        }

        .section-title {
            color: #ffb628;
            font-size: .225rem;
            font-weight: bold;
        }
    }

    // 人工审核区域
    .audit-section {
        flex: 1;
        display: flex;
        flex-direction: column;

        .audit-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: .1875rem;

            .stats-text {
                color: #ffb628;
                font-size: .175rem;
            }

            .type-select {
                width: 2.5rem;
                :deep(.q-field__control) {
                    // background-color: #2a2d3a;
                    color: #fff;
                    font-size: .175rem;
                }

                :deep(.q-field__native) {
                    color: #fff;
                    
                }

                :deep(.q-field__label) {
                    color: #999;
                    font-size: .175rem;
                }
            }
        }

        .questions-list {
            flex: 1;
            overflow-y: auto;
            border: .0125rem solid #607387;
            padding: .125rem;

            .question-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: .125rem;
                margin-bottom: .0625rem;
                background-color: rgba(42, 45, 58, 0.3);
                cursor: pointer;
                transition: background-color 0.3s ease;
                border-bottom: .0125rem solid #607387;
                &:hover {
                    background-color: rgba(42, 45, 58, 0.6);
                }

                &.selected {
                    background-color: rgba(74, 180, 255, 0.2);
                    border: .0125rem solid #4ab4ff;
                }

                .question-text {
                    flex: 1;
                    color: #fff;
                    font-size: .15rem;
                    line-height: 1.4;
                    margin-right: .125rem;
                }

                .question-status {
                    .status-indicator {
                        display: flex;
                        align-items: center;
                        gap: .0625rem;

                        .status-icon {
                            width: .1rem;
                            height: .1rem;
                            border-radius: 50%;
                        }

                        .status-text {
                            font-size: .125rem;
                            color: #fff;
                        }

                        .green{
                            background: #009000;
                        }

                        .red{
                            background:#ff0000;
                        }

                        .default{
                            background:#3e629c;
                        }

                        &.confirmed .status-text {
                            color: #9cacc6;
                        }

                        &.rejected .status-text {
                            color: #9cacc6;
                        }

                        &.pending .status-text {
                            color: #9cacc6;
                        }
                    }
                }
            }
        }
    }

    // 审核内容区域
    .review-section {
        flex: 1;
        display: flex;
        flex-direction: column;

        .review-content {
            flex: 1;
            overflow-y: auto;
            border-radius: .125rem;
            padding: .1875rem;
            padding-top: 0;

            .review-title {
                display: flex;
                align-items: center;
                margin-bottom: .2875rem;

                .title-icon {
                    width: .275rem;
                    height: .275rem;
                    margin-right: .125rem;
                }

                .title-text {
                    color: white;
                    font-size: .2rem;
                }
            }

            .review-question {
                margin-bottom: .1875rem;
                padding-left: .4rem;

                .question-label {
                    color: #4ab4ff;
                    font-size: .175rem;
                    font-weight: bold;
                    margin-bottom: .0625rem;
                }

                .question-text {
                    color: #ccc;
                    font-size: .15rem;
                    line-height: 1.6;
                }
            }

            .answer-options {
                margin-bottom: .25rem;

                .option-item {
                    display: flex;
                    align-items: center;
                    padding: .125rem;
                    margin-bottom: .0625rem;
                    background-color: rgba(42, 45, 58, 0.5);
                    border-radius: .0625rem;
                    cursor: pointer;
                    transition: background-color 0.3s ease;

                    &:hover {
                        background-color: rgba(42, 45, 58, 0.8);
                    }

                    &.selected {
                        background-color: rgba(74, 180, 255, 0.2);
                    }

                    .checkbox-wrapper {
                        margin-right: .125rem;

                        .checkbox-icon {
                            width: .2rem;
                            height: .2rem;
                        }
                    }

                    .option-text {
                        color: #fff;
                        font-size: .15rem;
                    }
                }
            }

            .action-buttons {
                display: flex;
                gap: .1875rem;

                .action-btn {
                    height: .5rem;
                    font-size: .175rem;
                    padding: 0 .25rem;

                    &.confirm-btn {
                        background: #4ab4ff;
                        color: #fff;

                        &:hover {
                            background: #3a9de8;
                        }
                    }

                    &.delete-btn {
                        background: #f44336;
                        color: #fff;

                        &:hover {
                            background: #d32f2f;
                        }
                    }
                }
            }
        }
    }

    // 底部按钮
    .bottom {
        height: .8rem;
        display: flex;
        align-items: center;
        flex-shrink: 0;
        margin-top: auto;

        .next {
            display: flex;
            justify-content: space-between;
            width: 100%;
            padding: 0 .25rem;
            gap: .25rem;

            .prevBtn {
                margin-right: auto;
            }

            .nextBtn {
                margin-left: auto;
            }
        }
    }
}

// 滚动条样式
::-webkit-scrollbar {
    width: .0625rem;
}

::-webkit-scrollbar-track {
    background: #1a1a1a;
    border-radius: .03125rem;
}

::-webkit-scrollbar-thumb {
    background: rgba(74, 180, 255, 0.6);
    border-radius: .03125rem;

    &:hover {
        background: rgba(74, 180, 255, 0.8);
    }
}
</style>
