import ray
import subprocess
# import gym
import gymnasium as gym
from buffer import CartReplayBuffer
from parameter_server import CartParameterServer
from actor import CartActor
from learner import CartLearner
import torch
from model import PPOActorCritic
from model_cnn import PPOActorCriticCNN

import argparse

from utils import preprocess_state

def main_distributed(
        actor_cpu_num,
        actor_gpu_num,
        actor_mem_num,
        learner_cpu_num,
        learner_gpu_num,
        learner_mem_num,
        num_actors=64, episodes=10000,  batch_size=20,max_buffer_size=1000, gamma=0.99, lr=1e-4, 
        clip_epsilon=0.2, update_epochs=20,  hidden_dim=64, env_name='CartPole-v1', 
        horizon=500,log_dir="logs",log_interval=50,model_save_path="checkpoints",model_save_interval=100,on_policy=True):
    ray.init()

    device_learner = "cuda:0" if learner_gpu_num > 0 and torch.cuda.is_available() else "cpu"
    # device_actor = "cuda:0" if actor_gpu_num > 0 and torch.cuda.is_available() else "cpu"
    
    # device_learner = "cpu"
    device_actor = "cpu"
    print(f"Device for learner: {device_learner} and for actor: {device_actor}")
    env = gym.make(env_name)
    # state, _ = env.reset()
    # process_state = preprocess_state(state)
    
    # mlp
    # model = PPOActorCritic(state_dim=process_state.shape[0], action_dim=env.action_space.n, hidden_dim=hidden_dim).to(device_learner)
    # cnn
    model = PPOActorCriticCNN(action_dim=env.action_space.n).to(device_learner)

    replay_buffer = CartReplayBuffer.options(num_cpus=actor_cpu_num, num_gpus=actor_gpu_num, memory=actor_mem_num).remote(max_buffer_size)
    param_server = CartParameterServer.options(num_cpus=actor_cpu_num, num_gpus=actor_gpu_num, memory=actor_mem_num).remote(env_name)

    learner = CartLearner.options(num_cpus=learner_cpu_num, num_gpus=learner_gpu_num, memory=learner_mem_num).remote(
        model, replay_buffer, param_server, device_learner, gamma, lr, clip_epsilon, log_dir, model_save_path, model_save_interval)

    actors = [CartActor.options(num_cpus=actor_cpu_num, num_gpus=actor_gpu_num, memory=actor_mem_num).remote(
        seed=1, env_name=env_name,  horizon=horizon, device=device_actor, index=index, log_dir=log_dir, log_interval=log_interval) for index in range(num_actors)]
   
    episode=0
    while episode < episodes:
        actor_tasks = [actor.run.remote(param_server, replay_buffer, learner) for actor in actors]
        if on_policy:
           ray.get(actor_tasks)
           update_epochs=1
        else:
            update_epochs=1
        if ray.get(replay_buffer.get_buffer_size.remote()) >= update_epochs*batch_size:
            learner_task = learner.train.remote(batch_size, update_epochs)
            ray.get(learner_task)
            episode+=1
        
        # print(f"Episode {episode + 1}/{episodes} completed.")

    print('Finished training!')
    ray.shutdown()


if __name__ == '__main__':
    print(torch.cuda.is_available())
    parser = argparse.ArgumentParser()

    # Define arguments with default values
    parser.add_argument('--actor_cpu_num',type=int,default=1)
    parser.add_argument('--actor_gpu_num',type=int,default=0)
    parser.add_argument('--actor_mem_num',type=int,default=2)
    parser.add_argument('--learner_cpu_num',type=int,default=2)
    parser.add_argument('--learner_gpu_num',type=int,default=1)
    parser.add_argument('--learner_mem_num',type=int,default=8)
    parser.add_argument('--num_actors', type=int, default=5)
    parser.add_argument('--episodes', type=int, default=10000)
    parser.add_argument('--batch_size', type=int, default=5)
    parser.add_argument('--max_buffer_size', type=int, default=1000)
    parser.add_argument('--gamma', type=float, default=0.99)
    parser.add_argument('--lr', type=float, default=1e-3)
    parser.add_argument('--clip_epsilon', type=float, default=0.2)
    parser.add_argument('--update_epochs', type=int, default=5)
    parser.add_argument('--hidden_dim', type=int, default=64)
    parser.add_argument('--env_name', type=str, default='CartPole-v1')
    parser.add_argument('--horizon', type=int, default=500)
    parser.add_argument('--log_interval', type=int, default=1)
    parser.add_argument('--model_save_path', type=str, default='results/checkpoints')
    parser.add_argument('--log_dir', type=str, default='results/logs')
    parser.add_argument('--model_save_interval', type=int, default=500)

    args = parser.parse_args()

    #tensorboard_process = subprocess.Popen(["tensorboard", "--logdir", "logs", "--bind_all"])
    subprocess.Popen(['tensorboard --logdir results/logs --bind_all'], shell=True)
    
    # Pass arguments to the function
    main_distributed(
        actor_cpu_num=args.actor_cpu_num,
        actor_gpu_num=args.actor_gpu_num,
        actor_mem_num=args.actor_mem_num*1024*1024*1024,
        learner_cpu_num=args.learner_cpu_num,
        learner_gpu_num=args.learner_gpu_num,
        learner_mem_num=args.learner_mem_num*1024*1024*1024,
        num_actors=args.num_actors,
        episodes=args.episodes,
        batch_size=args.batch_size,
        max_buffer_size=args.max_buffer_size,
        gamma=args.gamma,
        lr=args.lr,
        hidden_dim=args.hidden_dim,
        env_name=args.env_name,
        horizon=args.horizon,
        log_dir=args.log_dir,
        log_interval=args.log_interval,
        model_save_path=args.model_save_path,
        model_save_interval=args.model_save_interval
    )
