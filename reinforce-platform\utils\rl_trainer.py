import logging
import uuid
import json
import os
import time
import tempfile
import paramiko
from datetime import datetime
from pathlib import Path
from typing import Dict, Optional, Any
from utils.resource_manager import ResourceManager
from django.utils import timezone
import environ


# 加载环境变量
env = environ.Env()
# 读取.env文件
BASE_DIR = Path(__file__).resolve().parent.parent
environ.Env.read_env(os.path.join(BASE_DIR, '.env'))

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RLDockerTrainer:
    """
    强化学习训练器类 - 基于资源调度平台的容器化训练解决方案

    特性：
    - 支持NPU和GPU训练环境
    - 实时状态监控和日志获取
    - 支持PPO、SAC、DDPG等强化学习算法

    前端配置文件格式示例:
    {
        "algorithm": {
            "version": "torch-1.8.1",     # 算法版本
            "rlType": "PPO"               # 强化学习算法类型
        },
        "simulation": {
            "dataSource": "内置仿真环境",    # 数据源
            "useExternalEnv": false,      # 是否使用外部环境
            "externalEnvAddress": ""      # 外部环境地址
        },
        "agent": {
            "sampleTime": "0.1",          # 采样时间
            "actionSpace": "离散动作空间", # 动作空间类型
            "observationSpace": "连续观测空间", # 观测空间类型
            "rewardFunction": "标准奖励函数"    # 奖励函数
        },
        "hyperParams": {
            "learningRate": "1e-5",       # 学习率
            "epochs": 5000,               # 训练轮数
            "batchSize": 32,              # 批次大小
            "learningRateStrategy": "余弦衰减", # 学习率策略
            "computeType": "fp32",        # 计算类型
            "validationRatio": 0.2        # 验证集比例
        },
        "cluster": {
            "cpuCount": "4",              # CPU核心数
            "gpuCount": "1"               # GPU数量
        },
        "output": {
            "savePath": "/models/rl_training", # 保存路径
            "saveName": "rl_model_xxx"         # 保存名称
        }
    }

    注意事项:
    1. 脚本从网盘目录复制到workspace目录
    2. 在ray-ppo-job目录下执行ray_train_gym-npu.py脚本进行训练
    3. 支持暂停、继续、停止训练功能
    4. 支持实时获取训练指标
    """

    def __init__(self, training_task):
        """
        初始化训练器
        参数:
            training_task: RLTrainingTask模型实例，包含以下主要字段：
                - training_id: 训练ID
                - algorithm_version: 算法版本
                - rl_type: 强化学习算法类型
                - data_source: 数据源
                - learning_rate: 学习率
                - epochs: 训练轮数
                - batch_size: 批处理大小
                - cpu_count: CPU数量
                - gpu_count: GPU数量
                等...
        """
        self.training_task = training_task
        self.task_info = None

        # 通过资源调度平台请求资源
        self.resource_manager = ResourceManager()

        # SSH连接和远程目录配置
        self.ssh_client = None
        self.remote_dir = "/workspace"
        # 脚本源目录 - 使用环境变量SITON_DATA_MOUNT_PATH下的data目录
        siton_data_mount_path = os.environ.get('SITON_DATA_MOUNT_PATH', '/root')
        self.script_source_dir = siton_data_mount_path +  '/data'



    def _find_product_by_id(self, products: list, specification_id: str) -> Optional[dict]:
        """根据规格ID查找产品"""
        for product in products:
            if product.get('specificationId') == specification_id:
                return product
        return None

    def _find_image_by_name(self, images: list, image_name: str) -> Optional[str]:
        """根据镜像名称查找镜像"""
        for image in images:
            if image == image_name:
                return image
        return None

    def _request_resource(self) -> bool:
        """请求训练资源"""
        try:
            # 1. 查询可用产品
            products = self.resource_manager.query_products()
            if not products:
                logger.error("没有可用的计算资源")
                return False

            # 获取配置参数
            resources = {
                'cpu': int(self.training_task.cpu_count),
                'gpu': int(self.training_task.gpu_count),
                'specification_id': getattr(self.training_task, 'specification_id', None),
                'mirror_image': getattr(self.training_task, 'mirror_image', None),
                'structure': getattr(self.training_task, 'structure', 'amd64')
            }

            # 选择产品
            selected_product = None
            if resources.get('specification_id'):
                selected_product = self._find_product_by_id(products, resources['specification_id'])
                if not selected_product:
                    logger.error(f"未找到指定的产品ID: {resources['specification_id']}")
                    return False
            else:
                # 获取第一个可用产品
                for item in products:
                    if item.get('hoseAlias') == 'node01-910B3':
                        continue
                    else:
                        selected_product = item
                        break

            # 2. 查询镜像信息
            images = self.resource_manager.query_images(selected_product.get('structure', 'amd64'))
            if not images:
                logger.error("没有可用的镜像")
                return False

            selected_image = None
            if resources.get('mirror_image'):
                selected_image = self._find_image_by_name(images, resources['mirror_image'])
                if not selected_image:
                    logger.error(f"未找到指定的镜像: {resources['mirror_image']}")
                    return False
            else:
                # 选择包含强化学习环境的镜像
                for image in images:
                    if any(keyword in image.lower() for keyword in ['pytorch', 'rlray:v1.0']):
                        selected_image = image
                        break
                if not selected_image:
                    selected_image = images[0]

            # 3. 创建任务
            taskname = f"rl训练任务-{uuid.uuid4().hex[:8]}"

            # 使用GPU数量作为specification_amount参数
            specification_amount = int(resources.get('gpuCount', 1))  # 默认为1

            task = self.resource_manager.create_task(
                calculate_device_id=selected_product['calculateDeviceId'],
                specification_id=selected_product['specificationId'],
                mirror_image=selected_image,
                task_name=taskname,
                gpu_mode=selected_product['gpuMode'],
                specification_amount=specification_amount  # 传递GPU数量
            )

            if task.get("code") != "0":
                logger.error("任务创建失败")
                return False

            # 4. 查询任务状态获取详细信息
            task_info = self.resource_manager.query_task(taskname)
            if not task_info or not task_info.get('records'):
                logger.error("获取任务信息失败")
                return False

            self.task_info = {
                'taskName': taskname,
                'taskId': task_info['records'][0]['id'],
                'ip': task_info['records'][0]['agentIp'],
                'port': task_info['records'][0]['sshPort'],
                'password': task_info['records'][0]['sshPasswd'],
                'status': task_info['records'][0]['status'],
            }

            logger.info(f"成功创建强化学习训练任务，任务ID: {task_info['records'][0]['id']}")
            return True

        except Exception as e:
            logger.error(f"请求资源失败: {e}")
            return False

    def _connect_ssh(self):
        """连接到远程服务器"""
        if not self.task_info:
            logger.error("没有任务信息，无法连接SSH")
            return False

        try:
            # 创建SSH客户端
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            # 连接远程服务器
            logger.info(f"正在连接服务器: {self.task_info['ip']}:{self.task_info['port']}")
            time.sleep(10)
            self.ssh_client.connect(
                hostname=self.task_info['ip'],
                port=int(self.task_info['port']),
                username='root',
                password=self.task_info['password'],
                timeout=30
            )
            logger.info("SSH连接成功")
            return True
        except Exception as e:
            logger.error(f"SSH连接失败: {e}")
            return False

    def _prepare_training_environment(self):
        """准备训练环境：创建工作目录、复制训练脚本和配置文件"""
        if not self.ssh_client:
            logger.error("SSH未连接，无法准备训练环境")
            return False

        try:
            # 1. 确保远程工作目录存在
            logger.info(f"创建远程工作目录: {self.remote_dir}")
            mkdir_cmd = f"mkdir -p {self.remote_dir}"
            self.ssh_client.exec_command(mkdir_cmd)

            # 2. 复制ray-ppo-job目录到workspace
            logger.info("复制ray-ppo-job目录到远程工作目录")
            source_path = f"{self.script_source_dir}/ray-ppo-job"
            target_path = f"{self.remote_dir}/ray-ppo-job"

            # 检查源目录是否存在
            check_source_cmd = f"test -d {source_path} && echo 'exists' || echo 'not exists'"
            _, stdout, _ = self.ssh_client.exec_command(check_source_cmd)
            result = stdout.read().decode().strip()

            if result != 'exists':
                logger.error(f"源目录不存在: {source_path}")
                return False

            # 复制目录
            copy_cmd = f"cp -r {source_path} {target_path}"
            _, _, stderr = self.ssh_client.exec_command(copy_cmd)
            err = stderr.read().decode().strip()
            if err:
                logger.error(f"复制ray-ppo-job目录失败: {err}")
                return False

            logger.info("ray-ppo-job目录复制成功")

            # 3. 创建训练配置文件
            config = self._generate_training_config()
            logger.info(f"生成训练配置: {config}")

            # 使用tempfile模块创建临时文件
            fd, temp_path = tempfile.mkstemp(suffix='.json', prefix='rl_training_config_')
            try:
                with os.fdopen(fd, 'w') as temp:
                    json.dump(config, temp, indent=2)

                logger.info(f"创建临时配置文件: {temp_path}")

                # 上传临时文件
                sftp = self.ssh_client.open_sftp()
                remote_config_path = f"{self.remote_dir}/rl_training_config.json"
                sftp.put(temp_path, remote_config_path)
                sftp.close()

                logger.info("训练配置文件上传成功")
            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                    logger.info(f"已删除临时文件: {temp_path}")

            return True

        except Exception as e:
            logger.error(f"准备训练环境失败: {e}")
            return False

    def _generate_training_config(self) -> dict:
        """生成强化学习训练配置"""
        return {
            'algorithm': {
                'version': getattr(self.training_task, 'algorithm_version', 'torch-1.8.1'),
                'rlType': getattr(self.training_task, 'rl_type', 'PPO')
            },
            'simulation': {
                'dataSource': getattr(self.training_task, 'data_source', '内置仿真环境'),
                'useExternalEnv': getattr(self.training_task, 'use_external_env', False),
                'externalEnvAddress': getattr(self.training_task, 'external_env_address', '')
            },
            'agent': {
                'sampleTime': getattr(self.training_task, 'sample_time', '0.1'),
                'actionSpace': getattr(self.training_task, 'action_space', '离散动作空间'),
                'observationSpace': getattr(self.training_task, 'observation_space', '连续观测空间'),
                'rewardFunction': getattr(self.training_task, 'reward_function', '标准奖励函数')
            },
            'hyperParams': {
                'learningRate': getattr(self.training_task, 'learning_rate', '1e-5'),
                'epochs': getattr(self.training_task, 'epochs', 5000),
                'batchSize': getattr(self.training_task, 'batch_size', 32),
                'learningRateStrategy': getattr(self.training_task, 'learning_rate_strategy', '余弦衰减'),
                'computeType': getattr(self.training_task, 'compute_type', 'fp32'),
                'validationRatio': getattr(self.training_task, 'validation_ratio', 0.2)
            },
            'cluster': {
                'cpuCount': getattr(self.training_task, 'cpu_count', '4'),
                'gpuCount': getattr(self.training_task, 'gpu_count', '1')
            },
            'output': {
                'savePath': getattr(self.training_task, 'save_path', '/models/rl_training'),
                'saveName': getattr(self.training_task, 'save_name', f'rl_model_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
            }
        }

    def _execute_training_script(self):
        """执行强化学习训练脚本"""
        if not self.ssh_client:
            logger.error("SSH未连接，无法执行训练脚本")
            return False

        try:
            # 1. 设置环境变量命令（针对NPU环境）
            set_env_cmd = 'source /usr/local/Ascend/ascend-toolkit/set_env.sh && export LD_LIBRARY_PATH=/usr/local/Ascend/driver/lib64/driver/:/usr/local/python3.10.17/lib/:$LD_LIBRARY_PATH'

            # 2. 构建训练命令
            task_id = f'rl_task_{self.training_task.training_id}'
            config_path = f'{self.remote_dir}/rl_training_config.json'

            # 在ray-ppo-job目录下执行ray_train_gym-npu.py脚本
            command = (
                f"cd {self.remote_dir}/ray-ppo-job && "
                f"python ray_train_gym-npu.py "
                f"--config {config_path} "
                f"--task_id {task_id} "
                f"--use-npu"
            )

            logger.info(f"执行强化学习训练命令: {command}")

            # 3. 使用nohup后台运行训练脚本，防止SSH断开导致训练中断
            run_cmd = f"nohup bash -c '{set_env_cmd} && {command}' > {self.remote_dir}/rl_train.log 2>&1 &"
            _, _, stderr = self.ssh_client.exec_command(run_cmd)

            # 检查是否有错误
            err = stderr.read().decode().strip()
            if err:
                logger.error(f"执行训练命令出错: {err}")
                return False

            logger.info("强化学习训练脚本已在后台启动")
            return True

        except Exception as e:
            logger.error(f"执行训练脚本失败: {e}")
            return False

    def get_training_metrics(self, max_retries=3) -> Dict:
        """
        从远程服务器获取强化学习训练指标数据
        Args:
            max_retries: 最大重试次数
        返回:
            dict: 包含训练指标的字典，如果获取失败则返回空字典
        """
        logger.info("获取最新强化学习训练指标数据")

        # 如果没有任务信息或SSH连接，返回空字典
        if not self.task_info or not self.ssh_client:
            logger.warning("无法获取训练指标：任务未启动或SSH未连接")
            return {}

        # 重试机制
        for attempt in range(max_retries):
            try:
                logger.info(f"第{attempt+1}次尝试获取强化学习训练指标数据")

                # 检查metrics文件是否存在
                check_cmd = f"test -f {self.remote_dir}/rl_training_metrics.json && echo 'exists' || echo 'not exists'"
                _, stdout, _ = self.ssh_client.exec_command(check_cmd)
                result = stdout.read().decode().strip()

                if result != 'exists':
                    logger.warning(f"第{attempt+1}次尝试：强化学习训练指标文件不存在")
                    if attempt < max_retries - 1:
                        time.sleep(1)  # 等待1秒后重试
                        continue
                    return {}

                # 读取训练指标文件
                read_cmd = f"cat {self.remote_dir}/rl_training_metrics.json"
                _, stdout, _ = self.ssh_client.exec_command(read_cmd)

                # 解析JSON数据
                metrics_json = stdout.read().decode()
                if not metrics_json:
                    logger.warning(f"第{attempt+1}次尝试：强化学习训练指标文件为空")
                    if attempt < max_retries - 1:
                        time.sleep(1)  # 等待1秒后重试
                        continue
                    return {}

                # 处理可能包含多行JSON的情况
                metrics_lines = metrics_json.strip().split('\n')
                metrics_list = []

                for line in metrics_lines:
                    if line.strip():
                        try:
                            metric_data = json.loads(line)
                            metrics_list.append(metric_data)
                        except json.JSONDecodeError:
                            logger.warning(f"无法解析强化学习训练指标行: {line}")

                if not metrics_list:
                    logger.warning(f"第{attempt+1}次尝试：未找到有效的强化学习训练指标数据")
                    if attempt < max_retries - 1:
                        time.sleep(1)  # 等待1秒后重试
                        continue
                    return {}

                # 提取强化学习训练指标并组织成合适的格式
                episodes = []
                cumulative_rewards = []
                policy_losses = []
                value_losses = []
                entropies = []
                timestamps = []
                cpu_usages = []
                npu_usages = []
                memory_usages = []

                for metric in metrics_list:
                    # 收集强化学习训练指标
                    episodes.append(metric.get('episode', 1))
                    cumulative_rewards.append(metric.get('cumulative_reward', 0.0))
                    policy_losses.append(metric.get('policy_loss', 0.0))
                    value_losses.append(metric.get('value_loss', 0.0))
                    entropies.append(metric.get('entropy', 0.0))

                    # 资源使用指标
                    cpu_usages.append(metric.get('cpu_usage', 0.0))
                    npu_usages.append(metric.get('npu_usage', 0.0))
                    memory_usages.append(metric.get('memory_usage', 0.0))

                    # 时间戳 - 确保格式一致
                    timestamp = metric.get('timestamp', time.time())
                    if isinstance(timestamp, (int, float)):
                        # 如果是时间戳数字，转换为datetime对象
                        timestamp = datetime.fromtimestamp(timestamp)
                    timestamps.append(timestamp)

                # 构建返回的指标数据
                metrics_data = {
                    'episodes': episodes,
                    'cumulative_rewards': cumulative_rewards,
                    'policy_losses': policy_losses,
                    'value_losses': value_losses,
                    'entropies': entropies,
                    'timestamps': timestamps,
                    'cpu_usages': cpu_usages,
                    'npu_usages': npu_usages,
                    'memory_usages': memory_usages,
                    'average_reward': metrics_list[-1].get('average_reward', 0.0),
                    'episode_length': metrics_list[-1].get('episode_length', 0),
                    'exploration_rate': metrics_list[-1].get('exploration_rate', 0.0)
                }

                logger.info(f"第{attempt+1}次尝试成功获取强化学习训练指标，包含 {len(episodes)} 个episode")

                # 保存指标到数据库
                self.save_metrics_to_database(metrics_list)

                return metrics_data

            except Exception as parse_error:
                logger.error(f"第{attempt+1}次尝试解析强化学习训练指标数据时发生错误: {parse_error}")
                if attempt < max_retries - 1:
                    time.sleep(1)  # 等待1秒后重试
                    continue
                return {}

        # 所有重试都失败了
        logger.error(f"经过 {max_retries} 次重试，仍无法获取强化学习训练指标")
        return {}

    def get_task_status(self) -> Dict:
        """获取资源调度平台创建任务状态"""
        try:
            if not self.task_info:
                return {'status': 'not_started'}

            # 首先检查数据库中的状态，如果是暂停相关状态，优先返回数据库状态
            if hasattr(self, 'training_task') and self.training_task:
                # 刷新数据库状态，确保获取最新状态
                self.training_task.refresh_from_db()
                db_status = self.training_task.status
                logger.info(f"数据库状态为：{db_status}")
                if db_status in ['paused', 'pausing', 'resuming', 'cancelling', 'cancelled']:
                    logger.info(f"数据库状态为 {db_status}，优先返回数据库状态而不是资源平台状态")
                    return {'status': db_status}

            # 通过资源调度平台API查询任务状态
            task_info = self.resource_manager.query_task(self.task_info['taskName'])
            if not task_info or not task_info.get('records'):
                return {'status': 'unknown', 'error': '无法获取任务信息'}

            task_status = task_info['records'][0].get('status')

            status_mapping = {
                1: 'downloading',  # 镜像下载中
                2: 'packaging',    # 镜像打包中
                3: 'creating',     # 创建中
                4: 'running',      # 运行中
                5: 'shutting_down', # 关机中
                6: 'shutdown',     # 已关机
                7: 'unavailable',  # 不可用
                8: 'releasing',    # 释放中
                9: 'no_card_mode', # 无卡模式
                10: 'starting',    # 正在开机
                11: 'restarting',  # 重启中
                12: 'starting_no_card', # 无卡模式启动中
                13: 'resetting',   # 重置系统中
                14: 'upgrading'    # 升配中
            }

            # 获取资源平台状态
            status_str = status_mapping.get(task_status, 'unknown')

            # 如果任务正在运行，尝试检查训练是否完成
            if status_str == 'running' and self.ssh_client:
                try:
                    # 获取训练配置的总轮数
                    total_episodes = self.training_task.epochs

                    # 首先检查训练进程是否还在运行
                    process_cmd = "ps aux | grep ray_train_gym-npu.py | grep -v grep"
                    _, stdout, _ = self.ssh_client.exec_command(process_cmd)
                    processes = stdout.read().decode().strip()

                    # 检查训练指标文件是否存在
                    check_cmd = f"test -f {self.remote_dir}/rl_training_metrics.json && echo 'exists' || echo 'not exists'"
                    _, stdout, _ = self.ssh_client.exec_command(check_cmd)
                    result = stdout.read().decode().strip()

                    if result == 'exists':
                        # 读取训练指标文件
                        read_cmd = f"cat {self.remote_dir}/rl_training_metrics.json"
                        _, stdout, _ = self.ssh_client.exec_command(read_cmd)
                        metrics_json = stdout.read().decode()

                        # 解析JSON数据
                        if metrics_json:
                            metrics_lines = metrics_json.strip().split('\n')
                            if metrics_lines:
                                # 获取最后一行的指标数据
                                try:
                                    last_metric = json.loads(metrics_lines[-1])
                                    current_episode = last_metric.get('episode', 1)

                                    # 确保类型转换正确
                                    current_episode = int(current_episode)  # 确保是整数
                                    total_episodes_int = int(total_episodes)  # 确保是整数

                                    # 检查是否已完成所有训练轮数
                                    if current_episode == total_episodes_int:  # episode从1开始计数
                                        logger.info(f"强化学习训练已完成! 当前episode: {current_episode}, 总episodes: {total_episodes_int}")
                                        status_str = 'completed'

                                        # 更新数据库中的训练任务状态
                                        self._update_training_task_status('completed')

                                        # 如果训练进程已经结束，确认为完成状态
                                        if not processes:
                                            logger.info("训练进程已结束，确认训练完成")
                                            # 异步保存训练模型信息到数据库（避免阻塞SSE）
                                            logger.info("强化学习训练完成，启动异步任务保存模型信息到数据库")
                                            self.schedule_rl_model_save_task()
                                        else:
                                            logger.info("训练轮数已达到，但进程仍在运行，等待进程结束")
                                    else:
                                        logger.debug(f"强化学习训练进行中: episode {current_episode}/{total_episodes_int}")

                                        # 如果进程不存在但训练未完成，可能是异常情况
                                        if not processes:
                                            logger.warning(f"训练进程已结束但训练未完成 (episode {current_episode}/{total_episodes_int})，可能训练失败")
                                            status_str = 'failed'
                                            # 更新数据库中的训练任务状态
                                            self._update_training_task_status('failed')

                                except Exception as e:
                                    logger.error(f"解析强化学习训练指标失败: {e}")
                except Exception as e:
                    logger.error(f"检查强化学习训练完成状态时出错: {e}")

            return {
                'status': status_str,
                'task_id': self.task_info['taskId'],
                'ip': self.task_info['ip'],
                'port': self.task_info['port']
            }

        except Exception as e:
            logger.error(f"获取任务状态失败: {e}")
            return {'status': 'unknown', 'error': str(e)}

    def get_training_logs(self, lines=100, mode='tail'):
        """
        获取强化学习训练日志
        Args:
            lines: 获取的行数
            mode: 获取模式 ('tail': 最后几行, 'head': 前几行, 'all': 全部)
        Returns:
            str: 日志内容
        """
        if not self.task_info or not self.ssh_client:
            return "任务未启动或SSH未连接"

        try:
            # 确保工作目录存在
            mkdir_cmd = f"mkdir -p {self.remote_dir}"
            self.ssh_client.exec_command(mkdir_cmd)

            # 检查日志文件是否存在
            check_cmd = f"test -f {self.remote_dir}/rl_train.log && echo 'exists' || echo 'not exists'"
            _, stdout, _ = self.ssh_client.exec_command(check_cmd)
            result = stdout.read().decode().strip()

            if result != 'exists':
                return "强化学习训练日志文件不存在，可能训练尚未开始或日志未生成"

            # 根据模式选择命令
            if mode == 'all':
                command = f"cat {self.remote_dir}/rl_train.log"
            elif mode == 'head':
                command = f"head -n {lines} {self.remote_dir}/rl_train.log"
            else:  # tail (默认)
                command = f"tail -n {lines} {self.remote_dir}/rl_train.log"

            _, stdout, _ = self.ssh_client.exec_command(command)

            # 读取输出
            logs = stdout.read().decode()
            return logs if logs else "暂无日志"
        except Exception as e:
            logger.error(f"获取强化学习训练日志失败: {e}")
            return f"获取日志失败: {str(e)}"

    def start(self) -> bool:
        """启动强化学习训练任务"""
        try:
            # 1. 请求资源创建任务
            if not self._request_resource():
                return False

            # 2. 等待一段时间，确保服务器启动完成
            logger.info("等待服务器启动...")
            time.sleep(10)

            # 3. 建立SSH连接
            if not self._connect_ssh():
                return False

            # 4. 准备训练环境（创建工作目录和配置文件）
            if not self._prepare_training_environment():
                return False

            # 5. 执行训练脚本
            if not self._execute_training_script():
                return False

            logger.info("强化学习训练任务已成功启动")
            return True

        except Exception as e:
            logger.error(f"启动强化学习训练任务失败: {e}")
            return False

    def stop(self) -> bool:
        """停止强化学习训练任务"""
        try:
            if not self.task_info:
                logger.warning("没有运行中的任务")
                return True

            # 先通过SSH停止训练进程
            if self.ssh_client:
                try:
                    # 尝试保存模型信息（如果有的话）
                    try:
                        logger.info("尝试保存强化学习模型信息...")
                        self.save_rl_models_to_db()
                    except Exception as save_error:
                        logger.warning(f"保存强化学习模型信息失败: {save_error}")

                    self.ssh_client.exec_command("pkill -f ray_train_gym-npu.py")
                    logger.info("已发送停止强化学习训练进程命令")

                    # 清理模型信息文件
                    try:
                        self.cleanup_rl_model_info_file()
                    except Exception as cleanup_error:
                        logger.warning(f"清理强化学习模型信息文件失败: {cleanup_error}")

                    # 关闭SSH连接
                    self.ssh_client.close()
                    self.ssh_client = None
                except:
                    logger.warning("停止强化学习训练进程失败，继续关闭任务")

            # 通过资源调度平台停止任务
            success = self.resource_manager.stop_task(self.task_info['taskId'])
            if success.get("code") == "0":
                logger.info(f"任务 {self.task_info['taskId']} 已停止")
                # 删除任务
                time.sleep(5)  # 等待5秒，确保任务已经完全停止
                status = self.resource_manager.delete_task(self.task_info['taskId'])
                if status.get("code") == "0":
                    logger.info(f"任务 {self.task_info['taskId']} 已删除")
                    return True
                else:
                    logger.info(f"任务 {self.task_info['taskId']} 删除失败")
                    return False
            else:
                logger.error(f"停止任务 {self.task_info['taskId']} 失败")
                return False

        except Exception as e:
            logger.error(f"停止强化学习训练任务失败: {e}")
            return False

    def pause(self) -> bool:
        """暂停强化学习训练任务（通过SSH杀掉训练进程）"""
        try:
            if not self.task_info:
                logger.warning("没有运行中的任务")
                return True

            # 通过SSH连接到服务器并杀掉训练进程
            if self.ssh_client:
                try:
                    # 查找并杀掉训练进程
                    # 首先查找进程
                    _, stdout, _ = self.ssh_client.exec_command("ps aux | grep ray_train_gym-npu.py | grep -v grep")
                    processes = stdout.read().decode().strip()

                    if processes:
                        logger.info(f"找到强化学习训练进程: {processes}")

                        # 杀掉训练进程（使用SIGTERM信号，允许进程优雅退出）
                        _, stdout, _ = self.ssh_client.exec_command("pkill -f ray_train_gym-npu.py")

                        # 等待命令执行完成
                        exit_status = stdout.channel.recv_exit_status()

                        if exit_status == 0:
                            logger.info("强化学习训练进程已成功暂停")
                            return True
                        else:
                            # 如果SIGTERM失败，尝试使用SIGKILL强制杀掉
                            logger.warning("优雅停止失败，尝试强制停止")
                            _, stdout, _ = self.ssh_client.exec_command("pkill -9 -f ray_train_gym-npu.py")
                            exit_status = stdout.channel.recv_exit_status()

                            if exit_status == 0:
                                logger.info("强化学习训练进程已强制停止")
                                return True
                            else:
                                logger.error("无法停止强化学习训练进程")
                                return False
                    else:
                        logger.info("未找到运行中的强化学习训练进程")
                        return True

                except Exception as ssh_error:
                    logger.error(f"SSH执行暂停命令失败: {ssh_error}")
                    return False
            else:
                logger.error("SSH连接不可用")
                return False

        except Exception as e:
            logger.error(f"暂停强化学习训练任务失败: {e}")
            return False

    def resume(self) -> bool:
        """继续强化学习训练任务（重新执行训练脚本并加上--resume True参数）"""
        try:
            if not self.task_info:
                logger.warning("没有任务信息，无法继续训练")
                return False

            # 通过SSH连接到服务器并重新启动训练
            if self.ssh_client:
                try:
                    # 构建继续训练的命令
                    task_id = f'rl_task_{self.training_task.training_id}'
                    config_path = f'{self.remote_dir}/rl_training_config.json'

                    # 设置环境变量命令
                    set_env_cmd = 'source /usr/local/Ascend/ascend-toolkit/set_env.sh && export LD_LIBRARY_PATH=/usr/local/Ascend/driver/lib64/driver/:/usr/local/python3.10.17/lib/:$LD_LIBRARY_PATH'

                    # 构建继续训练命令，添加--resume True参数
                    resume_command = (
                        f"{set_env_cmd} && "
                        f"cd {self.remote_dir}/ray-ppo-job && "
                        f"python ray_train_gym-npu.py "
                        f"--config {config_path} "
                        f"--task_id {task_id} "
                        f"--resume True "
                        f"> {self.remote_dir}/rl_train.log 2>&1 &"
                    )

                    logger.info(f"执行继续强化学习训练命令: {resume_command}")

                    # 执行继续训练命令
                    _, stdout, stderr = self.ssh_client.exec_command(resume_command)

                    # 等待命令执行完成
                    exit_status = stdout.channel.recv_exit_status()

                    if exit_status == 0:
                        logger.info("继续强化学习训练命令执行成功")

                        # 等待一段时间，然后检查进程是否启动
                        time.sleep(3)

                        # 检查训练进程是否启动
                        _, stdout, _ = self.ssh_client.exec_command(
                            "ps aux | grep ray_train_gym-npu.py | grep -v grep"
                        )
                        processes = stdout.read().decode().strip()

                        if processes:
                            logger.info(f"强化学习训练进程已启动: {processes}")
                            return True
                        else:
                            logger.warning("强化学习训练进程未能启动")
                            return False
                    else:
                        error_output = stderr.read().decode().strip()
                        logger.error(f"继续强化学习训练命令执行失败，退出状态: {exit_status}, 错误: {error_output}")
                        return False

                except Exception as ssh_error:
                    logger.error(f"SSH执行继续强化学习训练命令失败: {ssh_error}")
                    return False
            else:
                logger.error("SSH连接不可用")
                return False

        except Exception as e:
            logger.error(f"继续强化学习训练任务失败: {e}")
            return False

    def get_task_id(self) -> Optional[str]:
        """获取任务ID"""
        return self.task_info.get('taskId') if self.task_info else None

    def get_task_info(self) -> Optional[Dict]:
        """获取完整的任务信息"""
        if not self.task_info:
            return None

        try:
            # 从资源调度平台获取最新的任务信息
            task_info = self.resource_manager.query_task(self.task_info['taskId'])
            if task_info and task_info.get('records'):
                return task_info['records'][0]
            return None
        except Exception as e:
            logger.error(f"获取任务信息失败: {e}")
            return self.task_info

    def schedule_rl_model_save_task(self):
        """
        调度异步任务保存强化学习模型信息，避免阻塞SSE流程
        """
        try:
            import threading

            def delayed_save():
                """延迟保存强化学习模型信息的后台任务"""
                try:
                    # 等待一段时间确保文件生成
                    logger.info("等待30秒确保强化学习模型信息文件完全生成...")
                    time.sleep(30)

                    # 尝试保存模型信息
                    max_retries = 3
                    for attempt in range(max_retries):
                        logger.info(f"第{attempt+1}次尝试保存强化学习模型信息到数据库...")

                        if self.save_rl_models_to_db():
                            logger.info("强化学习模型信息保存成功")
                            break
                        else:
                            if attempt < max_retries - 1:
                                logger.warning(f"第{attempt+1}次保存失败，10秒后重试...")
                                time.sleep(10)
                            else:
                                logger.error("所有保存尝试都失败了")

                except Exception as e:
                    logger.error(f"异步保存强化学习模型信息失败: {e}")

            # 启动后台线程
            save_thread = threading.Thread(target=delayed_save, daemon=True)
            save_thread.start()
            logger.info("已启动后台线程保存强化学习模型信息")

        except Exception as e:
            logger.error(f"启动异步保存任务失败: {e}")
            # 如果异步失败，尝试同步保存一次
            try:
                self.save_rl_models_to_db()
            except:
                logger.error("同步保存也失败了")

    def get_rl_model_info(self):
        """
        获取强化学习模型信息

        Returns:
            list: 模型信息列表
        """
        try:
            if not self.ssh_client:
                logger.warning("SSH连接不可用，无法获取强化学习模型信息")
                return []

            model_info_file = "/workspace/rl_model_info.json"

            # 检查模型信息文件是否存在
            check_cmd = f"test -f {model_info_file} && echo 'exists' || echo 'not exists'"
            _, stdout, _ = self.ssh_client.exec_command(check_cmd)
            result = stdout.read().decode().strip()

            if result != 'exists':
                logger.warning(f"强化学习模型信息文件不存在: {model_info_file}")
                return []

            # 读取模型信息文件
            read_cmd = f"cat {model_info_file}"
            _, stdout, _ = self.ssh_client.exec_command(read_cmd)
            content = stdout.read().decode().strip()

            if not content:
                logger.warning("强化学习模型信息文件为空")
                return []

            models_info = []
            # 处理多行JSON格式（每行一个JSON对象）
            for line in content.split('\n'):
                if line.strip():
                    try:
                        model_info = json.loads(line)
                        models_info.append(model_info)
                    except json.JSONDecodeError as e:
                        logger.error(f"解析模型信息行失败: {line}, 错误: {e}")
                        continue

            logger.info(f"成功读取 {len(models_info)} 个强化学习模型信息")
            return models_info

        except Exception as e:
            logger.error(f"读取强化学习模型信息失败: {e}")
            return []

    def save_rl_models_to_db(self):
        """
        将强化学习模型信息保存到数据库

        Returns:
            bool: 保存成功返回True，否则返回False
        """
        try:
            # 导入模型
            from backend_api.models.rl_training import RLTrainingModel
            from django.utils import timezone

            # 获取模型信息
            models_data = self.get_rl_model_info()
            if not models_data:
                logger.warning("没有强化学习模型数据可保存")
                return False

            saved_count = 0

            # 处理每个模型信息
            for model_info in models_data:
                # 检查是否属于当前训练任务
                if model_info.get('task_id') != f'rl_task_{self.training_task.training_id}':
                    continue

                # 检查模型是否已存在
                model_name = model_info.get('model_name', '')
                existing_model = RLTrainingModel.objects.filter(
                    task=self.training_task,
                    model_name=model_name
                ).first()

                # 提取模型信息
                model_path = model_info.get('model_path', '')
                checkpoint_path = model_info.get('checkpoint_path', '')
                algorithm_type = model_info.get('algorithm_type', 'PPO')
                training_iterations = model_info.get('training_iterations', 0)
                best_episode_reward = model_info.get('final_reward', 0.0)  # 映射到正确的字段名
                is_best = model_info.get('is_best', True)

                # 将额外信息存储到metadata中
                metadata = model_info.copy()  # 保留原始信息
                metadata.update({
                    'training_time': model_info.get('training_time', 0.0),
                    'hyperparameters': model_info.get('hyperparameters', {}),
                    'environment': model_info.get('environment', 'Unknown'),
                    'device_type': model_info.get('device_type', 'cpu')
                })

                if existing_model:
                    # 更新现有模型记录
                    existing_model.model_path = model_path
                    existing_model.checkpoint_path = checkpoint_path
                    existing_model.algorithm_type = algorithm_type
                    existing_model.training_iterations = training_iterations
                    existing_model.final_reward = final_reward
                    existing_model.training_time = training_time
                    existing_model.hyperparameters = hyperparameters
                    existing_model.environment = environment
                    existing_model.device_type = device_type
                    existing_model.is_best = is_best
                    existing_model.status = 'completed'
                    existing_model.completed_time = timezone.now()
                    existing_model.metadata = model_info
                    existing_model.updated_time = timezone.now()
                    existing_model.save()
                    logger.info(f"更新强化学习模型记录: {model_name}")
                else:
                    # 创建新模型记录
                    RLTrainingModel.objects.create(
                        task=self.training_task,
                        model_name=model_name,
                        model_path=model_path,
                        checkpoint_path=checkpoint_path,
                        algorithm_type=algorithm_type,
                        training_iterations=training_iterations,
                        final_reward=final_reward,
                        training_time=training_time,
                        hyperparameters=hyperparameters,
                        environment=environment,
                        device_type=device_type,
                        is_best=is_best,
                        status='completed',
                        completed_time=timezone.now(),
                        metadata=model_info,
                        created_time=timezone.now(),
                        updated_time=timezone.now()
                    )
                    logger.info(f"创建强化学习模型记录: {model_name}")

                saved_count += 1

            logger.info(f"已保存 {saved_count} 条强化学习模型记录")
            return saved_count > 0

        except Exception as e:
            logger.error(f"保存强化学习模型到数据库失败: {e}")
            return False

    def save_metrics_to_database(self, metrics_list):
        """将训练指标保存到数据库"""
        try:
            from backend_api.models.rl_training import RLTrainingMetrics, RLResourceMetrics

            saved_count = 0
            for metrics in metrics_list:
                try:
                    episode = metrics.get('episode', 1)

                    # 检查是否已存在该episode的指标
                    existing_metrics = RLTrainingMetrics.objects.filter(
                        task=self.training_task,
                        episode=episode
                    ).first()

                    if existing_metrics:
                        # 更新现有指标
                        existing_metrics.cumulative_reward = metrics.get('cumulative_reward', 0.0)
                        existing_metrics.average_reward = metrics.get('average_reward')
                        existing_metrics.episode_length = metrics.get('episode_length')
                        existing_metrics.exploration_rate = metrics.get('exploration_rate')
                        existing_metrics.policy_loss = metrics.get('policy_loss')
                        existing_metrics.value_loss = metrics.get('value_loss')
                        existing_metrics.entropy = metrics.get('entropy')
                        existing_metrics.save()
                        logger.debug(f"更新强化学习训练指标: episode {episode}")
                    else:
                        # 创建新指标记录
                        RLTrainingMetrics.objects.create(
                            task=self.training_task,
                            episode=episode,
                            cumulative_reward=metrics.get('cumulative_reward', 0.0),
                            average_reward=metrics.get('average_reward'),
                            episode_length=metrics.get('episode_length'),
                            exploration_rate=metrics.get('exploration_rate'),
                            policy_loss=metrics.get('policy_loss'),
                            value_loss=metrics.get('value_loss'),
                            entropy=metrics.get('entropy')
                        )
                        logger.debug(f"创建强化学习训练指标: episode {episode}")

                    # 保存资源指标
                    if metrics.get('cpu_usage') is not None:
                        RLResourceMetrics.objects.create(
                            task=self.training_task,
                            cpu_utilization=metrics.get('cpu_usage', 0.0),
                            npu_utilization=metrics.get('npu_usage', 0.0),
                            memory_utilization=metrics.get('memory_usage', 0.0),
                            network_io=metrics.get('network_io'),
                            disk_io=metrics.get('disk_io')
                        )
                        logger.debug(f"创建强化学习资源指标: episode {episode}")

                    saved_count += 1

                except Exception as metric_error:
                    logger.warning(f"保存单个指标失败 (episode {metrics.get('episode', 'unknown')}): {metric_error}")
                    continue

            logger.info(f"成功保存 {saved_count} 条强化学习训练指标到数据库")
            return saved_count > 0

        except Exception as e:
            logger.error(f"保存强化学习训练指标到数据库失败: {e}")
            return False

    def _update_training_task_status(self, status):
        """更新数据库中的训练任务状态"""
        try:
            if self.training_task:
                old_status = self.training_task.status
                if old_status != status:
                    self.training_task.status = status

                    # 如果是完成状态，设置结束时间
                    if status in ['completed', 'failed', 'stopped']:
                        self.training_task.end_time = timezone.now()

                    self.training_task.save()
                    logger.info(f"强化学习训练任务状态已更新: {old_status} -> {status}")
                    return True
                else:
                    logger.debug(f"强化学习训练任务状态无变化: {status}")
                    return False
        except Exception as e:
            logger.error(f"更新强化学习训练任务状态失败: {e}")
            return False

    def cleanup_rl_model_info_file(self):
        """
        清理强化学习模型信息文件
        """
        try:
            if not self.ssh_client:
                logger.warning("SSH连接不可用，无法清理强化学习模型信息文件")
                return

            model_info_file = "/workspace/rl_model_info.json"

            # 检查文件是否存在
            check_cmd = f"test -f {model_info_file} && echo 'exists' || echo 'not exists'"
            _, stdout, _ = self.ssh_client.exec_command(check_cmd)
            result = stdout.read().decode().strip()

            if result == 'exists':
                # 删除文件
                rm_cmd = f"rm -f {model_info_file}"
                _, _, stderr = self.ssh_client.exec_command(rm_cmd)
                err = stderr.read().decode().strip()

                if not err:
                    logger.info(f"已清理强化学习模型信息文件: {model_info_file}")
                else:
                    logger.error(f"清理强化学习模型信息文件失败: {err}")
            else:
                logger.info("强化学习模型信息文件不存在，无需清理")

        except Exception as e:
            logger.error(f"清理强化学习模型信息文件失败: {e}")


def create_rl_training_task(config_data: Dict[str, Any], user):
    """根据配置创建强化学习训练任务"""
    from backend_api.models.rl_training import RLTrainingTask
    
    # 生成唯一的训练ID
    training_id = f"rl_train_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:4]}"
    
    # 解析配置数据
    algorithm = config_data.get('algorithm', {})
    simulation = config_data.get('simulation', {})
    agent = config_data.get('agent', {})
    hyper_params = config_data.get('hyperParams', {})
    cluster = config_data.get('cluster', {})
    output = config_data.get('output', {})
    
    # 准备创建参数
    create_params = {
        'training_id': training_id,
        
        # 算法配置
        'algorithm_version': algorithm.get('version', 'torch-1.8.1'),
        'rl_type': algorithm.get('rlType', 'PPO'),
        
        # 仿真配置
        'data_source': simulation.get('dataSource', '内置仿真环境'),
        'use_external_env': simulation.get('useExternalEnv', False),
        'external_env_address': simulation.get('externalEnvAddress', ''),
        
        # 智能体配置
        'sample_time': str(agent.get('sampleTime', '0.1')),
        'action_space': str(agent.get('actionSpace', '离散动作空间')),
        'observation_space': str(agent.get('observationSpace', '连续观测空间')),
        'reward_function': agent.get('rewardFunction', '标准奖励函数'),
        
        # 超参数配置（处理零值和默认值）
        'learning_rate': str(hyper_params.get('learningRate', '1e-5')),
        'epochs': int(hyper_params.get('epochs', 5000)),
        'batch_size': max(1, int(hyper_params.get('batchSize', 32))),  # 确保批次大小至少为1
        'learning_rate_strategy': hyper_params.get('learningRateStrategy', '余弦衰减'),
        'compute_type': hyper_params.get('computeType', 'fp32'),
        'validation_ratio': max(0.0, min(1.0, float(hyper_params.get('validationRatio', 0.2)))),  # 限制在0-1之间
        
        # 集群配置
        'cpu_count': cluster.get('cpuCount', '4'),
        'gpu_count': cluster.get('gpuCount', '1'),
        
        # 输出配置（处理空字符串）
        'save_path': output.get('savePath') or '/models/rl_training',
        'save_name': output.get('saveName') or f'rl_model_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
    }
    
    # 处理用户信息
    if user and hasattr(user, 'id') and user.id:
        create_params['created_by'] = user
    else:
        # 为匿名用户创建或获取默认用户
        from django.contrib.auth import get_user_model
        User = get_user_model()
        default_user, _ = User.objects.get_or_create(
            username='system_default',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'System',
                'last_name': 'Default'
            }
        )
        create_params['created_by'] = default_user
    
    # 创建训练任务
    task = RLTrainingTask.objects.create(**create_params)
    
    return task 