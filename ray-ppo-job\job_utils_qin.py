import yaml
from kubernetes import client,config
from kubernetes.client.rest import ApiException
import copy



def calculate_worker_configuration(
    actor_num, actor_cpu_num, actor_gpu_num, actor_mem_num, 
    learner_num, learner_cpu_num, learner_gpu_num, learner_mem_num):
    worker_configurations=[]

    worker_configurations.append({
        'replicas': learner_num,
        'minReplicas': learner_num,
        'maxReplicas': learner_num,
        'groupName':'learner-group',
        'cpu': learner_cpu_num,
        'memory': f"{learner_mem_num}Gi",
        'gpu': learner_gpu_num,
        'type': 'learner'
    })
    # 分配 Actors 到当前节点
    worker_configurations.append({
        'replicas': actor_num,
        'minReplicas':actor_num,
        'maxReplicas':actor_num,
        'groupName':'actor-group',
        'cpu': actor_cpu_num,
        'memory': f"{actor_mem_num}Gi",
        'gpu':  actor_gpu_num,
        'type': 'actor'
    })
        
    return worker_configurations


def submit_ray_job(
                   entrypoint: str = 'python /root/ray-gym/train.py',
                   run_image: str = 'core.**************.nip.io:30670/public/ray-gym-cartpole:latest',
                   ray_version: str = '2.7.0',
                   actor_num: int = 64,
                   actor_cpu_num: int = 1,
                   actor_gpu_num: int = 0,
                   actor_mem_num: int = 2,
                   learner_num: int = 1,
                   learner_cpu_num: int = 4,
                   learner_gpu_num: int = 1,
                   learner_mem_num: int = 4,
                   namespace: str = 'rl-platform',
                   ray_job_name: str = 'ray-job',
                   mount_path: str ='/root/ray-gym'
                   ):
    
    config.load_kube_config()
    api_instance = client.CustomObjectsApi()
    
    # 动态计算 worker 节点的配置
    worker_configurations = calculate_worker_configuration(
        actor_num,actor_cpu_num, actor_gpu_num, actor_mem_num, 
        learner_num,learner_cpu_num, learner_gpu_num, learner_mem_num)

    # 根据计算的配置更新 YAML
    worker_specs = []
    for conf in worker_configurations:
        worker_spec = {
            'replicas': conf['replicas'],
            'minReplicas': conf['minReplicas'],
            'maxReplicas': conf['maxReplicas'],
            'groupName': conf['groupName'],
            'rayStartParams': {},
            'template': {
                'spec': {
                    'containers': [{
                        'name': 'ray-worker',
                        'image': run_image,
                        'imagePullPolicy': 'IfNotPresent',
                        'resources': {
                            'limits': {
                                'cpu': conf['cpu'],
                                'memory': conf['memory'],
                                'nvidia.com/gpu': conf['gpu'] if conf['gpu'] > 0 else None
                            },
                            'requests': {
                                'cpu': conf['cpu'],
                                'memory': conf['memory'],
                                'nvidia.com/gpu': conf['gpu'] if conf['gpu'] > 0 else None
                            }
                        },
                        'volumeMounts': [{
                            'name': 'shared-volume',
                            'mountPath': mount_path  
                        }]
                       
                  
                        
                    }],
                    'volumes':[{
                       'name': 'shared-volume',
                       'persistentVolumeClaim':{
                           'claimName': 'common-pvc'
                        }
                    }]
                  
              
                }
            }
        }
        worker_specs.append(worker_spec)
    
   


    # Load the YAML template
    with open('job.yaml') as f:
        ray_job_yaml = yaml.safe_load(f)

    entrypoint += f' --num_actors {actor_num} --actor_cpu_num {actor_cpu_num} --actor_gpu_num {actor_gpu_num} --actor_mem_num {actor_mem_num} --learner_cpu_num {learner_cpu_num} --learner_gpu_num {learner_gpu_num} --learner_mem_num {learner_mem_num}'
    ray_job_yaml['spec']['rayClusterSpec']['workerGroupSpecs'] = worker_specs

    # Update YAML fields dynamically
    ray_job_yaml['metadata']['name'] = ray_job_name
    ray_job_yaml['metadata']['namespace'] = namespace
    ray_job_yaml['spec']['entrypoint']  =entrypoint
    ray_job_yaml['spec']['rayClusterSpec']['rayVersion'] = ray_version
    ray_job_yaml['spec']['rayClusterSpec']['headGroupSpec']['template']['spec']['containers'][0]['image'] = run_image
    
    # # Update worker group specs
    # gpu_group = ray_job_yaml['spec']['rayClusterSpec']['workerGroupSpecs'][0]
    # gpu_group['template']['spec']['containers'][0]['image'] = run_image

    # cpu_group = ray_job_yaml['spec']['rayClusterSpec']['workerGroupSpecs'][1]
    # cpu_group['template']['spec']['containers'][0]['image'] = run_image

    # Create RayJob in Kubernetes
    try:
        api_response = api_instance.create_namespaced_custom_object(
            group="ray.io",
            version="v1",
            namespace=namespace,
            plural="rayjobs",
            body=ray_job_yaml
        )
        print(api_response)
        print("RayCluster created successfully!")
    except ApiException as e:
        print(f"Exception when creating RayCluster: {e}")

if __name__ == '__main__':
    submit_ray_job()
