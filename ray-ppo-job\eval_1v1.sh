source /opt/conda/bin/activate
conda activate ray

python ray_eval_jsbsim1v1_record.py \
    --checkpoint-path /home/<USER>/xiangshuai/projects/xs/ray-ppo-job/ray_results/jsbsim-1v1-bsl_20250626-184843/PPO_JSBSim-Combat_227d2_00000_0_2025-06-26_18-48-46/checkpoint_000095 \
    --env-name 1v1/ShootMissile/VsBaseline --experiment-name jsbsim-1v1-bsl --output-dir /home/<USER>/xiangshuai/projects/xs/ray-ppo-job/eval_results \
    --num-gpus 0 > eval_1v1.log 2>&1
