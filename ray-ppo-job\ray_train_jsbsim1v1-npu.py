import time
import os
import sys
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
import ray
print("Ray 路径:", ray.__file__)
print("Ray 版本:", ray.__version__)
from ray import tune
from ray.tune.registry import register_env
from ray.rllib.algorithms.ppo import PPOConfig
from ray.rllib.algorithms import Algorithm
from ray.rllib.callbacks.callbacks import RLlibCallback
from torch import Tensor
from envs.JSBSim.envs.multiplecombat_rllib_env import MultipleCombatEnv
from envs.JSBSim.envs.singlecombat_env import SingleCombatEnv
import argparse
import subprocess

# 华为NPU支持
try:
    import torch_npu
    NPU_AVAILABLE = True
    print("华为NPU支持已加载")
except ImportError:
    NPU_AVAILABLE = False
    print("华为NPU支持未找到，将使用CPU/GPU")

# NPU设备管理回调
class NPUDeviceCallback(RLlibCallback):
    """NPU设备管理回调，确保模型和数据正确移到NPU设备"""
    
    def __init__(self):
        super().__init__()
        self.device_type = None
        
    def on_algorithm_init(self, *, algorithm, **kwargs):
        """算法初始化时设置设备"""
        # 检查是否应该使用NPU
        if NPU_AVAILABLE and hasattr(algorithm.config, 'framework_str'):
            # 尝试获取设备类型从配置
            if hasattr(algorithm.config, '_device_type'):
                self.device_type = algorithm.config._device_type
            else:
                # 默认检查NPU可用性
                import torch
                if hasattr(torch, 'npu') and torch.npu.is_available():
                    self.device_type = 'npu'
                    print("NPU设备回调已激活")
    
    def on_train_result(self, *, algorithm, result, **kwargs):
        """训练结果回调，显示设备使用情况"""
        if self.device_type == 'npu':
            import torch
            if hasattr(torch, 'npu'):
                try:
                    memory_allocated = torch.npu.memory_allocated() / 1024**3  # GB
                    memory_reserved = torch.npu.memory_reserved() / 1024**3   # GB
                    result["custom_metrics"]["npu_memory_allocated_gb"] = memory_allocated
                    result["custom_metrics"]["npu_memory_reserved_gb"] = memory_reserved
                    print(f"NPU内存使用: 已分配 {memory_allocated:.2f}GB, 已保留 {memory_reserved:.2f}GB")
                except Exception as e:
                    print(f"获取NPU内存使用情况失败: {e}")

def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--checkpoint-path", type=str, default="",
                      help="检查点路径，如果不指定则使用随机动作")
    parser.add_argument("--num-env-runners", type=int, default=16,
                      help="环境运行器数量")
    parser.add_argument("--num-learners", type=int, default=1,
                      help="学习器数量")
    parser.add_argument("--num-gpus", type=int, default=0,
                      help="GPU数量")
    parser.add_argument("--num-npus", type=int, default=0,
                      help="华为NPU数量")
    parser.add_argument("--use-npu", action="store_true",
                      help="使用华为NPU进行训练")
    parser.add_argument("--local-dir", type=str, default="~/ray_results",
                      help="结果保存路径")
    parser.add_argument("--tensorboard-dir", type=str, default="~/tensorboard_logs",
                      help="TensorBoard日志路径")
    parser.add_argument("--env-name", type=str, default="1v1/ShootMissile/Selfplay",
                      help="环境名称，例如：1v1/DodgeMissile/vsBaseline, 1v1/NoWeapon/vsBaseline, 1v1/ShootMissile/Selfplay")
    parser.add_argument("--training-iterations", type=int, default=2000,
                      help="训练迭代次数")
    parser.add_argument("--env-registry-name", type=str, default="JSBSim-Combat",
                      help="Ray注册的环境名称")
    parser.add_argument("--experiment-name", type=str, default="jsbsim_1v1_combat_npu",
                      help="实验名称，用于保存结果和日志")

    return parser.parse_args()

def env_creator(env_config):
    # 1v1环境使用MultipleCombatEnv
    env = MultipleCombatEnv(env_config["env_name"])
    # env.seed(5)
    return env

def get_config(args):
    # 确定使用的设备类型和数量
    if args.use_npu and NPU_AVAILABLE:
        # 使用华为NPU
        device_type = "npu"
        num_devices = args.num_npus if args.num_npus > 0 else 1
        print(f"使用华为NPU进行训练，NPU数量: {num_devices}")
    else:
        # 使用GPU
        device_type = "gpu" 
        num_devices = args.num_gpus
        print(f"使用GPU进行训练，GPU数量: {num_devices}")
    
    config = (
        PPOConfig()
        .environment(args.env_registry_name, env_config={"env_name": args.env_name})
        .framework("torch")
        .env_runners(
            num_env_runners=args.num_env_runners,
            rollout_fragment_length='auto',
            num_envs_per_env_runner=1,
            num_cpus_per_env_runner=1,
            max_env_runner_restarts=0,  # 禁用env runner重启，提高稳定性
        )
        .learners(
            num_learners=args.num_learners,
            num_cpus_per_learner=1,
            num_gpus_per_learner=0,
            custom_resources_per_learner={"NPU": 1} if args.use_npu else {}
        )
        .training(
            gamma=0.99,
            lr=5e-5,
            train_batch_size=8192,  # 1v1场景减少batch size
            lambda_=0.95,
            clip_param=0.2,
            vf_clip_param=10.0,
            entropy_coeff=0.01,
            num_sgd_iter=10,
            minibatch_size=1024,  # 1v1场景减少minibatch size
            model={
                # 1v1场景网络配置相对简单一些
                "fcnet_hiddens": [256, 256, 128],
                "fcnet_activation": "relu",
                "vf_share_layers": False,  # 分离价值网络和策略网络
                "use_lstm": True,
                "lstm_cell_size": 128,  # 1v1场景减少LSTM大小
                "max_seq_len": 32,
            },
        )
        .multi_agent(
            policies=["red_policy", "blue_policy"],
            policy_mapping_fn=lambda agent_id, *args, **kwargs: (
                "red_policy" if agent_id.startswith("A") else "blue_policy"
            ),
            # policies_to_train=["red_policy"],  # 只训练红方策略
        )
        .evaluation(
            evaluation_interval=10,
            evaluation_duration=20,
            evaluation_num_workers=5,  # 1v1场景减少评估worker数量
            evaluation_config={
                "render_mode": "txt",
                "explore": False,
                "env_config": {
                    "env_name": args.env_name,
                },
            },
        )
    )
    
    # 华为NPU特定配置
    if args.use_npu and NPU_AVAILABLE:
        # 添加NPU回调
        config = config.callbacks(NPUDeviceCallback)
        
        # 添加设备类型标记到配置
        config._device_type = "npu"
        
        # 添加NPU特定的训练配置
        config = config.training(
            # NPU优化配置
            optimizer={
                "type": "Adam",
                "eps": 1e-7,  # NPU对数值稳定性要求更高
            }
        )
        
        # 设置NPU环境变量
        os.environ.setdefault("ASCEND_RT_VISIBLE_DEVICES", "0")
        # 设置PyTorch默认设备为NPU
        import torch
        if hasattr(torch, 'npu') and torch.npu.is_available():
            torch.npu.set_device(0)
            print(f"设置默认NPU设备: {torch.npu.current_device()}")
    else:
        config._device_type = "gpu" if args.num_gpus > 0 else "cpu"
        
    # 清空探索配置
    config.exploration_config = {}
    return config

if __name__ == "__main__":
    args = parse_args()
    
    timestamp = time.strftime("%Y%m%d-%H%M%S")
    experiment_name = f"{args.experiment_name}_{timestamp}"
    
    # 检查NPU可用性
    if args.use_npu and not NPU_AVAILABLE:
        print("警告：要求使用NPU但torch_npu未安装，将回退到GPU/CPU训练")
        args.use_npu = False
    
    # 启动tensorboard
    subprocess.Popen(['tensorboard --logdir ~/ray_results/' + experiment_name + ' --bind_all'], shell=True)
    
    # 注册环境
    register_env(args.env_registry_name, env_creator)  # 使用指定的环境注册名称
    
    # 初始化Ray
    ray.init()
    
    # NPU设备验证和设置
    if args.use_npu and NPU_AVAILABLE:
        import torch
        if hasattr(torch, 'npu') and torch.npu.is_available():
            print(f"NPU设备数量: {torch.npu.device_count()}")
            for i in range(torch.npu.device_count()):
                print(f"NPU {i}: {torch.npu.get_device_name(i)}")
            print("NPU设备验证成功")
        else:
            print("警告: NPU设备不可用")
            args.use_npu = False
    
    # 获取配置
    config = get_config(args)
    
    # 运行训练
    tune.run(
        "PPO",
        config=config,
        # resume="Local",
        # restore=args.checkpoint_path if args.checkpoint_path else None,
        stop={
            "training_iteration": args.training_iterations,
            "episode_reward_mean": 800,  # 1v1场景目标奖励调整
        },
        checkpoint_freq=50,
        checkpoint_at_end=True,
        # local_dir=os.path.expanduser(args.local_dir),
        name=experiment_name,
        verbose=2,
    ) 