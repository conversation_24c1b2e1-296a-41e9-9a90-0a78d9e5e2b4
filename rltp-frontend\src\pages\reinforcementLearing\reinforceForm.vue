<template>
  <div class="main-Form">
    <!-- 按钮区 -->
    <div class="Top">
      <TitleBtn ref="titleBtnRef"></TitleBtn>
      <q-btn class="doReturn" @click="returnToOverview">
        <img class="returnIcon" src="../../assets/images/icon_fh.png" alt="" />
        <div class="labelColor">返回模型概览</div>
      </q-btn>
    </div>

    <!-- 根据当前步骤显示对应表单 -->
    <component
      :is="currentStepComponent"
      @next-step="handleNextStep"
      @prev-step="handlePrevStep"
    ></component>
  </div>
</template>

<script setup>
import { defineAsyncComponent, ref, computed } from "vue";
import { useRouter } from "vue-router";
import TitleBtn from "./components/TitleBtn.vue";

// 异步加载拆分为独立chunk
const stepComponents = {
  1: defineAsyncComponent(() => import("./StepOne.vue")),
  2: defineAsyncComponent(() => import("./StepTwo.vue")),
  3: defineAsyncComponent(() => import("./StepThree.vue")),
  4: defineAsyncComponent(() => import("./StepFour.vue")),
  5: defineAsyncComponent(() => import("./StepFive.vue")),
};

const router = useRouter();

// TitleBtn组件引用
const titleBtnRef = ref(null);

// 当前步骤计算属性，与TitleBtn同步
const currentStep = computed(() => {
  return titleBtnRef.value?.currentStep || 1;
});

// 当前步骤对应的组件
const currentStepComponent = computed(() => stepComponents[currentStep.value]);

// 处理下一步
const handleNextStep = () => {
  if (titleBtnRef.value) {
    titleBtnRef.value.nextStep();
    console.log("当前步骤:", titleBtnRef.value.currentStep);

    // 根据当前步骤执行相应逻辑
    const currentStep = titleBtnRef.value.currentStep;
    if (currentStep > 1) {
      console.log("执行步骤", currentStep, "的相关逻辑");
    }
  }
};

// 处理上一步
const handlePrevStep = () => {
  if (titleBtnRef.value) {
    titleBtnRef.value.prevStep();
    console.log("当前步骤:", titleBtnRef.value.currentStep);
  }
};

// 直接跳转到指定步骤的方法
const goToStep = (step) => {
  if (titleBtnRef.value) {
    titleBtnRef.value.goToStep(step);
    console.log("跳转到步骤:", step);
  }
};

// 返回概览页面
function returnToOverview() {
  router.push("/ai-model/reinforcementStudy"); //跳转到强化学习入口页
}
</script>

<style lang="scss" scoped>
.main-Form {
  background-color: #131520;
  height: calc(100vh - 1rem); // 减去顶部padding和margin
  display: flex;
  flex-direction: column;
}

.Top {
  margin-top: 1rem;
  margin-bottom: 0.375rem;
  position: relative;
  .doReturn {
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
    .returnIcon {
      width: 0.375rem;
      height: 0.375rem;
      margin-right: 0.125rem;
    }
  }
}
</style>
