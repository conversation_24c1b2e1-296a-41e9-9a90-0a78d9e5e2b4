import numpy as np
import torch
import torch.optim as optim
import torch.nn.functional as F
import ray
from torch.utils.tensorboard import SummaryWriter
import os
from datetime import datetime
import zipfile


@ray.remote
class CartLearner:
    def __init__(self, model, replay_buffer, param_server, device, gamma=0.99, lr=1e-3, clip_epsilon=0.2,log_dir="logs",model_save_path="checkpoint",model_save_interval=50):
        self.device = device
        self.model = model.to(self.device)
        self.replay_buffer = replay_buffer
        self.param_server = param_server
        self.optimizer = optim.Adam(self.model.parameters(), lr=lr)
        self.gamma = gamma
        self.clip_epsilon = clip_epsilon
        self.log_dir = f"{log_dir}/learner"
        self.writer = SummaryWriter(log_dir=self.log_dir)
        self.model_save_path=model_save_path
        self.model_save_interval=model_save_interval
        self.global_step = 0

    def get_global_step(self):
        return self.global_step
    
    def train(self, batch_size, update_epochs):
        # print('------------start train-----------------')
        buffer_size = ray.get(self.replay_buffer.get_buffer_size.remote())
        # print(f'buffer_size: {buffer_size}')
        if buffer_size < batch_size:
            print(f"Not enough samples in buffer to train: {buffer_size}/{batch_size}")
            return
        else:
            # 动态设置 update_epochs 为 buffer_size / batch_size
            # update_epochs = max(1, buffer_size // batch_size)
            total_reward=0            
            for epoch in range(update_epochs):
                # 从重放缓冲区中采样数据
                sampled_data = ray.get(self.replay_buffer.sample.remote(batch_size))
                states_batch, actions_batch, rewards_batch, returns_batch, advantages_batch, dones_batch = zip(*sampled_data)
                
                # 将样本转换为张量并发送到指定设备
                states = torch.cat([torch.tensor(states, dtype=torch.float32) for states in states_batch]).to(self.device)
                actions = torch.cat([torch.tensor(actions, dtype=torch.int64) for actions in actions_batch]).to(self.device)
                rewards = torch.cat([torch.tensor(rewards, dtype=torch.float32) for rewards in rewards_batch]).to(self.device)
                returns = torch.cat([torch.tensor(returns, dtype=torch.float32) for returns in returns_batch]).to(self.device)
                advantages = torch.cat([torch.tensor(advantages, dtype=torch.float32) for advantages in advantages_batch]).to(self.device)
                dones = torch.cat([torch.tensor(dones, dtype=torch.float32) for dones in dones_batch]).to(self.device)

                # 累计总奖励
                total_reward += rewards.sum().item()
              
                # 获取 actions 对应的概率
                action_probs, state_values = self.model(states)
                action_probs_old = action_probs.gather(1, actions.unsqueeze(1)).squeeze(1)

                # 计算策略比率，注意使用 old action_probs
                ratio = action_probs.gather(1, actions.unsqueeze(1)).squeeze(1) / (action_probs_old + 1e-5)

                surrogate1 = ratio * advantages
                surrogate2 = torch.clamp(ratio, 1 - self.clip_epsilon, 1 + self.clip_epsilon) * advantages

                # 计算损失
                policy_loss = -torch.min(surrogate1, surrogate2).mean()
                value_loss = F.mse_loss(state_values.squeeze(), returns)
                loss = policy_loss + 0.5 * value_loss
                
                if epoch == update_epochs - 1:
                    print(f"Epoch: {epoch}, Loss: {loss.item()}, Reward: {total_reward}")

                # 记录损失
                self.writer.add_scalar("Loss/Policy Loss", policy_loss.item(), self.global_step)
                self.writer.add_scalar("Loss/Value Loss", value_loss.item(), self.global_step)
                self.writer.add_scalar("Loss/Total Loss", loss.item(), self.global_step)

                # 反向传播和优化
                self.optimizer.zero_grad()
                loss.backward()
                self.optimizer.step()

                # 增加全局步数
                self.global_step += 1
                if self.global_step % self.model_save_interval==0:
                    
                    self.save_model(self.model_save_path,self.global_step)
            
            # print(f"Reward: {total_reward}")
            # 更新参数服务器的模型权重
            share_state_dict = self.model.state_dict()
            for key, value in share_state_dict.items():
                share_state_dict[key] = value.cpu()
            ray.get(self.param_server.set_weights.remote(share_state_dict))
            ray.get(self.replay_buffer.clear.remote())

    def save_model(self, model_save_path, global_step):
        # 检查并创建保存目录（如果不存在）
        if not os.path.exists(model_save_path):
            os.makedirs(model_save_path)
        # 生成保存文件的路径，使用当前时间和步数来命名文件
        save_path = os.path.join(model_save_path, datetime.now().strftime('%Y%m%d%H%M%S') + f'_{global_step}.zip')

        # 创建一个临时文件用于保存模型权重
        temp_model_path = os.path.join(model_save_path, 'temp_model.pth')

        # 使用 PyTorch 的 `torch.save` 方法保存模型的状态字典
        torch.save(self.model.state_dict(), temp_model_path)

        # 将保存的模型文件压缩到 `.zip` 文件中
        with zipfile.ZipFile(save_path, 'w') as z:
            z.write(temp_model_path, arcname='model.pth')  # `arcname` 是压缩包内的文件名

        # 删除临时文件
        os.remove(temp_model_path)
            

    def close(self):
        self.writer.close()
