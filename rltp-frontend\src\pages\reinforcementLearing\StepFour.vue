<!--
 * @Author: Szc
 * @Date: 2025-08-06 20:24:42
 * @LastEditors: Szc
 * @LastEditTime: 2025-08-08 17:44:49
 * @Description: 
-->
<template>
  <div class="content">
    <div class="top">
      <div class="left">
        <!-- 模型性能对比 -->
        <div class="info-section">
          <div class="section-header">
            <img class="arrow-icon" src="../../assets/images/icon_dz.png" alt="" />
            <span class="section-title">不同训练迭代下的强化学习模型性能对比</span>
          </div>

          <div class="comparison-table">
            <!-- 有模型数据时显示模型列表 -->
            <div v-if="modelData.length > 0" class="model-items">
              <q-card
                v-for="(model, index) in modelData"
                :key="index"
                class="model-item"
                :class="{ 'model-active': selectedModelId === model.id }"
                @click="selectModel(model.id)"
              >
                <q-card-section class="model-card-content">
                  <div class="model-header">
                    <div class="model-name">{{ model.name }}</div>
                    <div class="model-size">{{ model.size }}</div>
                  </div>
                  <div class="checkpoint">{{ model.checkpoint }}</div>

                  <div class="model-metrics">
                    <div class="metric-row">
                      <span class="metric-label labelColor">最佳奖励:</span>
                      <q-input
                        v-model="model.accuracy"
                        class="metric-input"
                        dense
                        readonly
                      />
                    </div>
                    <div class="metric-row">
                      <span class="metric-label labelColor">平均奖励:</span>
                      <q-input
                        v-model="model.precision"
                        class="metric-input"
                        dense
                        readonly
                      />
                    </div>
                    <div class="metric-row">
                      <span class="metric-label labelColor">训练迭代:</span>
                      <q-input
                        v-model="model.recall"
                        class="metric-input"
                        dense
                        readonly
                      />
                    </div>
                    <div class="metric-row">
                      <span class="metric-label labelColor">算法类型:</span>
                      <q-input v-model="model.fps" class="metric-input" dense readonly />
                    </div>
                  </div>

                  <!-- 转换状态显示 -->
                  <div
                    v-if="getConversionStatusDisplay(model.id)"
                    class="conversion-status"
                    @click="showConversionDetails(model.id)"
                  >
                    <q-icon
                      name="mdi-sync"
                      :class="{
                        rotating: conversionTasks[model.id]?.status === 'processing',
                      }"
                    />
                    <span :style="{ color: getConversionStatusDisplay(model.id).color }">
                      {{ getConversionStatusDisplay(model.id).text }}
                    </span>
                    <q-icon name="mdi-information-outline" class="info-icon" />
                  </div>

                  <div class="model-actions">
                    <q-btn
                      v-if="selectedModelId === model.id"
                      class="action-btn export-btn"
                      size="sm"
                      flat
                      no-caps
                      @click="exportModel(model)"
                      :disable="!!conversionTasks[model.id]"
                    >
                      <img
                        src="../../assets/images/icon_dc.png"
                        alt="导出"
                        class="btn-icon"
                      />
                      <span>转为OM</span>
                    </q-btn>
                    <q-btn
                      v-if="selectedModelId === model.id"
                      class="action-btn compare-btn"
                      size="sm"
                      flat
                      no-caps
                      @click="addToCompare(model)"
                    >
                      <img
                        src="../../assets/images/icon_jrdb.png"
                        alt="对比"
                        class="btn-icon"
                      />
                      <span>加入对比</span>
                    </q-btn>
                  </div>
                </q-card-section>
              </q-card>
            </div>

            <!-- 无模型数据时显示空状态 -->
            <div v-else class="empty-state">
              <div class="empty-content">
                <div class="empty-title">暂无训练模型</div>
                <div class="empty-description">
                  {{ getTrainingTaskId() ? '当前训练任务暂未生成模型，请等待训练完成' : '请先完成训练任务，再查看模型概览' }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="right">
        <!-- 模型性能雷达图 -->
        <div class="radar-section">
          <div class="radar-header">
            <div style="display: flex; align-items: center">
              <img
                class="arrow-icon"
                src="../../assets/images/icon_dz.png"
                alt=""
                style="width: 0.2rem; height: 0.2rem; margin-right: 0.125rem"
              />
              <span class="radar-title">强化学习模型性能对比</span>
            </div>
            <div class="radar-desc">点击卡片上的"加入对比"按钮将强化学习模型添加到该图中</div>
          </div>

          <div class="radar-container">
            <div ref="radarChartRef" class="radar-chart"></div>
          </div>

          <!-- 下一步按钮 -->
          <div class="bottom">
            <div class="next">
              <q-btn
                class="prevBtn roundBox"
                color="grey-7"
                label="上一步"
                @click="prevStep"
              />
              <q-btn
                class="nextBtn roundBox"
                color="primary"
                label="下一步"
                @click="nextStep"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 转换详情对话框 -->
  <q-dialog v-model="showConversionDialog" persistent>
    <q-card class="conversion-dialog">
      <q-card-section class="dialog-header">
        <div class="dialog-title">模型转换详情</div>
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-card-section class="dialog-content" v-if="selectedConversionTask">
        <div class="detail-row">
          <div class="detail-label">模型名称:</div>
          <div class="detail-value">{{ selectedConversionTask.modelName || "未知" }}</div>
        </div>
        <div class="detail-row">
          <div class="detail-label">转换状态:</div>
          <div
            class="detail-value"
            :style="{ color: getStatusColor(selectedConversionTask.status) }"
          >
            {{
              conversionStatusMap[selectedConversionTask.status] ||
              selectedConversionTask.status
            }}
          </div>
        </div>
        <div class="detail-row">
          <div class="detail-label">开始时间:</div>
          <div class="detail-value">{{ selectedConversionTask.startTime || "未知" }}</div>
        </div>
        <div class="detail-row" v-if="selectedConversionTask.endTime">
          <div class="detail-label">完成时间:</div>
          <div class="detail-value">{{ selectedConversionTask.endTime }}</div>
        </div>
        <div
          class="detail-row"
          v-if="
            selectedConversionTask.outputPath &&
            selectedConversionTask.status === 'completed'
          "
        >
          <div class="detail-label">输出路径:</div>
          <div class="detail-value">{{ selectedConversionTask.outputPath }}</div>
        </div>
        <div
          class="detail-row"
          v-if="
            selectedConversionTask.fileSize &&
            selectedConversionTask.status === 'completed'
          "
        >
          <div class="detail-label">文件大小:</div>
          <div class="detail-value">{{ selectedConversionTask.fileSize }}MB</div>
        </div>
        <div class="detail-row error-message" v-if="selectedConversionTask.errorMessage">
          <div class="detail-label">错误信息:</div>
          <div class="detail-value">{{ selectedConversionTask.errorMessage }}</div>
        </div>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn flat label="关闭" color="primary" v-close-popup />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, defineEmits, nextTick } from "vue";
import * as echarts from "echarts";
import { api } from "boot/axios";
import { usePlugin } from "composables/plugin.js";

// 定义事件
const emit = defineEmits(["next-step", "prev-step"]);

// 引入通知插件
const { notify } = usePlugin();

// 强化学习训练API
const rlTrainingApi = {
  async getModelList(trainingId) {
    const response = await api.get(`/backend/training/rl/${trainingId}/models`)
    return response.data
  },
  async getModelInfo(trainingId) {
    const response = await api.get(`/backend/training/rl/${trainingId}/model/info`)
    return response.data
  },
  async downloadModel(trainingId, localPath) {
    const response = await api.post(`/backend/training/rl/${trainingId}/model/download`, {
      localPath
    })
    return response.data
  }
};

// 模型数据
const modelData = ref([]);

// 获取强化学习训练任务ID
function getTrainingTaskId() {
    try {
        // 优先查找强化学习特有的键名，然后查找通用键名
        const taskId = localStorage.getItem('currentRLTrainingTaskId') ||
                      sessionStorage.getItem('currentRLTrainingTaskId') ||
                      localStorage.getItem('currentTrainingTaskId') ||
                      sessionStorage.getItem('currentTrainingTaskId')

        if (taskId) {
            console.log('从存储中获取到强化学习训练任务ID:', taskId)
            return taskId
        }
    } catch (error) {
        console.warn('获取强化学习训练任务ID失败:', error)
    }

    return null
}

// 当前训练ID（从路由或父组件获取）
const currentTrainingId = ref(getTrainingTaskId() || ''); // 从存储中获取训练任务ID

// 选中的模型ID
const selectedModelId = ref("1");

// 对比的模型列表
const comparedModels = ref([]);

// 转换任务状态
const conversionTasks = ref({});
// 转换任务轮询定时器
const conversionPollingIntervals = ref({});
// 转换状态映射
const conversionStatusMap = {
  started: "已开始",
  processing: "转换中",
  completed: "已完成",
  failed: "失败",
  error: "错误",
  not_found: "未找到",
};

// 转换详情对话框
const showConversionDialog = ref(false);
const selectedConversionTask = ref(null);

// 显示转换详情
function showConversionDetails(modelId) {
  const task = conversionTasks.value[modelId];
  if (task) {
    selectedConversionTask.value = task;
    showConversionDialog.value = true;
  }
}

// 获取状态颜色
function getStatusColor(status) {
  const statusColorMap = {
    started: "#2196F3", // 蓝色
    processing: "#FF9800", // 橙色
    completed: "#4CAF50", // 绿色
    failed: "#F44336", // 红色
    error: "#F44336", // 红色
    not_found: "#9E9E9E", // 灰色
  };
  return statusColorMap[status] || "#9E9E9E";
}

// 雷达图相关
const radarChartRef = ref(null);
let radarChartInstance = null;

// 初始化雷达图
function initRadarChart() {
  if (radarChartRef.value) {
    try {
      // 如果已存在图表实例，先销毁
      if (radarChartInstance) {
        radarChartInstance.dispose();
      }

      radarChartInstance = echarts.init(radarChartRef.value);

      const radarOption = {
        title: {
          text: "",
        },
        tooltip: {
          axisPointer: {
            type: "shadow",
          },
          textStyle: {
            fontSize: window.screen.width > 1536 ? "20" : "14",
            color: "#333",
          },
        },
        legend: {
          show: false,
          data: [],
          textStyle: {
            color: "#fff",
            fontSize: 14,
          },
          top: "10",
          left: "10",
          orient: "vertical",
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "15%",
          top: "15%",
          containLabel: true,
        },
        radar: {
          shape: "polygon",
          name: {
            textStyle: {
              color: "#fff",
              padding: [3, 5],
            },
          },
          splitNumber: 7, // 默认是5，减小这个值会让每个环形区域变宽
          indicator: [
            { name: "最佳奖励", max: 1000 },
            { name: "平均奖励", max: 1000 },
            { name: "训练迭代", max: 2000 },
            { name: "收敛速度", max: 100 },
            { name: "模型大小(M)", max: 100 },
          ],
          axisLine: {
            lineStyle: {
              color: "#fff",
            },
          },
          splitLine: {
            lineStyle: {
              color: "#fff",
            },
          },
          splitArea: {
            show: true,
            areaStyle: {
              color: ["#f2f3f5", "#121724"],
            },
          },
        },
        textStyle: { fontSize: window.screen.width > 1536 ? "22" : "14" },
        series: [],
      };

      radarChartInstance.setOption(radarOption);
    } catch (error) {
      console.error("初始化雷达图失败:", error);
      // 延迟重试
      setTimeout(() => {
        initRadarChart();
      }, 100);
    }
  }
}

// 窗口大小改变时重置图表大小
function resizeChart() {
  radarChartInstance?.resize();
}

// 选择模型
function selectModel(modelId) {
  selectedModelId.value = modelId;
}

// 模型转换为OM
async function exportModel(model) {
  try {
    console.log("开始转换模型为OM:", model.name);

    const requestData = {
      model_id: parseInt(model.id),
      conversion_format: "om",
      chip_name: "910B3",
    };

    console.log("转换请求参数:", requestData);

    const response = await api.post(`backend/training/models/convert`, requestData);
    console.log("转换API响应:", response);

    if (response && response.success) {
      notify("模型转换任务已启动", "positive");
      console.log("转换任务信息:", response.data);

      // 如果有转换日志ID，开始轮询转换状态
      if (response.data && response.data.conversion_log_id) {
        const conversionLogId = response.data.conversion_log_id;
        console.log("转换日志ID:", conversionLogId);

        // 初始化转换任务状态
        conversionTasks.value[model.id] = {
          conversionLogId: conversionLogId,
          status: "processing",
          message: "转换中...",
          startTime: new Date().toLocaleString(),
          endTime: null,
          outputPath: null,
          fileSize: null,
          errorMessage: null,
        };

        // 开始轮询转换状态
        startConversionStatusPolling(model.id, conversionLogId);
      }
    } else {
      const message = response?.message || "模型转换启动失败";
      notify(message, "warning");
    }
  } catch (error) {
    console.error("模型转换失败:", error);
    const errorMessage =
      error.response?.data?.message || error.message || "模型转换请求失败";
    notify("转换失败: " + errorMessage, "negative");
  }
}

// 开始轮询转换状态
function startConversionStatusPolling(modelId, conversionLogId) {
  // 清除可能存在的旧定时器
  if (conversionPollingIntervals.value[modelId]) {
    clearInterval(conversionPollingIntervals.value[modelId]);
  }

  // 每3秒查询一次转换状态
  conversionPollingIntervals.value[modelId] = setInterval(async () => {
    try {
      const status = await fetchConversionStatus(conversionLogId);

      // 如果状态是已完成或失败，停止轮询
      if (["completed", "failed", "error"].includes(status.status)) {
        clearInterval(conversionPollingIntervals.value[modelId]);
        conversionPollingIntervals.value[modelId] = null;

        // 根据状态显示不同通知
        if (status.status === "completed") {
          notify(`模型 ${status.modelName || "未知"} 转换为OM格式已完成`, "positive");
        } else {
          notify(
            `模型 ${status.modelName || "未知"} 转换失败: ${
              status.errorMessage || "未知错误"
            }`,
            "negative"
          );
        }
      }
    } catch (error) {
      console.error("获取转换状态失败:", error);

      // 错误次数过多时停止轮询
      const task = conversionTasks.value[modelId];
      if (task) {
        task.errorCount = (task.errorCount || 0) + 1;
        if (task.errorCount > 5) {
          clearInterval(conversionPollingIntervals.value[modelId]);
          conversionPollingIntervals.value[modelId] = null;
          task.status = "error";
          task.message = "获取状态失败";
          task.errorMessage = "连接服务器失败，请稍后再试";
          notify("无法获取模型转换状态，请检查网络连接", "negative");
        }
      }
    }
  }, 3000);
}

// 获取转换状态
async function fetchConversionStatus(conversionLogId) {
  try {
    console.log("获取转换状态, ID:", conversionLogId);
    const response = await api.get(
      `backend/training/models/convert/${conversionLogId}/status`
    );
    console.log("转换状态响应:", response);

    if (response && response.success) {
      const statusData = response.data;
      const modelId = statusData.model_id.toString();

      // 更新转换任务状态
      if (conversionTasks.value[modelId]) {
        const task = conversionTasks.value[modelId];
        task.status = statusData.status;
        task.message = conversionStatusMap[statusData.status] || statusData.status;
        task.modelName = statusData.model_name;
        task.startTime = statusData.start_time;
        task.endTime = statusData.end_time;
        task.outputPath = statusData.output_path;
        task.fileSize = statusData.file_size_mb;
        task.errorMessage = statusData.error_message;
        task.errorCount = 0; // 重置错误计数
      }

      return {
        status: statusData.status,
        modelName: statusData.model_name,
        errorMessage: statusData.error_message,
      };
    } else {
      throw new Error(response?.message || "获取转换状态失败");
    }
  } catch (error) {
    console.error("获取转换状态API调用失败:", error);
    throw error;
  }
}

// 模型推理
async function runInference(model, inputSource) {
  try {
    console.log("开始模型推理:", model.name);

    const requestData = {
      model_id: parseInt(model.id),
      input_source: inputSource || "/path/to/image.jpg",
      confidence_threshold: 0.5,
    };

    console.log("推理请求参数:", requestData);

    const response = await api.post(`backend/training/models/inference`, requestData);
    console.log("推理API响应:", response);

    if (response && response.success) {
      notify("模型推理成功", "positive");
      console.log("推理结果:", response.data);
      return response.data;
    } else {
      const message = response?.message || "模型推理失败";
      notify(message, "warning");
      return null;
    }
  } catch (error) {
    console.error("模型推理失败:", error);
    const errorMessage =
      error.response?.data?.message || error.message || "模型推理请求失败";
    notify("推理失败: " + errorMessage, "negative");
    return null;
  }
}

// 加入对比
function addToCompare(model) {
  console.log("加入对比:", model.name);

  // 检查是否已经在对比列表中
  const existingIndex = comparedModels.value.findIndex((m) => m.id === model.id);
  if (existingIndex === -1) {
    // 如果不存在，添加到对比列表
    comparedModels.value.push(model);
  }

  // 更新雷达图
  updateRadarChart();
}

// 更新雷达图数据
function updateRadarChart() {
  if (!radarChartInstance) return;

  // 计算动态最大值 - 适配强化学习数据
  const allValues = comparedModels.value.map((model) => [
    parseFloat(model.accuracy.replace("最佳奖励: ", "")),
    parseFloat(model.precision.replace("平均奖励: ", "")),
    parseFloat(model.recall.replace("迭代次数: ", "")),
    // 收敛速度：基于迭代次数计算，迭代次数越少收敛越快
    Math.max(0, 100 - parseFloat(model.recall.replace("迭代次数: ", "")) / 20),
    parseFloat(model.size.replace("MB", "")),
  ]);

  const maxValues = [100, 100, 100, 100, 100]; // 默认最大值，保持一致性
  if (allValues.length > 0) {
    for (let i = 0; i < 5; i++) {
      const max = Math.max(...allValues.map((values) => values[i] || 0));
      // 所有指标统一使用100作为基础最大值，动态调整时留20%余量
      maxValues[i] = Math.max(100, max * 1.2);
    }
  }

  const seriesData = comparedModels.value.map((model, index) => {
    const colors = [
      "rgb(25, 183, 182)",
      "rgb(76, 175, 80)",
      "rgb(255, 87, 34)",
      "rgb(156, 39, 176)",
    ];
    const color = colors[index % colors.length];

    return {
      name: model.name,
      type: "radar",
      areaStyle: {
        normal: {
          opacity: 0.5,
          color: color.replace("rgb", "rgba").replace(")", ", 0.4)"),
        },
      },
      lineStyle: {
        color: color,
      },
      data: [
        {
          value: [
            parseFloat(model.accuracy.replace("最佳奖励: ", "")),
            parseFloat(model.precision.replace("平均奖励: ", "")),
            parseFloat(model.recall.replace("迭代次数: ", "")),
            // 收敛速度：基于迭代次数计算
            Math.max(0, 100 - parseFloat(model.recall.replace("迭代次数: ", "")) / 20),
            parseFloat(model.size.replace("MB", "")),
          ],
          name: model.name,
        },
      ],
    };
  });

  const legendData = comparedModels.value.map((model) => model.name);

  const radarOption = {
    title: {
      text: "",
    },
    tooltip: {
      axisPointer: {
        type: "shadow",
      },
      textStyle: {
        fontSize: window.screen.width > 1536 ? "20" : "14",
        color: "#333",
      },
    },
    legend: {
      show: comparedModels.value.length > 0,
      data: legendData,
      textStyle: {
        color: "#fff",
        fontSize: 14,
      },
      top: "10",
      left: "10",
      orient: "vertical",
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "15%",
      top: comparedModels.value.length > 0 ? "20%" : "15%",
      containLabel: true,
    },
    radar: {
      shape: "polygon",
      name: {
        textStyle: {
          color: "#fff",
          padding: [3, 5],
        },
      },
      indicator: [
        { name: "最佳奖励", max: maxValues[0] },
        { name: "平均奖励", max: maxValues[1] },
        { name: "训练迭代", max: maxValues[2] },
        { name: "收敛速度", max: maxValues[3] },
        { name: "模型大小(M)", max: maxValues[4] },
      ],
      axisLine: {
        lineStyle: {
          color: "#fff",
        },
      },
      splitLine: {
        lineStyle: {
          color: "#fff",
        },
      },
      splitArea: {
        show: true,
        areaStyle: {
          color: ["#f2f3f5", "#121724"],
        },
      },
    },
    textStyle: { fontSize: window.screen.width > 1536 ? "22" : "14" },
    series: seriesData,
  };

  radarChartInstance.setOption(radarOption);
}

// 下一步按钮点击事件
function nextStep() {
  emit("next-step");
}

// 获取模型数据
async function fetchModels() {
    try {
        console.log('🔍 StepFour: 开始获取模型数据')

        // 获取训练任务ID
        const taskId = getTrainingTaskId()
        console.log('🔍 StepFour: 获取到的训练任务ID:', taskId)

        if (!taskId) {
            console.log('❌ StepFour: 未找到训练任务ID，不显示任何模型')
            modelData.value = []
            selectedModelId.value = null
            return
        }

        console.log('📡 StepFour: 准备调用API获取模型数据，任务ID:', taskId)
        const apiUrl = `backend/training/rl/${taskId}/models`
        console.log('📡 StepFour: API URL:', apiUrl)

        // 根据任务ID查询模型列表
        const response = await api.get(apiUrl)
        console.log('📡 StepFour: API响应完整数据:', response)
        console.log('📡 StepFour: API响应状态:', response?.status)
        console.log('📡 StepFour: API响应数据:', response?.data)

        let apiData = null
        if (response?.data && Array.isArray(response.data)) {
            apiData = response.data
        }

        if (apiData && apiData.length > 0) {
            // 处理API返回的模型数据
            modelData.value = apiData.map(model => ({
                id: model.id || `model_${Math.random()}`,
                name: model.model_name || '未知模型',
                checkpoint: `Checkpoint: 迭代 ${model.episode || 'unknown'}`,
                size: `${model.model_size_mb || 0}MB`,
                accuracy: `最佳奖励: ${model.best_reward || 0}`,
                precision: `平均奖励: ${model.avg_reward || 0}`,
                recall: `迭代次数: ${model.episode || 0}`,
                fps: `算法: ${model.algorithm || '未知'}`
            }))

            // 如果有模型数据，默认选中第一个
            if (modelData.value.length > 0) {
                selectedModelId.value = modelData.value[0].id
            }

            console.log(`成功获取到 ${modelData.value.length} 个模型`)
        } else {
            console.log(`任务ID ${taskId} 暂无训练完成的模型`)
            modelData.value = []
            selectedModelId.value = null
        }
    } catch (error) {
        console.error('❌ StepFour: 获取模型数据失败:', error)
        console.error('❌ StepFour: 错误详情:', {
            message: error.message,
            response: error.response,
            request: error.request,
            config: error.config
        })

        // 获取训练任务ID用于错误提示
        const taskId = getTrainingTaskId()

        if (!taskId) {
            console.log('❌ StepFour: 无训练任务ID，不显示模型')
            modelData.value = []
            selectedModelId.value = null
        } else {
            console.log('❌ StepFour: API调用失败，不显示模型数据')
            modelData.value = []
            selectedModelId.value = null

            // 显示错误通知
            notify('获取模型数据失败: ' + (error.message || '未知错误'), 'negative')
        }
    }
}

// 上一步按钮点击事件
function prevStep() {
  emit("prev-step");
}

// 获取模型转换状态显示
function getConversionStatusDisplay(modelId) {
  const task = conversionTasks.value[modelId];
  if (!task) return null;

  const statusColorMap = {
    started: "#2196F3", // 蓝色
    processing: "#FF9800", // 橙色
    completed: "#4CAF50", // 绿色
    failed: "#F44336", // 红色
    error: "#F44336", // 红色
    not_found: "#9E9E9E", // 灰色
  };

  return {
    text: task.message,
    color: statusColorMap[task.status] || "#9E9E9E",
  };
}

// 生命周期钩子
onMounted(async () => {
  // 获取模型数据
  await fetchModels();

  // 使用nextTick确保DOM完全渲染后再初始化图表
  nextTick(() => {
    initRadarChart();
  });

  // 监听窗口大小变化
  window.addEventListener("resize", resizeChart);
});

onBeforeUnmount(() => {
  // 销毁图表实例
  if (radarChartInstance) {
    radarChartInstance.dispose();
    radarChartInstance = null;
  }

  // 移除事件监听
  window.removeEventListener("resize", resizeChart);

  // 清除所有转换状态轮询定时器
  Object.values(conversionPollingIntervals.value).forEach((interval) => {
    if (interval) clearInterval(interval);
  });
});
</script>

<style lang="scss" scoped>
.content {
  display: flex;
  flex-direction: column;
  flex: 1;

  .top {
    display: flex;
    margin-bottom: 0.125rem;
    flex: 1;

    min-height: 9.5rem;

    .left {
      width: 40%;
      height: inherit;
      border: 0.025rem solid #707070;
      background-color: #181a24;
      padding: 0.3375rem;
      display: flex;
      flex-direction: column;
      margin-right: 0.125rem;
      position: relative;
      background-color: #181a24;
      background-image: repeating-linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.05) 0,
        rgba(255, 255, 255, 0.01) 0.05rem,
        transparent 0.0125rem,
        transparent 0.1875rem
      );
      &::before {
        position: absolute;
        content: "";
        left: -0.1rem;
        top: 0;
        width: 0.025rem;
        height: 100%;
        background: rgba(156, 172, 198);
        border-radius: 0.125rem;
      }

      &::after {
        position: absolute;
        content: "";
        left: -0.075rem;
        top: 0;
        width: 0.025rem;
        height: 100%;
        background: rgba(156, 172, 198, 0.5);
      }
    }

    .right {
      width: 60%;
      height: inherit;
      border: 0.025rem solid #707070;
      background-color: #181a24;
      padding: 0.3375rem;
      display: flex;
      flex-direction: column;
      position: relative;
      background-color: #181a24;
      background-image: repeating-linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.05) 0,
        rgba(255, 255, 255, 0.01) 0.05rem,
        transparent 0.0125rem,
        transparent 0.1875rem
      );
      &::before {
        position: absolute;
        content: "";
        left: -0.1rem;
        top: 0;
        width: 0.025rem;
        height: 100%;
        background: rgba(156, 172, 198);
        border-radius: 0.125rem;
      }

      &::after {
        position: absolute;
        content: "";
        left: -0.075rem;
        top: 0;
        width: 0.025rem;
        height: 100%;
        background: rgba(156, 172, 198, 0.5);
      }
    }
  }
}

// 信息区域
.info-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.25rem;

    .arrow-icon {
      width: 0.2rem;
      height: 0.2rem;
      margin-right: 0.125rem;
    }

    .section-title {
      color: #4ab4ff;
      font-size: 0.2rem;
    }
  }

  .comparison-table {
    flex: 1;
    display: flex;
    flex-direction: column;

    .table-header {
      background: linear-gradient(to right, #216f6e 30%, #003366 70%);
      margin-bottom: 0.125rem;

      .header-cell {
        padding: 0.125rem 0.25rem;
        color: white;
        font-size: 0.175rem;
        font-weight: bold;
        text-align: center;
      }
    }

    .model-items {
      // flex: 1;
      // overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 0.125rem;
      // height: 80%;
    }

    .model-item {
      background-color: #181a24;
      border: 0.025rem solid #999999;
      border-radius: 0.0625rem;
      cursor: pointer;
      transition: border-color 0.3s ease;

      &.model-active {
        border-color: white !important;
      }

      .model-card-content {
        padding: 0.1875rem !important;
        display: flex;
        flex-direction: column;
        height: 100%;
      }

      .model-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.125rem;

        .model-name {
          color: white;
          font-size: 0.175rem;
          font-weight: bold;
        }

        .model-size {
          color: #4ab4ff;
          font-size: 0.175rem;
          font-weight: bold;
          width: 1.125rem;
          height: 0.5rem;
          line-height: 0.5rem;
          text-align: center;
          background: #164c82;
          border-radius: 0.25rem;
        }
      }

      .checkpoint {
        color: #999;
        font-size: 0.15rem;
        margin-bottom: 0.125rem;
      }

      .model-metrics {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0.0625rem;
        margin-bottom: 0.1875rem;
        flex: 1;

        .metric-row {
          display: flex;
          align-items: center;
          gap: 0.0625rem;

          :deep(.q-field__inner) {
            border-radius: 0 !important;
          }

          .metric-label {
            color: #999;
            font-size: 0.15rem;
            min-width: 0.875rem;
            text-align: right;
          }

          .metric-input {
            // flex: 1;
            width: 1.875rem;

            :deep(.q-field__control-container) {
              padding-top: 0 !important;
            }

            :deep(.q-field__control) {
              height: 0.375rem !important;
              min-height: 0.375rem !important;
            }

            :deep(.q-field__native) {
              color: white !important;
              font-size: 0.15rem !important;
              text-align: center;
            }
          }
        }
      }

      .conversion-status {
        display: flex;
        align-items: center;
        gap: 0.0625rem;
        margin-top: 0.125rem;
        margin-bottom: 0.1875rem;
        cursor: pointer;

        .q-icon {
          font-size: 0.15rem;
          color: #999;
        }

        .q-icon.rotating {
          animation: spin 1.5s linear infinite;
        }

        .info-icon {
          margin-left: auto;
          font-size: 0.15rem;
          color: #4ab4ff;
        }

        &:hover {
          opacity: 0.8;
        }
      }

      .model-actions {
        display: flex;
        justify-content: space-between;
        gap: 0.125rem;
        margin-top: auto;

        .action-btn {
          height: 0.4rem !important;
          font-size: 0.15rem !important;
          padding: 0 0.125rem !important;
          min-height: auto !important;

          .btn-icon {
            width: 0.15rem;
            height: 0.15rem;
            margin-right: 0.0625rem;
          }

          &.export-btn {
            background-color: #0b3e50 !important;
            color: #4ab4ff !important;
            border: 0.0125rem solid #4ab4ff;

            &:hover {
              background-color: rgba(74, 180, 255, 0.1) !important;
            }
          }

          &.compare-btn {
            background-color: #2a2d3a !important;
            color: #fff !important;
            border: 0.0125rem solid #707070;

            &:hover {
              background-color: rgba(255, 255, 255, 0.1) !important;
            }
          }

          &:before {
            display: none !important;
          }
        }
      }
    }
  }
}

// 雷达图区域
.radar-section {
  height: 100%;
  display: flex;
  flex-direction: column;

  .radar-header {
    margin-bottom: 0.25rem;

    .radar-title {
      color: #4ab4ff;
      font-size: 0.2rem;
      display: block;
    }

    .radar-desc {
      margin-top: 0.25rem;
      color: #cea345;
      font-size: 0.15rem;
    }
  }

  .radar-container {
    flex: 1;
    // border: .025rem solid #333;
    border-radius: 0.0625rem;
    padding: 0.125rem;
    margin-bottom: 0.1875rem;

    .radar-chart {
      width: 100%;
      height: 100%;
      min-height: 6rem;
    }
  }

  .legend-container {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 0.25rem;

    .legend-item {
      display: flex;
      align-items: center;
      color: white;
      font-size: 0.15rem;

      .legend-color {
        width: 0.15rem;
        height: 0.15rem;
        border-radius: 50%;
        margin-right: 0.0625rem;

        &.yolov5m {
          background-color: #2196f3;
        }

        &.yolov5s {
          background-color: #4caf50;
        }
      }
    }
  }

  // 底部按钮
  .bottom {
    display: flex;
    align-items: center;
    flex-shrink: 0;

    .next {
      display: flex;
      justify-content: space-between;
      width: 100%;
      padding: 0 0.25rem;
      gap: 0.25rem;

      .prevBtn {
        margin-right: auto;
      }

      .nextBtn {
        margin-left: auto;
      }
    }
  }
}

// 通用样式
.labelColor {
  color: #4ab4ff;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 转换详情对话框样式
.conversion-dialog {
  width: 500px;
  max-width: 90vw;
  background-color: #181a24;
  border: 0.025rem solid #707070;

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem;
    background: linear-gradient(to right, #216f6e 30%, #003366 70%);

    .dialog-title {
      color: white;
      font-size: 0.2rem;
      font-weight: bold;
    }
  }

  .dialog-content {
    padding: 0.25rem;

    .detail-row {
      display: flex;
      margin-bottom: 0.125rem;
      padding: 0.125rem 0;
      border-bottom: 0.0125rem solid #333;

      &:last-child {
        border-bottom: none;
      }

      .detail-label {
        width: 30%;
        color: #4ab4ff;
        font-size: 0.175rem;
      }

      .detail-value {
        flex: 1;
        color: white;
        font-size: 0.175rem;
      }

      &.error-message {
        .detail-value {
          color: #f44336;
        }
      }
    }
  }

  // 空状态样式
  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: auto;
    min-height: 3rem;

    .empty-content {
      text-align: center;
      color: #fff;

      .empty-title {
        font-size: 0.225rem;
        font-weight: 500;
        color: #fff;
        margin-bottom: 0.125rem;
      }

      .empty-description {
        font-size: 0.175rem;
        color: #ccc;
        line-height: 1.5;
        max-width: 20rem;
      }
    }
  }
}
</style>
