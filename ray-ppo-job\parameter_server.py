# import gym
import gymnasium as gym
import ray
import torch
import io
from model_cnn import PPOActorCriticCNN

@ray.remote
class CartParameterServer:
    def __init__(self, env_name):
        self.env = gym.make(env_name)
        self.model = PPOActorCriticCNN(action_dim=self.env.action_space.n)

    def get_weights(self):
        # 将模型权重保存到一个内存缓冲区
        buffer = io.BytesIO()
        torch.save(self.model.state_dict(), buffer)
        buffer.seek(0)
        # 确保将权重加载到 CPU
        weights = torch.load(buffer, map_location=torch.device('cpu'))
        # for name, param in weights.items():
        #     print(f"Weight {name} device: {param.device}")
        return weights

    def set_weights(self, weights):
        # 确保模型加载权重时不会有设备问题
        self.model.load_state_dict(weights)