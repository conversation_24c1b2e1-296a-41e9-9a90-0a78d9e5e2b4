# import gym
import gymnasium as gym
import torch
import argparse
import numpy as np
import zipfile
import os
from model import PPOActorCritic
from gym.wrappers import RecordVideo

def render_cartpole(model_path, save_path, env_name='CartPole-v1', hidden_dim=64, max_steps=500):
    # 创建保存目录（如果不存在）
    # if not os.path.exists(save_path):
    #     os.makedirs(save_path)

    # 创建CartPole环境
    env = gym.make(env_name)
    # 包装环境以录制视频
    env = RecordVideo(env, video_folder=save_path, episode_trigger=lambda x: x==2)

    # 确定状态空间和动作空间的维度
    state_dim = env.observation_space.shape[0]
    action_dim = env.action_space.n

    # 解压缩并加载模型
    with zipfile.ZipFile(model_path, 'r') as zip_ref:
        zip_ref.extract('model.pth', '.')  # 将模型文件解压到当前目录

    # 加载解压后的模型文件
    model = PPOActorCritic(state_dim=state_dim, action_dim=action_dim, hidden_dim=hidden_dim)
    model.load_state_dict(torch.load('model.pth'))
    model.eval()

       # 开始渲染并保存结果
    state = env.reset()

    # 确保状态是一个非空的 NumPy 数组
    if isinstance(state, tuple):
        state = state[0]  # 提取状态部分，如果是元组
    if state is None or len(state) == 0:
        raise ValueError("Reset failed, state is None or empty.")

    for step in range(max_steps):
        # 将状态转换为张量并通过模型获取动作
        state_tensor = torch.tensor(np.array(state), dtype=torch.float32).unsqueeze(0)  # 确保 state 是 numpy 数组
        with torch.no_grad():
            action_probs, _ = model(state_tensor)
        action = torch.argmax(action_probs, dim=1).item()

        # 执行动作
        state, reward, done, _,_ = env.step(action)

        # 检查是否完成
        if done:
            break

    # 关闭环境
    env.close()
    print(f"Render saved to {save_path}")

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--model_path', type=str, help='Path to the model .zip file',default='/home/<USER>/yan/ray-ppo-gym/20240830183234_3000.zip')
    parser.add_argument('--save_path', type=str,  default='renders')
    parser.add_argument('--env_name', type=str, default='CartPole-v1', help='Name of the gym environment')
    parser.add_argument('--hidden_dim', type=int, default=64, help='Hidden dimension size for the model')
    parser.add_argument('--max_steps', type=int, default=500, help='Maximum number of steps to render')
    args = parser.parse_args()

    render_cartpole(
        model_path=args.model_path,
        save_path=args.save_path,
        env_name=args.env_name,
        hidden_dim=args.hidden_dim,
        max_steps=args.max_steps
    )
