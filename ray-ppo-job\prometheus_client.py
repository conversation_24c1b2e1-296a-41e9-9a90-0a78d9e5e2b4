import time
import requests

prom_url = 'http://172.16.16.126:53169/api/v1/query'

def get_cluster_cpu_index(q_time=None):
    """获取集群每个节点的可用 CPU 数量"""
    query_promql_map = dict(
        # CPU 总数
        cpu_total_num='sum by (node) (node:node_num_cpu:sum)',
        # CPU 已使用数量
        cpu_used_num='sum by (node) ( kube_pod_container_resource_requests{resource="cpu"} * on (pod, namespace) group_left(node) kube_pod_info)'
)
    
    return get_promql_params(q_time, query_promql_map)

def get_promql_params(q_time, query_maps):
    """格式化参数 发起请求"""
    if not q_time:
        q_time = time.time()

    results = {}
    for key, query in query_maps.items():
        params = {
            'query': query,
            'time': q_time
        }
        result = requests.get(prom_url, params)
        if result:
            result_json = result.json()
            # print(result_json)
            if result_json['status'] == 'success':
                data_results = result_json['data']['result']
                for data in data_results:
                    if 'node' in data['metric']:
                        node = data['metric']['node']
                        value = float(data['value'][1])
                        if node not in results:
                            results[node] = {}
                        results[node][key] = value

    # Calculate available CPU per node
    available_cpu_results = []
    for node, metrics in results.items():
        total_cpu = metrics.get('cpu_total_num', 0)
        used_cpu = metrics.get('cpu_used_num', 0)
        available_cpu = total_cpu - used_cpu
        available_cpu_results.append({
            'node': node,
            'available_cpu': available_cpu
        })

    return available_cpu_results

# result=get_cluster_cpu_index()
# print(result)