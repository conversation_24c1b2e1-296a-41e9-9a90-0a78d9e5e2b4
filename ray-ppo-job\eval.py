import os
import zipfile
# import gym
import gymnasium as gym
import ray
import torch
from actor import CartActor
from model_cnn import PPOA<PERSON><PERSON><PERSON>CNN


def get_latest_file(folder_path):
    try:
        files = [f for f in os.listdir(folder_path)]
        print(f"folder_path: {folder_path}, files: {files}")
        file_creation_times = {f: os.path.getctime(os.path.join(folder_path, f)) for f in files}
        latest_file = max(file_creation_times, key=file_creation_times.get)
        return os.path.join(folder_path, latest_file)
    except Exception as e:
        print(f"Error: {e}")
        return None

def main():
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    env_name = "ALE/Breakout-v5"
    env = gym.make("ALE/Breakout-v5")
    zipped_model_path = get_latest_file("results/checkpoints")
    extract_path = zipped_model_path.replace(".zip", "")
    
    with zipfile.ZipFile(zipped_model_path, 'r') as zip_ref:
        zip_ref.extractall(extract_path)
    
    model_path = extract_path + "/model.pth"
    print(f"Loading model from {model_path}")
    model = PPOActorCriticCNN(action_dim=env.action_space.n).to(device)
    model.load_state_dict(torch.load(model_path, map_location=device))
    # actor = CartActor(seed=1, env_name=env_name, horizon=1000, device=device, index=0, log_dir="./logs/eval", log_interval=1)
    num_actors = 1
    actor_cpu_num = 1
    actor_gpu_num = 0
    actor_mem_num = 1024
    horizon = 1000
    log_dir = "./logs/eval"
    log_interval = 1
    actors = [CartActor.options(num_cpus=actor_cpu_num, num_gpus=actor_gpu_num, memory=actor_mem_num).remote(
        seed=1, env_name=env_name,  horizon=horizon, device=device, index=index, log_dir=log_dir, log_interval=log_interval) for index in range(num_actors)]
    # actor.eval(model, 1)
    actor_tasks = [actor.eval.remote(model, 1) for actor in actors]
    ray.get(actor_tasks)

if __name__ == '__main__':
    main()